# CA App

## Summary
- [CA – Docker Setup](#ca-project--docker-setup)
- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
    - [Clone the Repository](#clone-the-repository)
    - [Copy Your .env File](#copy-your-env-file)
    - [Build and Start Containers](#build-and-start-containers)
    - [Access the Application Container](#access-the-application-container)
    - [Install PHP Dependencies (Composer)](#install-php-dependencies-composer)
    - [Set Up Storage Directories and Database](#set-up-storage-directories-and-database)
    - [Log in to the Application](#log-in-to-the-application)


- [Hosts Entries (Optional)](#hosts-entries-optional)
- [Additional Commands](#additional-commands)
    - [Webpack](#webpack)
    - [Copy Shared Files](#copy-shared-files)
    - [Compile Resource SASS](#compile-resource-sass)
 

- [Using Parallels Instead of Docker (Optional Legacy Info)](#using-parallels-instead-of-docker-optional-legacy-info)
- [Troubleshooting](#troubleshooting)
- [References](#references)

---

## CA Project – Docker Setup

This repository contains the CA Project (sometimes referred to as `fxlratr_app`) and its associated services, including a MariaDB database and Redis. Below you’ll find instructions on how to build and run the application using Docker and Docker Compose.

## Prerequisites

- **Docker** (version 27+ recommended)
- **Docker Compose** (v2 or later)

> **Note:** If you are using an Apple Silicon/ARM-based machine or another ARM architecture, please see the [Migration Guide – ARM Architecture](https://www.notion.so/contractoraccelerator/Migration-Guide-ARM-architecture-136d2857f78e80a798d5ee6e2c04024a) for additional steps or considerations.

---

## Quick Start

### Clone the Repository

```bash
git clone https://bitbucket.org/foundationaccelerator/site-foundation-accelerator/src/master/
cd site-foundation-accelerator
```

### Copy Your `.env` File

- If one does not exist yet, copy from `.env.example`.
- Fill out or adjust variables according to your local environment or ask any other developer.
- Ensure your database connection details match the `mariadb` container’s credentials (see `docker-compose.yml`).

### Build and Start Containers

From the devops folder (where `docker-compose.yml` is located):

```bash
docker compose build
docker compose up
```

To run in detached mode (this will not block the terminal), use:

```bash
docker compose up -d
```

This command will build and run three services:

- `fxlratr_app` (the main PHP application)
- `mariadb` (the MariaDB 10.11 database)
- `redis` (the Redis server)

### Access the Application Container

Once the containers are running, you can access the main application container using:

```bash
docker exec -it devops-fxlratr_app-1 /bin/bash
```

### Install PHP Dependencies (Composer)

Inside the container and on the main project directory where composer.json exists:

```bash
composer install
```

### Set Up Storage Directories and Database

```bash
# Make sure your .env is properly configured before running these
php console environment:setup
php console seed:company --create-role-users --create-admin
```

- `environment:setup` will create any required databases, storage directories, and seed the database with base data.
- `seed:company` will create default users, roles, and a company entry.

### Log in to the Application

By default, an admin user is created with the following credentials:

- **Email:** `primary@company<company #>.com` (e.g., `<EMAIL>`)
- **Password:** `cxlratr-password`

> Adjust these if your `.env` or seeds are configured differently.

---

## Hosts Entries (Optional)

If your application is configured to run on custom domain names (e.g., `app.ca.test`, `app.ecplinq.test`, etc.), you may need to add entries to your `/etc/hosts` file.

```bash
sudo vi /etc/hosts
```

Append entries similar to the following:

```
127.0.0.1 app.ca.test fxlratr.test app.ecplinq.test chanceaccelerator.test ...
```

Save and exit.

---

## Additional Commands

### Webpack

```bash
yarn run build [--module=module1,module2] [--brand=brand1,brand2] [--watch] [--mode=production]
```

### Copy Shared Files

```bash
yarn run copy-files [--mode=production]
```

### Compile Resource SASS

```bash
yarn run compile-sass
```

---

## Using Parallels Instead of Docker (Optional Legacy Info)

If your workflow requires or prefers **Parallels** or you are transitioning from a Vagrant-based setup, you may install the `vagrant-parallels` plugin:

```bash
vagrant plugin install vagrant-parallels
```

For most developers, **Docker Compose** is now the primary and recommended approach.

---

## Troubleshooting

- **Database Access**: The default root password is `vagrant`. If you need to connect from outside the container, ensure your `.env` matches those credentials.
- **Redis**: The Redis container listens on port `6379`. Make sure no other service is using that port or adjust the mapping in `docker-compose.yml`.
- **Permissions**: If you receive permission-related errors when creating or modifying storage/cache directories, verify that Docker volume mounts and folder ownership are correctly set.
- **ARM Architecture**: If you are on Apple Silicon or any other ARM-based machine, please refer to the [Migration Guide – ARM Architecture](https://www.notion.so/contractoraccelerator/Migration-Guide-ARM-architecture-136d2857f78e80a798d5ee6e2c04024a) for additional details.

---

## References

- [Migration Guide – ARM Architecture](https://www.notion.so/contractoraccelerator/Migration-Guide-ARM-architecture-136d2857f78e80a798d5ee6e2c04024a)
- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
