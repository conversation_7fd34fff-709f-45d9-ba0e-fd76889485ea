<?php

declare(strict_types=1);

namespace Common\Components\Validation\Rules;

use Brick\Math\BigDecimal;
use Brick\Math\Exception\MathException;
use Brick\Math\RoundingMode;
use Core\Components\Validation\Attributes\RuleAttribute;

/**
 * Class NumberRule
 *
 * @package Common\Components\Validation\Rules
 */
class NumberRule extends \Core\Components\Validation\Rules\NumberRule
{
    /**
     * Get list of messages for rules
     *
     * @return string[]
     */
    protected function getMessages(): array
    {
        return array_merge(parent::getMessages(), [
            'currency' => '{label} is not in a valid format'
        ]);
    }

    /**
     * Filter which formats numeric value into normal currency format
     *
     * @param int|float|string $field
     * @return bool|string
     */
    #[RuleAttribute('currency')]
    public function currency(mixed &$field): bool|string
    {
        try {
            $field = (string) BigDecimal::of($field)->toScale(2, RoundingMode::HALF_DOWN);
            return true;
        } catch (MathException) {
            return 'currency';
        }
    }
}
