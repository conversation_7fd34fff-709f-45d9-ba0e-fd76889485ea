<?php

declare(strict_types=1);

namespace Common\Extensions;

use Common\Classes\DataProvider;
use Core\Interfaces\KernelInterface;

/**
 * Class DataProviderExtension
 *
 * @package Common\Extensions
 */
class DataProviderExtension
{
    /**
     * Load extension
     *
     * Register class with registry only if not in production environment
     *
     * @param KernelInterface $kernel
     */
    public function load(KernelInterface $kernel): void
    {
        if (SERVER_ROLE === 'PROD') {
            return;
        }
        $kernel->singleton(DataProvider::class, fn() => new DataProvider($kernel));
    }
}
