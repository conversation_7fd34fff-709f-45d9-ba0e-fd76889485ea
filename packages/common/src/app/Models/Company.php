<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Interfaces\DB\ScopeSearchInterface;

class Company extends HistoryEntityModel implements Interfaces\CompanyInterface, ScopeSearchInterface
{
    use Common\CompanyCommon;

    protected $table = 'companies';
    protected $primaryKey = 'companyID';
    protected $fillable = [
        'companyID', 'companyUUID', 'resellerID', 'successManagerID', 'subscriptionID', 'isSubscriptionLocked',
        'registrationID', 'status', 'signupStatus', 'trialAt', 'firstActiveAt', 'activeAt', 'suspendedAt', 'dormantAt',
        'trialExpiresAt', 'isSetupWizard', 'setupWizardStep', 'setupWizardCompletedAt', 'name', 'address', 'address2',
        'city', 'state', 'zip', 'billingAddress', 'billingAddress2', 'billingCity', 'billingState', 'billingZip',
        'website', 'logoFileID', 'color', 'colorHover', 'emailAddCustomer', 'emailAddCustomerLastUpdated',
        'emailSchedule', 'emailScheduleLastUpdated', 'scheduleEmailSendSales', 'emailSalesAppointmentReminder',
        'emailSalesAppointmentReminderLastUpdated', 'emailSalesAppointmentReminderSendFromSales', 'emailBidSent',
        'emailBidSentLastUpdated', 'bidEmailSendSales', 'emailInstallation', 'emailInstallationLastUpdated',
        'emailInstallationAppointmentReminder', 'emailInstallationAppointmentReminderLastUpdated', 'emailBidAccept',
        'emailBidAcceptLastUpdated', 'bidAcceptEmailSendSales', 'emailBidReject', 'emailBidRejectLastUpdated',
        'bidRejectEmailSendSales', 'emailFinalPacket', 'emailFinalPacketLastUpdated', 'emailInvoice',
        'emailInvoiceLastUpdated', 'coverLetter', 'coverLetterLastUpdated', 'emailFrom', 'emailReply',
        'defaultInvoices', 'invoiceSplitBidAcceptance', 'invoiceSplitProjectComplete', 'timezoneID', 'timezone',
        'daylightSavings', 'recentlyCompletedStatus', 'added', 'updated', 'customerProfileID', 'latitude', 'longitude',
        'isActive', 'smtpCredentialID', 'zendeskOrganizationID', 'createdAt', 'createdByUserID', 'updatedAt',
        'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
