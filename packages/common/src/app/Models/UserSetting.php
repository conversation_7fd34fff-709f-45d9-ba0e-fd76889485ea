<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class UserSetting extends HistoryEntityModel implements Interfaces\UserSettingInterface
{
    use Common\UserSettingCommon;

    protected $table = 'userSettings';
    protected $primaryKey = 'userSettingID';
    protected $fillable = [
        'userID', 'name', 'value', 'createdByUserID', 'updatedByUserID',
        'deletedAt', 'deletedByUserID'
    ];
}
