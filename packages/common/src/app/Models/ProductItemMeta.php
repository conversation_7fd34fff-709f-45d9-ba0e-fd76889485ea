<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class ProductItemMeta extends HistoryEntityModel implements Interfaces\ProductItemMetaInterface
{
    use Common\ProductItemMetaCommon;

    protected $table = 'productItemMeta';
    protected $primaryKey = 'productItemMetaID';
    protected $fillable = [
        'productItemMetaID', 'productItemID', 'name', 'valueType', 'value', 'createdByUserID',
        'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
