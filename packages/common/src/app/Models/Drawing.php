<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Interfaces\DB\ScopeSearchInterface;

class Drawing extends HistoryEntityModel implements Interfaces\DrawingInterface, ScopeSearchInterface
{
    use Common\DrawingCommon;

    protected $table = 'drawings';
    protected $primaryKey = 'drawingID';
    protected $fillable = [
        'drawingID', 'version', 'subversion', 'projectID', 'status', 'name', 'zoomLevel', 'gridUnit', 'showWallLengths',
        'config', 'imageFileID', 'repairPlanFileID', 'isRepairPlanValid', 'startedAt', 'lastModifiedAt', 'isLocked',
        'lockedAt', 'finalizedAt', 'finalizedByUserID', 'lockedByUserApiTokenID', 'createdByUserID', 'updatedByUserID',
        'deletedByUserID', 'deletedAt'
    ];
}
