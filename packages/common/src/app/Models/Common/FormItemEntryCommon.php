<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\FormItemEntryGroup;
use Common\Models\Interfaces\FormItemEntryInterface;
use Common\Traits\DB\UuidTrait;

trait FormItemEntryCommon
{
    use UuidTrait;

    protected $castMap = [
        'formType' => 'int',
        'isLocked' => 'bool',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function form()
    {
        return $this->morphTo('form', 'formType', 'formID');
    }

    public function groups()
    {
        return $this->hasMany(FormItemEntryGroup::class, 'formItemEntryID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->join('companyFormItems', function ($join) {
            $join->where("{$this->table}.formType", FormItemEntryInterface::FORM_TYPE_COMPANY)
                ->on('companyFormItems.companyFormItemID', '=', "{$this->table}.formID");
        })
        ->where('companyFormItems.companyID', $company);
    }
}
