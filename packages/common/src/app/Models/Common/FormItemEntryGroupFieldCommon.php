<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\FormItemEntry;
use Common\Models\FormItemEntryGroup;
use Common\Traits\DB\UuidTrait;

trait FormItemEntryGroupFieldCommon
{
    use UuidTrait;

    protected $castMap = [
        'fieldSource' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function group()
    {
        return $this->belongsTo(FormItemEntryGroup::class, 'formItemEntryGroupID', 'formItemEntryGroupID');
    }

    public function field()
    {
        return $this->morphTo('field', 'fieldSource', 'fieldID');
    }

    public function scopeWithFormItem($query)
    {
        return $query->join('formItemEntryGroups', 'formItemEntryGroups.formItemEntryGroupID', '=', "{$this->table}.formItemEntryGroupID")
            ->join('formItemEntries', 'formItemEntries.formItemEntryID', '=', 'formItemEntryGroups.formItemEntryID')
            ->join('companyFormItems', function ($join) {
                $join->where('formItemEntries.formType', FormItemEntry::FORM_TYPE_COMPANY)
                    ->on('companyFormItems.companyFormItemID', '=', 'formItemEntries.formID');
            });
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $this->scopeWithFormItem($query)->where('companyFormItems.companyID', $company);
    }
}
