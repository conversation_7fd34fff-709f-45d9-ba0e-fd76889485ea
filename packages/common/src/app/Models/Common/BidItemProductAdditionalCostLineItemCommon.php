<?php

namespace Common\Models\Common;

use Common\Models\AdditionalCost;
use Common\Models\BidItemProductLineItem;
use Common\Models\Company;
use Common\Models\ProductItemAdditionalCost;
use Common\Models\Unit;
use Common\Traits\DB\UuidTrait;

trait BidItemProductAdditionalCostLineItemCommon
{
    use UuidTrait;

    protected $castMap = [
        'order' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function productLineItem()
    {
        return $this->belongsTo(BidItemProductLineItem::class, 'bidItemProductLineItemID', 'bidItemProductLineItemID');
    }

    public function productItemAdditionalCost()
    {
        return $this->belongsTo(ProductItemAdditionalCost::class, 'productItemAdditionalCostID', 'productItemAdditionalCostID');
    }

    public function additionalCost()
    {
        return $this->belongsTo(AdditionalCost::class, 'additionalCostID', 'additionalCostID');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class, 'unitID', 'unitID');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy("{$this->table}.order", 'asc');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('user', 'user.userID', '=', "{$this->table}.createdByUserID");
        return $query->where('user.companyID', $company);
    }
}
