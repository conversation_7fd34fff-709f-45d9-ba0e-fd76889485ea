<?php

namespace Common\Models\Common;

use Common\Models\BidItem;
use Common\Models\Company;
use Common\Models\Customer;
use Common\Models\Drawing;
use Common\Models\Evaluation;
use Common\Models\MarketingType;
use Common\Models\ProjectCost;
use Common\Models\ProjectFile;
use Common\Models\ProjectEmail;
use Common\Models\ProjectNote;
use Common\Models\ProjectSchedule;
use Common\Models\ProjectType;
use Common\Models\Property;
use Common\Models\PunchTime;
use Common\Models\ResultType;
use Common\Models\Task;
use Common\Models\User;
use Common\Traits\DB\ScopeSearchTrait;
use Ramsey\Uuid\Uuid;

trait ProjectCommon
{
    use ScopeSearchTrait;

    protected $castMap = [
        'projectID' => 'int',
        'propertyID' => 'int',
        'customerID' => 'int',
        'projectSalesperson' => 'int',
        'isFinancingEnabled' => 'bool',
        'status' => 'int',
        'projectCompletedByID' => 'int',
        'projectCancelledByID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $dateMap = ['projectCompleted', 'completedAt', 'projectCancelled', 'cancelledAt'];
    protected $searchColumns = ['referenceID', 'projectDescription'];

    public function bidItems()
    {
        return $this->hasMany(BidItem::class, 'projectID');
    }

    public function cancelledByUser()
    {
        return $this->belongsTo(User::class, 'projectCancelledByID', 'userID');
    }

    public function completedByUser()
    {
        return $this->belongsTo(User::class, 'projectCompletedByID', 'userID');
    }

    public function contacts()
    {
        return $this->hasMany(ProjectEmail::class, 'projectID');
    }

    /** @todo add to cascading delete */
    public function costs()
    {
        return $this->hasMany(ProjectCost::class, 'projectID');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customerID', 'customerID');
    }

    public function drawings()
    {
        return $this->hasMany(Drawing::class, 'projectID');
    }

    public function emails()
    {
        return $this->hasMany(ProjectEmail::class, 'projectID');
    }

    public function evaluations()
    {
        return $this->hasMany(Evaluation::class, 'projectID');
    }

    public function events()
    {
        return $this->hasMany(ProjectSchedule::class, 'projectID');
    }

    public function files()
    {
        return $this->hasMany(ProjectFile::class, 'projectID');
    }

    public function projectType()
    {
        return $this->belongsTo(ProjectType::class, 'type', 'projectTypeID');
    }

    public function resultType()
    {
        return $this->belongsTo(ResultType::class, 'resultTypeID', 'resultTypeID');
    }

    public function marketingType()
    {
        return $this->belongsTo(MarketingType::class, 'referralMarketingTypeID', 'marketingTypeID');
    }

    public function secondaryMarketingType()
    {
        return $this->belongsTo(MarketingType::class, 'secondaryMarketingTypeID', 'marketingTypeID');
    }

    public function notes()
    {
        return $this->hasMany(ProjectNote::class, 'projectID');
    }

    public function property()
    {
        return $this->belongsTo(Property::class, 'propertyID', 'propertyID');
    }

    public function punchTimes()
    {
        return $this->hasMany(PunchTime::class, 'projectID');
    }

    public function salesperson()
    {
        return $this->belongsTo(User::class, 'projectSalesperson', 'userID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('property', 'property.propertyID', '=', "{$this->table}.propertyID")
            ->join('customer', 'customer.customerID', '=', 'property.customerID');
        return $query->where('customer.companyID', $company);
    }

    public function scopeOfUser($query, $user)
    {
        if (is_object($user) && $user instanceof User) {
            $user = $user->getKey();
        }
        $query->join('property', 'property.propertyID', '=', "{$this->table}.propertyID")
            ->join('customer', 'customer.customerID', '=', 'property.customerID');
        return $query->where(function ($query) use ($user) {
            $query->where("{$this->table}.projectSalesperson", $user)
                ->orWhere("{$this->table}.createdByUserID", $user);
        });
    }

    public function tasks()
    {
        return $this->morphMany(Task::class, 'tasks', 'associationType', 'associationID');
    }

    /**
     * Get project UUID as uppercase string without dashes
     *
     * @return string
     */
    public function getProjectUUIDString(): string
    {
        return strtoupper(str_replace('-', '', UUID::fromBytes($this->projectUUID)->toString()));
    }
}
