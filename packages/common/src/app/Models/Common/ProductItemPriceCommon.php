<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\ProductItem;
use Common\Traits\DB\UuidTrait;

trait ProductItemPriceCommon
{
    use UuidTrait;

    protected $castMap = [
        'status' => 'int',
        'adjustmentType' => 'int',
        'archivedByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function item()
    {
        return $this->belongsTo(ProductItem::class, 'productItemID', 'productItemID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->join('productItems', function ($join) use ($company) {
            $join->on('productItems.productItemID', '=', 'productItemPrices.productItemID')
                ->where('productItems.ownerType', ProductItem::OWNER_COMPANY)
                ->where('ownerID', $company);
        });
    }
}
