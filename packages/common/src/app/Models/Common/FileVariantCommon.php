<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\File;
use Common\Traits\DB\UuidTrait;

trait FileVariantCommon
{
    use UuidTrait;

    protected $castMap = [
        'type' => 'int',
        'status' => 'int',
        'size' => 'int',
        'data' => 'array',
        'isValid' => 'bool',
        'time' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function file()
    {
        return $this->belongsTo(File::class, 'fileID', 'fileID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('files', 'files.fileID', '=', "{$this->table}.fileID");
        $query->join('user', 'user.userID', '=', 'files.createdByUserID');
        return $query->where('user.companyID', $company);
    }
}
