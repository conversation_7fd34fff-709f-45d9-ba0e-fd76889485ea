<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormItemGroupTemplate extends Model implements Interfaces\FormItemGroupTemplateInterface
{
    use SoftDeletes;
    use UuidTrait;

    protected $table = 'formItemGroupTemplates';
    protected $primaryKey = 'formItemGroupTemplateID';
    protected $fillable = [
        'formItemGroupTemplateID', 'formItemGroupID', 'type', 'alias', 'styles', 'content', 'scripts', 'config',
        'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
    protected $casts = [
        'type' => 'int',
        'config' => 'array',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function group()
    {
        return $this->belongsTo(FormItemGroup::class, 'formItemGroupID', 'formItemGroupID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->join('formItemGroups', 'formItemGroups.formItemGroupID', "{$this->table}.formItemGroupID")
            ->join('formItems', 'formItems.formItemID', '=', 'formItemGroups.formItemID')
            ->leftJoin('companyFormItems', function ($join) {
                $join->where('formItems.ownerType', FormItem::OWNER_TYPE_COMPANY)
                    ->on('companyFormItems.companyFormItemID', '=', 'formItems.ownerID');
            })
            ->where(function ($query) use ($company) {
                $query->where('formItems.ownerType', FormItem::OWNER_TYPE_SYSTEM)
                    ->orWhere('companyFormItems.companyID', $company);
            });
    }

    public function scopeOfType($query, $type)
    {
        return $query->where("{$this->table}.type", $type);
    }
}
