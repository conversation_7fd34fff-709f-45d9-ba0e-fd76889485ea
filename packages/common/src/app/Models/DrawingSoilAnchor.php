<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DrawingSoilAnchor extends Model
{
    use SoftDeletes;

    protected $table = 'drawingSoilAnchors';
    protected $primaryKey = 'ID';
    protected $fillable = [
        'drawingID',
        'nodeID',
        'xPos',
        'yPos',
        'zRotation'
    ];
    protected $hidden = ['drawingID'];

    public function appDrawing()
    {
        return $this->belongsTo('AppDrawing', 'drawingID', 'drawingID');
    }
}
