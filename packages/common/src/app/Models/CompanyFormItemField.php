<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyFormItemField extends Model implements Interfaces\FormItemGroupFieldInterface
{
    use SoftDeletes;
    use UuidTrait;

    protected $table = 'companyFormItemFields';
    protected $primaryKey = 'companyFormItemFieldID';
    protected $fillable = [
        'companyFormItemFieldID', 'companyFormItemID', 'formItemGroupFieldID', 'type', 'label', 'displayLabel',
        'isRequired', 'tooltip', 'config', 'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
    protected $casts = [
        'type' => 'int',
        'isRequired' => 'bool',
        'config' => 'array',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function form()
    {
        return $this->belongsTo(CompanyFormItem::class, 'companyFormItemID', 'companyFormItemID');
    }

    public function formItemGroupField()
    {
        return $this->belongsTo(FormItemGroupField::class, 'formItemGroupID', 'formItemGroupID');
    }

    public function options()
    {
        return $this->hasMany(CompanyFormItemFieldOption::class, 'companyFormItemFieldID');
    }

    public function products()
    {
        return $this->hasMany(CompanyFormItemFieldProduct::class, 'companyFormItemFieldID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        $query->join('companyFormItems', 'companyFormItems.companyFormItemID', "{$this->table}.companyFormItemID");
        return $query->where('companyFormItems.companyID', $company);
    }
}
