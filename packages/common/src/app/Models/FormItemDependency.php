<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormItemDependency extends Model
{
    use SoftDeletes;
    use UuidTrait;

    public const TYPE_PRODUCT_ITEM = 1;

    protected $table = 'formItemDependencies';
    protected $primaryKey = 'formItemDependencyID';
    protected $fillable = [
        'formItemDependencyID', 'formItemID', 'alias', 'type', 'label', 'description', 'config', 'createdByUserID',
        'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
    protected $casts = [
        'type' => 'int',
        'config' => 'array',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public function form()
    {
        return $this->belongsTo(FormItem::class, 'formItemID', 'formItemID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->join('formItems', 'formItems.formItemID', '=', "{$this->table}.formItemID")
            ->leftJoin('companyFormItems', function ($join) {
                $join->where('formItems.ownerType', FormItem::OWNER_TYPE_COMPANY)
                    ->on('companyFormItems.companyFormItemID', '=', 'formItems.ownerID');
            })
            ->where(function ($query) use ($company) {
                $query->where('formItems.ownerType', FormItem::OWNER_TYPE_SYSTEM)
                    ->orWhere('companyFormItems.companyID', $company);
            });
    }
}
