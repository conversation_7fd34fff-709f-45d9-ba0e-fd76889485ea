<?php

namespace Common\Models\Interfaces;

/**
 * Interface MediaInterface
 *
 * @package Common\Models\Interfaces
 */
interface MediaInterface
{
    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    public function company();

    public function file();

    public function scopeActive($query);

    public function scopeOrdered($query);

    public function scopeOfCompany($query, $company);
}
