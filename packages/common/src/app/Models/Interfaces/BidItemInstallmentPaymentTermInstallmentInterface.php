<?php

declare(strict_types=1);

namespace Common\Models\Interfaces;

/**
 * Interface BidItemInstallmentPaymentTermInstallmentInterface
 *
 * @package Common\Models\Interfaces
 */
interface BidItemInstallmentPaymentTermInstallmentInterface
{
    public const AMOUNT_TYPE_TOTAL = 1;
    public const AMOUNT_TYPE_PERCENTAGE = 2;

    public const DUE_TIME_FRAME_AT_BID_ACCEPTANCE = 1;
    public const DUE_TIME_FRAME_AFTER_BID_ACCEPTANCE = 2;
    public const DUE_TIME_FRAME_BEFORE_PROJECT_START = 3;
    public const DUE_TIME_FRAME_AFTER_PROJECT_START = 4;
    public const DUE_TIME_FRAME_BEFORE_PROJECT_COMPLETION = 5;
    public const DUE_TIME_FRAME_AT_PROJECT_COMPLETION = 7;
    public const DUE_TIME_FRAME_AFTER_PROJECT_COMPLETION = 6;
    public const DUE_TIME_FRAME_AT_CLOSING = 8;

    public function installmentPaymentTerm();

    public function scopeOrdered($query);

    public function scopeOfCompany($query, $company);
}
