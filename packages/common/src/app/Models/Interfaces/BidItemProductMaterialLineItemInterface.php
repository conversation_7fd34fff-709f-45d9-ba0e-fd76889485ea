<?php

namespace Common\Models\Interfaces;

/**
 * Interface BidItemProductMaterialLineItemInterface
 *
 * @package Common\Models\Interfaces
 */
interface BidItemProductMaterialLineItemInterface
{
    public function productLineItem();

    public function productItemMaterial();

    public function material();

    public function unit();

    public function scopeOrdered($query);

    public function scopeOfCompany($query, $company);
}
