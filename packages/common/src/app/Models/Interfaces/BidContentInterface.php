<?php

namespace Common\Models\Interfaces;

/**
 * Interface BidContentInterface
 *
 * @package Common\Models\Interfaces
 */
interface BidContentInterface
{
    const TYPE_DISCLAIMER = 1;
    const TYPE_WAIVER = 2;
    const TYPE_WARRANTY = 3;
    const TYPE_CONTRACT = 4;
    const TYPE_ACKNOWLEDGEMENT = 5;
    const TYPE_INTRO = 6;

    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    public function createdByUser();

    public function company();

    public function productItems();

    public function scopeOrdered($query);

    public function scopeOfCompany($query, $company);
}
