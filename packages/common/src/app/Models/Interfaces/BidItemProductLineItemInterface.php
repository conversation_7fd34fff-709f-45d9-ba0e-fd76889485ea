<?php

namespace Common\Models\Interfaces;

/**
 * Interface BidItemProductLineItemInterface
 *
 * @package Common\Models\Interfaces
 */
interface BidItemProductLineItemInterface
{
    const ADJUSTMENT_MODE_PLUS = 1;
    const ADJUSTMENT_MODE_MINUS = 2;

    const ADJUSTMENT_TYPE_TOTAL = 1;
    const ADJUSTMENT_TYPE_PERCENTAGE = 2;

    public function lineItem();

    public function productItem();

    public function productItemPrice();

    public function unit();

    public function scopeOfCompany($query, $company);
}
