<?php

namespace Common\Models\Interfaces;

/**
 * Interface FileVariantInterface
 *
 * @package Common\Models\Interfaces
 */
interface FileVariantInterface
{
    const TYPE_DRAWING_APP_EVENT_THUMBNAIL = 1;
    const TYPE_DRAWING_EVALUATION_THUMBNAIL = 2;
    const TYPE_COMPANY_PROFILE_THUMBNAIL = 3;
    const TYPE_USER_PROFILE_THUMBNAIL = 4;
    const TYPE_COMPANY_EMAIL_THUMBNAIL = 5;
    const TYPE_COMPANY_DOCUMENT_THUMBNAIL = 6;
    const TYPE_USER_EMAIL_THUMBNAIL = 7;
    const TYPE_FORM_UPLOAD_FIELD_THUMBNAIL = 8;
    const TYPE_BID_CUSTOM_DRAWING_THUMBNAIL = 9;
    const TYPE_DRAWING_BID_THUMBNAIL = 10;
    const TYPE_PROPERTY_SIZE_MEDIUM = 11;
    const TYPE_PROJECT_FILE_THUMBNAIL = 12;
    const TYPE_USER_BID_THUMBNAIL = 13;
    const TYPE_COMPANY_DRAWING_THUMBNAIL = 14;

    const STATUS_IN_PROGRESS = 1;
    const STATUS_FINISHED = 2;
    const STATUS_FAILED = 3;

    public function file();

    public function scopeOfCompany($query, $company);
}
