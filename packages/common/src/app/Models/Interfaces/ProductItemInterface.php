<?php

namespace Common\Models\Interfaces;

/**
 * Interface ProductItemInterface
 *
 * @package Common\Models\Interfaces
 */
interface ProductItemInterface
{
    const OWNER_MANUFACTURER = 4;
    const OWNER_COMPANY = 5;

    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    const PRICING_TYPE_BASIC = 1;
    const PRICING_TYPE_COMPONENT = 2;

    public function bidContent();

    public function categories();

    public function materials();

    public function additionalCosts();

    public function meta();

    public function nonTieredPrice();

    public function owner();

    public function prices();

    public function unit();

    public function scopeActive($query);

    public function scopeOrdered($query);

    public function scopeOfCompany($query, $company);
}
