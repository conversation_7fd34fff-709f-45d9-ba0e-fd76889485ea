<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Interfaces\DB\ScopeSearchInterface;
use Common\Models\Common\ProjectFileCommon;

class ProjectFile extends HistoryEntityModel implements Interfaces\ProjectFileInterface, ScopeSearchInterface
{
    use ProjectFileCommon;

    protected $table = 'projectFiles';
    protected $primaryKey = 'projectFileID';
    protected $fillable = [
        'projectFileID', 'projectID', 'name', 'fileID', 'createdAt', 'createdByUserID', 'updatedAt', 'updatedByUserID',
        'deletedAt', 'deletedByUserID'
    ];
}
