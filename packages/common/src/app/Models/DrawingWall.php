<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DrawingWall extends Model
{
    use SoftDeletes;

    protected $table = 'drawingWalls';
    protected $primaryKey = 'ID';
    protected $fillable = [
        'drawingID',
        'nodeID',
        'x1',
        'y1',
        'x2',
        'y2',
        'lineColor'
    ];
    protected $hidden = ['drawingID'];

    public function appDrawing()
    {
        return $this->belongsTo('AppDrawing', 'drawingID', 'drawingID');
    }
}
