<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\BidItemPriceAdjustmentLineItemCommon;
use Common\Models\Interfaces\BidItemPriceAdjustmentLineItemInterface;

class BidItemPriceAdjustmentLineItemHistory extends HistoryModel implements BidItemPriceAdjustmentLineItemInterface
{
    use BidItemPriceAdjustmentLineItemCommon;

    protected $table = 'bidItemPriceAdjustmentLineItemsHistory';
    protected $primaryKey = 'bidItemPriceAdjustmentLineItemsHistoryID';
    protected $entityPrimaryKey = 'bidItemPriceAdjustmentLineItemID';
}
