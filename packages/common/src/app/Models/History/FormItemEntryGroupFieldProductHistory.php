<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\FormItemEntryGroupFieldProductCommon;
use Common\Models\Interfaces\FormItemEntryGroupFieldProductInterface;

class FormItemEntryGroupFieldProductHistory extends HistoryModel implements FormItemEntryGroupFieldProductInterface
{
    use FormItemEntryGroupFieldProductCommon;

    protected $table = 'formItemEntryGroupFieldProductsHistory';
    protected $primaryKey = 'formItemEntryGroupFieldProductsHistoryID';
    protected $entityPrimaryKey = 'formItemEntryGroupFieldProductID';
}
