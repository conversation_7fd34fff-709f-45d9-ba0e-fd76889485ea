<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Interfaces\DB\ScopeSearchInterface;
use Common\Models\Common\PropertyCommon;
use Common\Models\Interfaces\PropertyInterface;

class PropertyHistory extends HistoryModel implements PropertyInterface, ScopeSearchInterface
{
    use PropertyCommon;

    protected $table = 'propertyHistory';
    protected $primaryKey = 'propertyHistoryID';
    protected $entityPrimaryKey = 'propertyID';
}
