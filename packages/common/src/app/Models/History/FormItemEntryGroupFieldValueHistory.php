<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\FormItemEntryGroupFieldValueCommon;
use Common\Models\Interfaces\FormItemEntryGroupFieldValueInterface;

class FormItemEntryGroupFieldValueHistory extends HistoryModel implements FormItemEntryGroupFieldValueInterface
{
    use FormItemEntryGroupFieldValueCommon;

    protected $table = 'formItemEntryGroupFieldValuesHistory';
    protected $primaryKey = 'formItemEntryGroupFieldValuesHistoryID';
    protected $entityPrimaryKey = 'formItemEntryGroupFieldValueID';
}
