<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\ProductItemPriceCommon;
use Common\Models\Interfaces\ProductItemPriceInterface;

class ProductItemPriceHistory extends HistoryModel implements ProductItemPriceInterface
{
    use ProductItemPriceCommon;

    protected $table = 'productItemPricesHistory';
    protected $primaryKey = 'productItemPricesHistoryID';
    protected $entityPrimaryKey = 'productItemPriceID';
}
