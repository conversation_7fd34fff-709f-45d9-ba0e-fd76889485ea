<?php

namespace Common\Models\History\Pivots;

use Common\Classes\DB\HistoryPivot;
use Common\Models\Common\ProductCategoryItemCommon;
use Common\Models\Interfaces\ProductCategoryItemInterface;

class ProductCategoryItemHistory extends HistoryPivot implements ProductCategoryItemInterface
{
    use ProductCategoryItemCommon;

    protected $table = 'productCategoriesItemsHistory';
    protected $primaryKey = 'productCategoriesItemsHistoryID';
    protected $entityPrimaryKey = 'productCategoryItemID';
}
