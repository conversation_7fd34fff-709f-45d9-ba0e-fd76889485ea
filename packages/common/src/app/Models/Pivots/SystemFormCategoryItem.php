<?php

namespace Common\Models\Pivots;

use Common\Classes\DB\HistoryEntityPivot;
use Common\Models\Common\SystemFormCategoryItemCommon;
use Common\Models\Interfaces\SystemFormCategoryItemInterface;

class SystemFormCategoryItem extends HistoryEntityPivot implements SystemFormCategoryItemInterface
{
    use SystemFormCategoryItemCommon;

    protected $table = 'systemFormCategoriesItems';
    protected $primaryKey = 'systemFormCategoryItemID';
}
