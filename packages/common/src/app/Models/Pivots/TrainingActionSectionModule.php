<?php

namespace Common\Models\Pivots;

use Common\Classes\DB\Pivot;
use Illuminate\Database\Eloquent\SoftDeletes;

class TrainingActionSectionModule extends Pivot
{
    use SoftDeletes;

    protected $table = 'trainingActionsSectionModules';
    protected $primaryKey = 'trainingActionSectionModuleID';
    protected $fillable = [
        'trainingActionID', 'trainingSectionModuleID'
    ];
    protected $casts = [
        'trainingActionID' => 'int',
        'trainingSectionModuleID' => 'int'
    ];
}
