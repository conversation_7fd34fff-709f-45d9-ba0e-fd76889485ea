<?php

namespace Common\Models;

use Illuminate\Database\Eloquent\Model;

class CrewmanPhone extends Model
{
    protected $table = 'crewmanPhone';
    protected $primaryKey = 'crewmanPhoneID';
    protected $casts = [
        'crewmanID' => 'int',
        'isPrimary' => 'bool'
    ];

    public $timestamps = false;

    public function crewman()
    {
        return $this->belongsTo(Crewman::class, 'crewmanID', 'crewmanID');
    }
}
