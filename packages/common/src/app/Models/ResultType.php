<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Interfaces\DB\ScopeSearchInterface;
use Common\Models\Interfaces\ResultTypeInterface;

class ResultType extends HistoryEntityModel implements ResultTypeInterface, ScopeSearchInterface
{
    use Common\ResultTypeCommon;

    protected $table = 'resultTypes';
    protected $primaryKey = 'resultTypeID';
    protected $dates = ['createdAt', 'updatedAt'];

    protected $fillable = [
        'resultTypeID', 'companyID', 'type', 'name', 'status',
        'inactiveAt', 'inactiveByUserID', 'archivedAt','archivedByUserID',
        'createdAt', 'createdByUserID', 'updatedAt', 'updatedByUser<PERSON>', 'deletedAt', 'deletedByUserID'
    ];

}
