<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class CompanyInvoiceCredit extends HistoryEntityModel implements Interfaces\CompanyInvoiceCreditInterface
{
    use Common\CompanyInvoiceCreditCommon;

    protected $table = 'companyInvoiceCredits';
    protected $primaryKey = 'companyInvoiceCreditID';
    protected $fillable = [
        'companyID', 'type', 'description', 'amount', 'remainingAmount', 'isExpended', 'expendedAt',
        'companySubscriptionID', 'createdAt', 'createdByUserID', 'updatedAt', 'updatedByUserID', 'deletedAt',
        'deletedByUserID'
    ];
}
