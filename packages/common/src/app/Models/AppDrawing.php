<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AppDrawing extends Model
{
    use SoftDeletes;

    protected $table = 'appDrawing';
    protected $primaryKey = 'drawingID';

    public $incrementing = false;
    public $timestamps = false;

    public static function boot()
    {
        parent::boot();
        static::deleting(function (self $drawing) {
            $relations = [
                'compasses' => DrawingCompass::class,
                'customServices' => DrawingCustomService::class,
                'deadMans' => DrawingDeadMan::class,
                'floorCracks' => DrawingFloorCrack::class,
                'interiorDrains' => DrawingInteriorDrain::class,
                'interiorPiers' => DrawingInteriorPier::class,
                'mudjackings' => DrawingMudjacking::class,
                'notes' => DrawingNote::class,
                'piers' => DrawingPier::class,
                'polyFoams' => DrawingPolyFoam::class,
                'soilAnchors' => DrawingSoilAnchor::class,
                'sumpPumps' => DrawingSumpPump::class,
                'supportPosts' => DrawingSupportPost::class,
                'walls' => DrawingWall::class,
                'wallBraces' => DrawingWallBrace::class,
                'wallCracks' => DrawingWallCrack::class
            ];
            $delete_func = ($drawing->forceDeleting ? 'forceDelete' : 'delete');
            foreach ($relations as $relation => $entity) {
                $drawing->{$relation}()->{$delete_func}();
            }
        });
    }

    public function compasses()
    {
        return $this->hasMany(DrawingCompass::class, 'drawingID');
    }

    public function customServices()
    {
        return $this->hasMany(DrawingCustomService::class, 'drawingID');
    }

    public function deadMans()
    {
        return $this->hasMany(DrawingDeadMan::class, 'drawingID');
    }

    public function evaluation()
    {
        return $this->belongsTo(Evaluation::class, 'evaluationID', 'evaluationID');
    }

    public function floorCracks()
    {
        return $this->hasMany(DrawingFloorCrack::class, 'drawingID');
    }

    public function interiorDrains()
    {
        return $this->hasMany(DrawingInteriorDrain::class, 'drawingID');
    }

    public function interiorPiers()
    {
        return $this->hasMany(DrawingInteriorPier::class, 'drawingID');
    }

    public function mudjackings()
    {
        return $this->hasMany(DrawingMudjacking::class, 'drawingID');
    }

    public function notes()
    {
        return $this->hasMany(DrawingNote::class, 'drawingID');
    }

    public function piers()
    {
        return $this->hasMany(DrawingPier::class, 'drawingID');
    }

    public function polyFoams()
    {
        return $this->hasMany(DrawingPolyFoam::class, 'drawingID');
    }

    public function soilAnchors()
    {
        return $this->hasMany(DrawingSoilAnchor::class, 'drawingID');
    }

    public function sumpPumps()
    {
        return $this->hasMany(DrawingSumpPump::class, 'drawingID');
    }

    public function supportPosts()
    {
        return $this->hasMany(DrawingSupportPost::class, 'drawingID');
    }

    public function walls()
    {
        return $this->hasMany(DrawingWall::class, 'drawingID');
    }

    public function wallBraces()
    {
        return $this->hasMany(DrawingWallBrace::class, 'drawingID');
    }

    public function wallCracks()
    {
        return $this->hasMany(DrawingWallCrack::class, 'drawingID');
    }
}
