<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\QuiteSaveTrait;
use Common\Traits\DB\UuidTrait;

class TwilioWebhookLog extends Model
{
    use QuiteSaveTrait;
    use UuidTrait;

    public const TYPE_MESSAGE_STATUS = 1;
    public const TYPE_INCOMING_MESSAGE = 2;

    protected $connection = 'utility';

    protected $table = 'twilioWebhookLog';
    protected $primaryKey = 'twilioWebhookLogID';
    protected $fillable = [
        'twilioWebhookLogID', 'type', 'body', 'createdAt'
    ];
    protected $casts = [
        'body' => 'array'
    ];
    protected $dates = ['createdAt'];
    protected $dateFormat = 'Y-m-d H:i:s.u';

    public $timestamps = false;
}
