<?php

declare(strict_types=1);

namespace Common\DataSources;

use Common\Classes\DataProvider\Source;
use Common\Models\TextMessageService;
use Ramsey\Uuid\Uuid;

/**
 * Class TextMessageServiceSource
 *
 * @package Common\DataSources
 */
class TextMessageServiceSource extends Source
{
    /**
     * @var string|null Name of source
     */
    protected ?string $name = 'text_message_services';

    /**
     * Setup source by importing data into database
     *
     * @throws \Core\Exceptions\AppException
     */
    public function setup(): void
    {
        $services = $this->loadData();
        $alias_map = [];
        foreach ($services as $alias => $service) {
            $id = Uuid::uuid4();
            TextMessageService::create([
                'textMessageServiceID' => $id->getBytes(),
                'name' => $service['name'],
                'serviceID' => $service['service_id']
            ]);
            $alias_map[$alias] = $id;
        }
        $this->storeUuidAliasMap($alias_map);
    }
}
