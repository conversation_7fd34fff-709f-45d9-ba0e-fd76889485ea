<?php

declare(strict_types=1);

use App\Classes\Func;
use Core\Exceptions\AppException;

use const Common\Constants\PATH_DATA;

$defaults = [
    'egress_windows' => [
        'name' => 'Egress Windows',
        'categories' => ['basement-windows'],
        'description' => <<<TXT
            The Egress Windows form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'window_wells' => [
        'name' => 'Window Wells',
        'categories' => ['basement-windows'],
        'description' => <<<TXT
            The Window Wells form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'floor_cracks' => [
        'name' => 'Floor Cracks',
        'categories' => ['crack-repair'],
        'description' => <<<TXT
            The Floor Cracks form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'crack_repair' => [
        'name' => 'Crack Repair',
        'categories' => ['crack-repair'],
        'description' => <<<TXT
            The Crack Repair form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'wall_cracks' => [
        'name' => 'Wall Cracks',
        'categories' => ['crack-repair'],
        'description' => <<<TXT
            The Wall Cracks form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'encapsulation' => [
        'name' => 'Encapsulation',
        'categories' => ['crawlspace'],
        'description' => <<<TXT
            The Encapsulation form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'encapsulation_calculation' => [
        'name' => 'Encapsulation',
        'display_name' => 'Encapsulation (calculation)',
        'categories' => ['crawlspace'],
        'description' => <<<TXT
            The Encapsulation with calculation form contains the standard form template of a required product dropdown and quantity fields as well as a length and width field allowing the system to figure the total square feet. Additional areas for customer facing notes and photos are also included.
            TXT
    ],
    'dehumidifier' => [
        'name' => 'Dehumidifier',
        'categories' => ['crawlspace'],
        'description' => <<<TXT
            The Dehumidifier form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'chain_link_fence' => [
        'name' => 'Chain Link Fence',
        'categories' => ['fencing'],
        'description' => <<<TXT
            The Chain Link Fence form contains separate required product dropdowns and quantities for the fencing and gates as well as optional areas for customer facing notes and photos.
            TXT
    ],
    'ornamental_fence' => [
        'name' => 'Ornamental Fence',
        'categories' => ['fencing'],
        'description' => <<<TXT
            The Ornamental Fence form contains separate required product dropdowns and quantities for the fencing and gates as well as optional areas for customer facing notes and photos.
            TXT
    ],
    'vinyl_fence' => [
        'name' => 'Vinyl Fence',
        'categories' => ['fencing'],
        'description' => <<<TXT
            The Vinyl Fence form contains separate required product dropdowns and quantities for the fencing and gates as well as optional areas for customer facing notes and photos.
            TXT
    ],
    'wood_fence' => [
        'name' => 'Wood Fence',
        'categories' => ['fencing'],
        'description' => <<<TXT
            The Wood Fence form contains separate required product dropdowns and quantities for the fencing and gates as well as optional areas for customer facing notes and photos.
            TXT
    ],
    'concrete' => [
        'name' => 'Concrete',
        'categories' => ['flatwork'],
        'description' => <<<TXT
            The Concrete form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'mudjacking' => [
        'name' => 'Mudjacking',
        'categories' => ['flatwork'],
        'description' => <<<TXT
            The Mudjacking form contains the standard form template of a required location, product dropdown, and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'polyurethane_foam' => [
        'name' => 'Polyurethane Foam',
        'categories' => ['flatwork'],
        'description' => <<<TXT
            The Polyurethane Foam form contains the standard form template of a required location, product dropdown, and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'polyurethane_foam_calculation' => [
        'name' => 'Polyurethane Foam',
        'display_name' => 'Polyurethane Foam (calculation)',
        'categories' => ['flatwork'],
        'description' => <<<TXT
            The Polyurethane Foam with calculation form contains a product dropdown in addition to a length, width, height, density, and overage fields to calculate the pounds of polyfoam needed to lift the slab. Additional areas for customer facing notes and photos are also included.
            TXT
    ],
    'custom_services' => [
        'name' => 'Custom Services',
        'categories' => ['general'],
        'description' => <<<TXT
            The Custom Service form contains two open text fields to type the description and price of the services as well the optional areas for customer facing notes and photos.
            TXT
    ],
    'field_notes' => [
        'name' => 'Field Notes',
        'categories' => ['general'],
        'description' => <<<TXT
            The Field Notes form includes a place to provide notes for the installation team. The form will be for internal use only and not show on the bid for the customer.
            TXT,
        'settings' => [
            'is_hidden_from_bid' => true
        ]
    ],
    'obstructions' => [
        'name' => 'Obstructions',
        'categories' => ['general'],
        'description' => <<<TXT
            The Obstructions form contains a place to document objects hindering installation. The item can be assigned to either the owner or contractor and a price can be added to document the cost for contractor removal.
            TXT
    ],
    'other' => [
        'name' => 'Other',
        'categories' => ['general'],
        'description' => <<<TXT
            Don't see what you need within the available forms? Rename this form for any other services not included in marketplace. The form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'sample' => [
        'name' => 'Sample',
        'categories' => ['general'],
        'description' => <<<TXT
            Don't see what you need within the available forms? Check out the "Other" form <NAME_EMAIL> to request a customized form specific to your company. The Sample form is intended to be an example of all the available field types. This form contains a dropdown, open text box, radio buttons, checkboxes, internal notes, external notes and more. When requesting a custom form, please specify what field types are needed.
            TXT
    ],
    'scope_of_work' => [
        'name' => 'Scope of Work',
        'categories' => ['general'],
        'description' => <<<TXT
            The Scope of Work form includes a place to provide overall notes and photos for the customer.
            TXT,
        'settings' => [
            'is_form_name_hidden' => true
        ]
    ],
    'scope_of_work_with_default' => [
        'name' => 'Scope of Work',
        'display_name' => 'Scope of Work (default content)',
        'categories' => ['general'],
        'description' => <<<TXT
            The Scope of Work form includes a place to provide overall notes and photos for the customer. The form also provides default content that can be autofilled into your form and adjusted per bid.
            TXT,
        'settings' => [
            'is_form_name_hidden' => true
        ]
    ],
    'interior_paint' => [
        'name' => 'Interior Paint',
        'categories' => ['painting'],
        'description' => <<<TXT
            The Interior Paint form contains the standard form template of a required product dropdown and quantity field as well as a dropdown of areas and note fields to include details of colors, sheen, etc. The form also includes a field to include the total number of days to accurately calculate labor costs. Additional areas for customer facing notes and photos are also included.
            TXT
    ],
    'exterior_paint' => [
        'name' => 'Exterior Paint',
        'categories' => ['painting'],
        'description' => <<<TXT
            The Exterior Paint form contains the standard form template of a required product dropdown and quantity field as well as a dropdown of areas and note fields to include details of colors, sheen, etc. The form also includes a field to include the total number of days to accurately calculate labor costs. Additional areas for customer facing notes and photos are also included.
            TXT
    ],
    'existing_piers' => [
        'name' => 'Existing Piers',
        'categories' => ['piering'],
        'structure' => 'notes',
        'description' => <<<TXT
            The Existing Piers form includes a place to provide notes regarding existing piers.
            TXT
    ],
    'grout_under_footings' => [
        'name' => 'Grout Under Footings',
        'categories' => ['piering'],
        'description' => <<<TXT
            The Grout Under Footings form contains a required field for unit quantity and optional space for customer facing notes and photos.
            TXT
    ],
    'pier_installation' => [
        'name' => 'Pier Installation',
        'categories' => ['piering'],
        'description' => <<<TXT
            The Pier Installation form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'radon_testing' => [
        'name' => 'Radon Testing',
        'categories' => ['radon'],
        'description' => <<<TXT
            The Radon Testing form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'radon_mitigation' => [
        'name' => 'Radon Mitigation',
        'categories' => ['radon'],
        'description' => <<<TXT
            The Radon Mitigation form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'beam_pockets_replacement' => [
        'name' => 'Beam Pockets Replacement',
        'categories' => ['structural-repair'],
        'description' => <<<TXT
            The Beam Pockets Replacement form contains a required field for unit quantity and optional space for customer facing notes and photos.
            TXT
    ],
    'carbon_fiber' => [
        'name' => 'Carbon Fiber',
        'categories' => ['structural-repair'],
        'description' => <<<TXT
            The Carbon Fiber form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos. The form also includes an internal field to collect the floor to joist measurement.
            TXT
    ],
    'excavation' => [
        'name' => 'Excavation',
        'categories' => ['structural-repair'],
        'description' => <<<TXT
            The Excavation form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'excavation_advanced' => [
        'name' => 'Excavation',
        'display_name' => 'Excavation (advanced)',
        'categories' => ['structural-repair'],
        'description' => <<<TXT
            The advanced Excavation form contains a place to include the type of excavation and excavation length, width, and depth to accurately calculate costs for excavation. The form also includes a dropdown to select the tile drain and membrane products and a field for gravel backfill height. The form will calculate the total cubic yards for gravel backfill and excess soil from excavation in cubic yards. Additional areas for customer facing notes and photos are also included.
            TXT
    ],
    'previous_structural_repair' => [
        'name' => 'Previous Structural Repair',
        'categories' => ['structural-repair'],
        'structure' => 'notes',
        'description' => <<<TXT
            The Previous Structural Repair form includes a place to provide notes regarding previous work.
            TXT
    ],
    'support_posts' => [
        'name' => 'Support Posts',
        'categories' => ['structural-repair'],
        'description' => <<<TXT
            The Support Posts form contains separate required product dropdowns and quantities for new posts and footings and optional areas for customer facing notes and photos. The form also includes a field to add the number of posts to adjust.
            TXT
    ],
    'wall_anchors' => [
        'name' => 'Wall Anchors',
        'categories' => ['structural-repair'],
        'description' => <<<TXT
            The Wall Anchors form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'wall_braces' => [
        'name' => 'Wall Braces',
        'categories' => ['structural-repair'],
        'description' => <<<TXT
            The Wall Brace form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos. The form also includes an internal field to collect the floor to joist measurement.
            TXT
    ],
    'curtain_drains' => [
        'name' => 'Curtain Drains',
        'categories' => ['water-management'],
        'description' => <<<TXT
            The Curtain Drains form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'drain_fixtures' => [
        'name' => 'Drain Fixtures',
        'categories' => ['water-management'],
        'description' => <<<TXT
            The Drain Fixtures form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'exterior_grading' => [
        'name' => 'Exterior Grading',
        'categories' => ['water-management'],
        'description' => <<<TXT
            The Exterior Grading form contains a length, width, and height field to calculate the total yards of dirt needed to add grading. Additional areas for customer facing notes and photos are also included.
            TXT
    ],
    'french_drains' => [
        'name' => 'French Drains',
        'categories' => ['water-management'],
        'description' => <<<TXT
            The French Drains form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'gutter_discharges' => [
        'name' => 'Gutter Discharges',
        'categories' => ['water-management'],
        'description' => <<<TXT
            The Gutter Discharges form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'interior_drain_systems' => [
        'name' => 'Interior Drain Systems',
        'categories' => ['water-management'],
        'description' => <<<TXT
            The Interior Drain Systems form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'sump_pump' => [
        'name' => 'Sump Pump',
        'categories' => ['water-management'],
        'description' => <<<TXT
            The Sump Pump form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ],
    'sump_pump_basin' => [
        'name' => 'Sump Pump with Basin',
        'categories' => ['water-management'],
        'description' => <<<TXT
            The Sump Pump with Basin form contains separate required product dropdowns for the sump pump and basin as well as optional areas for customer facing notes and photos.
            TXT
    ],
    'window_well_drains' => [
        'name' => 'Window Well Drains',
        'categories' => ['water-management'],
        'description' => <<<TXT
            The Window Well Drains form contains the standard form template of a required product dropdown and quantity field as well as the optional areas for customer facing notes and photos.
            TXT
    ]
];

$forms = [];
foreach ($defaults as $name => $form_info) {
    $form = [
        'name' => $form_info['name'],
        'display_name' => $form_info['display_name'] ?? null,
        'alias' => Func::createAlias($name),
        'structure' => isset($form_info['structure']) ? $form_info['structure'] : $name,
        'categories' => $form_info['categories'],
        'description' => $form_info['description'],
        'settings' => $form_info['settings'] ?? null
    ];
    $image_path = PATH_DATA . "system-form-items/images/{$name}.jpg";
    $new_path = Func::createTempFile(null, false);
    if (!copy($image_path, $new_path)) {
        throw new AppException('Unable to copy file: %s', $image_path);
    }
    $form['image_path'] = $new_path;
    $forms[] = $form;
}
return $forms;
