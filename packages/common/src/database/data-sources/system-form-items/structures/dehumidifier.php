<?php

declare(strict_types=1);

use Common\Classes\FormHelpers;

return FormHelpers::repeatableTable([
    'table-alias' => 'product-table',
    'template' => [
        'label' => 'Dehumidifier Service Description',
        'description' => 'Add an explanation of your dehumidifier installation service to be automatically pulled into the customer-facing Bid Document anytime this form is used.'
    ],
    'product_category' => [
        'label' => 'Dehumidifier Product Category',
        'description' => 'Choose or create the dehumidifier product category. The category will be used to attach and organize your products within the form dropdown.',
        'default_name' => 'Dehumidifier',
        'create_instruction' => 'Create the Dehumidifier product category needed for the form.'
    ]
]);
