<?php

declare(strict_types=1);

use App\Services\Form\Classes\Structure\Group\Rule\Condition;
use App\Services\Form\Components\Fields\{
    FileField,
    ProductListField,
    TextareaField,
    TextField
};
use App\Services\Form\Components\Groups\{DefaultGroup, RepeatableTableGroup};
use App\Services\Form\Components\LayoutItems\{FieldLayoutItem, GroupLayoutItem, TemplateLayoutItem};
use App\Services\Form\Components\Layouts\{
    Grid\InputScreenLargeLayout,
    Grid\OutputBidDocumentLayout,
    Grid\OutputJobDocumentLayout,
    TableRow\InputTableRowLayout
};
use App\Services\Form\Components\Overrides\{FieldOverride, TemplateOverride};
use App\Services\Form\Components\RuleConditionGroups\AllRuleConditionGroup;
use App\Services\Form\Components\RuleConditionSideValues\{FieldSideValue, NumericSideValue};
use App\Services\Form\Components\RuleEvents\{
    LineItemAdd\ProductLineItemAddRuleEvent,
    LineItemRemoveRuleEvent
};
use App\Services\Form\Components\Rules\PresentationRule;
use App\Services\Form\Components\Structures\BidStructure;
use App\Services\Form\Components\Templates\ServerTemplate;

return BidStructure::make()->withGroups(function (BidStructure $structure) {
    DefaultGroup::make($structure, 'main')
        ->withFields(function (DefaultGroup $group) {
            TextareaField::make($group, 'notes')->setLabel('Notes');
            FileField::make($group, 'photos')->setLabel('Photos')->setAllowedTypesImages()->setMaxSize('10mb');
        })
        ->withGroups(function (DefaultGroup $group, BidStructure $structure) {
            RepeatableTableGroup::make($structure, 'product-table', $group)
                ->withFields(function (RepeatableTableGroup $group) {
                    TextField::make($group, 'location')->setLabel('Location')->setIsRequired();
                    ProductListField::make($group, 'product')->setLabel('Product')->setIsRequired()
                        ->setDefaultProductCategoryName('Mudjacking')
                        ->setProductCategoryCreateInstruction('Create the Mudjacking product category needed for the form.')
                        ->requireOverride(function (FieldOverride $override) {
                        $override->setLabel('Mudjacking Product Category')
                            ->setDescription('Choose or create the mudjacking product category. The category will be used to attach and organize your products within the form dropdown.');
                    });
                    TextField::make($group, 'quantity')->setLabel('Quantity')->setDisplayMaskDecimal(2)->setIsRequired();
                })
                ->withLayouts(function (RepeatableTableGroup $group) {
                    InputTableRowLayout::make($group)->withItems(function (InputTableRowLayout $layout) {
                        FieldLayoutItem::make($layout, 'location')->setSize( 4);
                        FieldLayoutItem::make($layout, 'product')->setSize( 4);
                        FieldLayoutItem::make($layout, 'quantity')->setSize( 4);
                    });
                })
                ->withRules(function (RepeatableTableGroup $group) {
                    PresentationRule::make($group)
                        ->withConditions(function (PresentationRule $rule) {
                            AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                                Condition::make($group)->setLeft(FieldSideValue::make('location'))->notEmpty();
                                Condition::make($group)->setLeft(FieldSideValue::make('product'))->notEmpty();
                                Condition::make($group)->setLeft(FieldSideValue::make('quantity'))->notEmpty();
                                Condition::make($group)->setLeft(FieldSideValue::make('quantity'))->greaterThan(NumericSideValue::make(0));
                            });
                        })
                        ->withPassEvents(function (PresentationRule $rule) {
                            ProductLineItemAddRuleEvent::make($rule, 'product-quantity')->setProductField('product')->setQuantityField('quantity');
                        })
                        ->withFailEvents(function (PresentationRule $rule) {
                            LineItemRemoveRuleEvent::make($rule)->setEvent('product-quantity');
                        });
                });
        })
        ->withTemplates(function (DefaultGroup $group) {
            ServerTemplate::make($group, 'description')->requireOverride(function (TemplateOverride $override) {
                $override->setLabel('Mudjacking Service Description')
                    ->setDescription('Add an explanation of your mudjacking service to be automatically pulled into the customer-facing Bid Document anytime this form is used.');
            });
        })
        ->withLayouts(function (DefaultGroup $group) {
            InputScreenLargeLayout::make($group)->withItems(function (InputScreenLargeLayout $layout) {
                GroupLayoutItem::make($layout, 'product-table')->setSize(12);
                FieldLayoutItem::make($layout, 'notes')->setSize( 8);
                FieldLayoutItem::make($layout, 'photos')->setSize( 4);
            });
            OutputBidDocumentLayout::make($group)->withItems(function (OutputBidDocumentLayout $layout) {
                TemplateLayoutItem::make($layout, 'description')->setSize(12);
                GroupLayoutItem::make($layout, 'product-table')->setSize(12);
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
            OutputJobDocumentLayout::make($group)->withItems(function (OutputJobDocumentLayout $layout) {
                GroupLayoutItem::make($layout, 'product-table')->setSize(12);
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
        });
});
