<?php

declare(strict_types=1);

use Common\Classes\FormHelpers;

return FormHelpers::repeatableTable([
    'table-alias' => 'product-table',
    'template' => [
        'label' => 'Radon Testing Service Description',
        'description' => 'Add an explanation of your radon testing service to be automatically pulled into the customer-facing Bid Document anytime this form is used.'
    ],
    'product_category' => [
        'label' => 'Radon Testing Product Category',
        'description' => 'Choose or create the radon testing product category. The category will be used to attach and organize your products within the form dropdown.',
        'default_name' => 'Radon Testing',
        'create_instruction' => 'Create the Radon Testing product category needed for the form.'
    ]
]);
