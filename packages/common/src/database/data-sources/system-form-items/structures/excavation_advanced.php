<?php

declare(strict_types=1);

use App\Services\Form\Classes\Structure\Group\Rule\Condition;
use App\Services\Form\Components\Dependencies\ProductItemDependency;
use App\Services\Form\Components\Overrides\FieldOverride;
use App\Services\Form\Components\Overrides\TemplateOverride;
use App\Services\Form\Components\RuleConditionGroups\AllRuleConditionGroup;
use App\Services\Form\Components\RuleConditionSideValues\ContextSideValue;
use App\Services\Form\Components\RuleConditionSideValues\FieldOptionSideValue;
use App\Services\Form\Components\RuleConditionSideValues\FieldSideValue;
use App\Services\Form\Components\RuleConditionSideValues\NumericSideValue;
use App\Services\Form\Components\RuleEvents\CalculateRuleEvent;
use App\Services\Form\Components\RuleEvents\LineItemAdd\ProductLineItemAddRuleEvent;
use App\Services\Form\Components\RuleEvents\LineItemRemoveRuleEvent;
use App\Services\Form\Components\Rules\PresentationRule;
use App\Services\Form\Components\Fields\{FileField, Option\RadioField, ProductListField, TextareaField, TextField};
use App\Services\Form\Components\Groups\DefaultGroup;
use App\Services\Form\Components\LayoutItems\{FieldLayoutItem, TemplateLayoutItem};
use App\Services\Form\Components\Layouts\{
    Grid\InputScreenLargeLayout,
    Grid\OutputBidDocumentLayout,
    Grid\OutputJobDocumentLayout
};
use App\Services\Form\Components\Structures\BidStructure;
use App\Services\Form\Components\Templates\ServerTemplate;

return BidStructure::make()->withGroups(function (BidStructure $structure) {
    DefaultGroup::make($structure, 'main')
        ->withFields(function (DefaultGroup $group) {
            RadioField::make($group, 'excavation-type')->setLabel('Type of Excavation')->setIsRequired()->setDisplayColumns(2)
                ->withOptions([
                    'with-equipment' => 'With Equipment',
                    'hand-dig' => 'Hand Dig'
                ]);
            TextField::make($group, 'length')->setLabel('Excavation Length (lf)')->setIsRequired()->setDisplayMaskDecimal(2);
            TextField::make($group, 'depth')->setLabel('Excavation Depth (feet)')->setIsRequired()->setDisplayMaskDecimal(2);
            TextField::make($group, 'straighten')->setLabel('Straighten Wall Length (lf)')->setDisplayMaskDecimal(2);
            ProductListField::make($group, 'tile-drain')->setLabel('Tile Drain')
                ->setDefaultProductCategoryName('Tile Drain')
                ->setProductCategoryCreateInstruction('Create the Tile Drain product category needed for the form.')
                ->requireOverride(function (FieldOverride $override) {
                $override->setLabel('Tile Drain Product Category')
                    ->setDescription('Choose or create the tile drain product category. The category will be used to attach and organize your products within the form dropdown.');
            });
            ProductListField::make($group, 'membrane')->setLabel('Membrane')
                ->setDefaultProductCategoryName('Membrane')
                ->setProductCategoryCreateInstruction('Create the Membrane product category needed for the form.')
                ->requireOverride(function (FieldOverride $override) {
                $override->setLabel('Membrane Product Category')
                    ->setDescription('Choose or create the membrane product category. The category will be used to attach and organize your products within the form dropdown.');
            });
            TextField::make($group, 'gravel-backfill')->setLabel('Gravel Backfill Height (feet)')->setDisplayMaskDecimal(2);
            TextField::make($group, 'gravel-backfill-total')->setLabel('Gravel Backfill (cubic yards)')->setDisplayDisabled();
            TextField::make($group, 'excess-soil')->setLabel('Excess Soil From Excavation (cubic yards)')->setDisplayDisabled();
            TextareaField::make($group, 'notes')->setLabel('Notes');
            FileField::make($group, 'photos')->setLabel('Photos')->setAllowedTypesImages()->setMaxSize('10mb');
        })
        ->withTemplates(function (DefaultGroup $group) {
            ServerTemplate::make($group, 'description')->requireOverride(function (TemplateOverride $override) {
                $override->setLabel('Excavation Service Description')
                    ->setDescription('Add an explanation of your excavation service to be automatically pulled into the customer-facing Bid Document anytime this form is used.');
            });
        })
        ->withLayouts(function (DefaultGroup $group) {
            InputScreenLargeLayout::make($group)->withItems(function (InputScreenLargeLayout $layout) {
                FieldLayoutItem::make($layout, 'excavation-type')->setSize(6)->setIsLastInRow();
                FieldLayoutItem::make($layout, 'length')->setSize( 4);
                FieldLayoutItem::make($layout, 'depth')->setSize( 4);
                FieldLayoutItem::make($layout, 'straighten')->setSize( 4);
                FieldLayoutItem::make($layout, 'tile-drain')->setSize( 4);
                FieldLayoutItem::make($layout, 'membrane')->setSize( 4);
                FieldLayoutItem::make($layout, 'gravel-backfill')->setSize( 4);
                FieldLayoutItem::make($layout, 'gravel-backfill-total')->setSize( 4);
                FieldLayoutItem::make($layout, 'excess-soil')->setSize( 4)->setIsLastInRow();
                FieldLayoutItem::make($layout, 'notes')->setSize( 8);
                FieldLayoutItem::make($layout, 'photos')->setSize( 4);
            });
            OutputBidDocumentLayout::make($group)->withItems(function (OutputBidDocumentLayout $layout) {
                TemplateLayoutItem::make($layout, 'description')->setSize(12);
                FieldLayoutItem::make($layout, 'excavation-type')->setSize(6)->setIsLastInRow();
                FieldLayoutItem::make($layout, 'length')->setSize( 4);
                FieldLayoutItem::make($layout, 'depth')->setSize( 4);
                FieldLayoutItem::make($layout, 'straighten')->setSize( 4);
                FieldLayoutItem::make($layout, 'tile-drain')->setSize( 4);
                FieldLayoutItem::make($layout, 'membrane')->setSize( 4);
                FieldLayoutItem::make($layout, 'gravel-backfill')->setSize( 4);
                FieldLayoutItem::make($layout, 'gravel-backfill-total')->setSize( 4);
                FieldLayoutItem::make($layout, 'excess-soil')->setSize( 4)->setIsLastInRow();
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
            OutputJobDocumentLayout::make($group)->withItems(function (OutputJobDocumentLayout $layout) {
                FieldLayoutItem::make($layout, 'excavation-type')->setSize(6)->setIsLastInRow();
                FieldLayoutItem::make($layout, 'length')->setSize( 4);
                FieldLayoutItem::make($layout, 'depth')->setSize( 4);
                FieldLayoutItem::make($layout, 'straighten')->setSize( 4);
                FieldLayoutItem::make($layout, 'tile-drain')->setSize( 4);
                FieldLayoutItem::make($layout, 'membrane')->setSize( 4);
                FieldLayoutItem::make($layout, 'gravel-backfill')->setSize( 4);
                FieldLayoutItem::make($layout, 'gravel-backfill-total')->setSize( 4);
                FieldLayoutItem::make($layout, 'excess-soil')->setSize( 4)->setIsLastInRow();
                FieldLayoutItem::make($layout, 'notes')->setSize(12);
            });
        })
        ->withRules(function (DefaultGroup $group) {
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('length'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('length'))->greaterThan(NumericSideValue::make(0));
                        Condition::make($group)->setLeft(FieldSideValue::make('depth'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('depth'))->greaterThan(NumericSideValue::make(0));
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    CalculateRuleEvent::make($rule, 'calculate-square-foot-pass')
                        ->setExpression('{{field:length|FLT}}*{{field:depth|FLT}}')
                        ->setResultContextKey('total-square-foot')
                        ->setScale(2);
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    CalculateRuleEvent::make($rule,'calculate-square-foot-fail')
                        ->setExpression('0')
                        ->setResultContextKey('total-square-foot');
                });
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('excavation-type'))->contains(FieldOptionSideValue::make('with-equipment'));
                        Condition::make($group)->setLeft(FieldSideValue::make('length'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('length'))->greaterThan(NumericSideValue::make(0));
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    ProductLineItemAddRuleEvent::make($rule, 'excavation-equipment')->setProduct('{{dependency:excavation-equipment}}')->setQuantityField('length');
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    LineItemRemoveRuleEvent::make($rule)->setEvent('excavation-equipment');
                });
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('excavation-type'))->contains(FieldOptionSideValue::make('hand-dig'));
                        Condition::make($group)->setLeft(ContextSideValue::make('total-square-foot'))->notEmpty();
                        Condition::make($group)->setLeft(ContextSideValue::make('total-square-foot'))->greaterThan(NumericSideValue::make(0));
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    ProductLineItemAddRuleEvent::make($rule, 'excavation-hand-dig')->setProduct('{{dependency:excavation-hand-dig}}')->setQuantity('{{total-square-foot}}');
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    LineItemRemoveRuleEvent::make($rule)->setEvent('excavation-hand-dig');
                });
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('straighten'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('straighten'))->greaterThan(NumericSideValue::make(0));
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    ProductLineItemAddRuleEvent::make($rule, 'straighten-wall')->setProduct('{{dependency:wall-straighten}}')->setQuantityField('straighten');
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    LineItemRemoveRuleEvent::make($rule)->setEvent('straighten-wall');
                });
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('tile-drain'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('length'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('length'))->greaterThan(NumericSideValue::make(0));
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    ProductLineItemAddRuleEvent::make($rule, 'add-tile-drain')->setProductField('tile-drain')->setQuantityField('length');
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    LineItemRemoveRuleEvent::make($rule)->setEvent('add-tile-drain');
                });
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('membrane'))->notEmpty();
                        Condition::make($group)->setLeft(ContextSideValue::make('total-square-foot'))->notEmpty();
                        Condition::make($group)->setLeft(ContextSideValue::make('total-square-foot'))->greaterThan(NumericSideValue::make(0));
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    ProductLineItemAddRuleEvent::make($rule, 'add-membrane')->setProductField('membrane')->setQuantity('{{total-square-foot}}');
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    LineItemRemoveRuleEvent::make($rule)->setEvent('add-membrane');
                });
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('gravel-backfill'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('gravel-backfill'))->greaterThan(NumericSideValue::make(0));
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    ProductLineItemAddRuleEvent::make($rule, 'add-gravel-backfill')->setProduct('{{dependency:gravel-backfill}}')->setQuantityField('gravel-backfill');
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    LineItemRemoveRuleEvent::make($rule)->setEvent('add-gravel-backfill');
                });
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('gravel-backfill'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('gravel-backfill'))->greaterThan(NumericSideValue::make(0));
                        Condition::make($group)->setLeft(FieldSideValue::make('length'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('length'))->greaterThan(NumericSideValue::make(0));
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    CalculateRuleEvent::make($rule, 'calculate-gravel-backfill-total-pass')
                        ->setExpression('(({{field:length|FLT}}*{{field:gravel-backfill|FLT}}*3)/27)*1.1')
                        ->setResultField('gravel-backfill-total')
                        ->setScale(2);
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    CalculateRuleEvent::make($rule, 'calculate-gravel-backfill-total-fail')
                        ->setExpression('0')
                        ->setResultField('gravel-backfill-total')
                        ->setScale(2);
                });
            PresentationRule::make($group)
                ->withConditions(function (PresentationRule $rule) {
                    AllRuleConditionGroup::make($rule)->with(function (AllRuleConditionGroup $group) {
                        Condition::make($group)->setLeft(FieldSideValue::make('gravel-backfill'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('gravel-backfill'))->greaterThan(NumericSideValue::make(0));
                        Condition::make($group)->setLeft(FieldSideValue::make('length'))->notEmpty();
                        Condition::make($group)->setLeft(FieldSideValue::make('length'))->greaterThan(NumericSideValue::make(0));
                    });
                })
                ->withPassEvents(function (PresentationRule $rule) {
                    CalculateRuleEvent::make($rule, 'calculate-excess-soil-pass')
                        ->setExpression('(((({{field:length|FLT}}*{{field:depth|FLT}})*3)/27)*1.20)-((({{field:length|FLT}}*{{field:gravel-backfill|FLT}}*3)/27)*1.1)')
                        ->setResultField('excess-soil')
                        ->setScale(2);
                })
                ->withFailEvents(function (PresentationRule $rule) {
                    CalculateRuleEvent::make($rule, 'calculate-excess-soil-fail')
                        ->setExpression('0')
                        ->setResultField('excess-soil')
                        ->setScale(2);
                });
        });
})
    ->withDependencies(function (BidStructure $structure) {
        ProductItemDependency::make($structure, 'excavation-equipment')->setLabel('Excavation (with Equipment)')
            ->setDescription('Choose or create the excavation with equipment product. This will be used to create the product line item on the bid.')
            ->setDefaultProductItemName('Excavation (with Equipment)')
            ->setProductItemCreateInstruction('Create the Excavation (with Equipment) product needed for the form.');
        ProductItemDependency::make($structure, 'excavation-hand-dig')->setLabel('Excavation (Hand Dig)')
            ->setDescription('Choose or create the excavation hand dig product. This will be used to create the product line item on the bid.')
            ->setDefaultProductItemName('Excavation (Hand Dig)')
            ->setProductItemCreateInstruction('Create the Excavation (Hand Dig) product needed for the form.');
        ProductItemDependency::make($structure, 'wall-straighten')->setLabel('Wall Straighten')
            ->setDescription('Choose or create the wall straighten product. This will be used to create the product line item on the bid.')
            ->setDefaultProductItemName('Wall Straighten')
            ->setProductItemCreateInstruction('Create the Wall Straighten product needed for the form.');
        ProductItemDependency::make($structure, 'gravel-backfill')->setLabel('Gravel Backfill')
            ->setDescription('Choose or create the gravel backfill product. This will be used to create the product line item on the bid.')
            ->setDefaultProductItemName('Gravel Backfill')
            ->setProductItemCreateInstruction('Create the Gravel Backfill product needed for the form.');
    });
