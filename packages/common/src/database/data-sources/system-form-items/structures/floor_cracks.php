<?php

declare(strict_types=1);

use Common\Classes\FormHelpers;

return FormHelpers::repeatableTable([
    'table-alias' => 'product-table',
    'template' => [
        'label' => 'Floor Cracks Service Description',
        'description' => 'Add an explanation of your floor cracks service to be automatically pulled into the customer-facing Bid Document anytime this form is used.'
    ],
    'product_category' => [
        'label' => 'Floor Cracks Product Category',
        'description' => 'Choose or create the floor cracks product category. The category will be used to attach and organize your products within the form dropdown.',
        'default_name' => 'Floor Cracks',
        'create_instruction' => 'Create the Floor Cracks product category needed for the form.'
    ]
]);
