<?php

declare(strict_types=1);

use Common\Classes\FormHelpers;

return FormHelpers::repeatableTable([
    'table-alias' => 'product-table',
    'template' => [
        'label' => 'French Drains Service Description',
        'description' => 'Add an explanation of your french drains installation service to be automatically pulled into the customer-facing Bid Document anytime this form is used.'
    ],
    'product_category' => [
        'label' => 'French Drains Product Category',
        'description' => 'Choose or create the french drains product category. The category will be used to attach and organize your products within the form dropdown.',
        'default_name' => 'French Drains',
        'create_instruction' => 'Create the French Drains product category needed for the form.'
    ]
]);
