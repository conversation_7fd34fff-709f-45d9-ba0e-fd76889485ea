<?php

namespace Core\Interfaces;

/**
 * Interface HttpApplicationInterface
 *
 * @package Core\Interfaces
 */
interface HttpApplicationInterface
{
    /**
     * Set application exception handler class
     *
     * @param string $class
     */
    public function setExceptionHandler(string $class): void;

    /**
     * Add callable to be run when application terminates
     *
     * @param callable $callable
     */
    public function onTerminate(callable $callable): void;

    /**
     * Runs any code at end of app execution
     */
    public function terminate(): void;

    /**
     * Run application
     */
    public function run(): void;
}
