<?php

declare(strict_types=1);

namespace Core\Classes\Exception;

use Core\Interfaces\HttpExceptionHandlerInterface;
use Throwable;

/**
 * Class HttpHandler
 *
 * @package Core\Classes\Exception
 */
class HttpHandler extends Hand<PERSON> implements HttpExceptionHandlerInterface
{
    /**
     * <PERSON>le exception by throwing 500 status code and rending exception (if debug is enabled)
     *
     * @param Throwable $exception
     */
    public function handleException(Throwable $exception): void
    {
        parent::handleException($exception);
        \http_response_code(500);
        $this->render($exception);
    }

    /**
     * Render an exception if debug is enabled
     *
     * @param Throwable $e
     */
    protected function render(Throwable $e): void
    {
        if (!$this->kernel->debugEnabled()) {
            echo <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Application Error</title>
    <style>
		body {
			font-family: Tahoma,Arial,sans-serif;
			font-size: 16px;
		}
		 .message {
		    width: 1000px;
		    padding: 14px;
		    margin: 20px auto 0;
		    background-color: #EEEEEE;
		    border: 1px #CCCCCC solid;
		 }
    </style>
</head>
<body>
    <div class="message">We are experiencing technical difficulties, please check back later.</div>
</body>
</html>
HTML;
            return;
        }
        echo <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>Application Error</title>
	<style type="text/css">
		body {
			font-family: Tahoma,Arial,sans-serif;
			font-size: 12px;
			margin: 10px;
		}
		.error {
			padding: 10px;
			margin-bottom: 10px;
			background-color: #EEEEEE;
			border: 1px #CCCCCC solid;
		}
		.error .part {
			margin-bottom: 10px;
		}
		.error .last {
			margin-bottom: 0;
		}
		.error .part .title {
			float: left;
			width: 75px;
			font-weight: bold;
		}
		.error .part .data {
			float: left;
		}
		pre {
			margin: 10px 0 0;
		}
		.clear {
			clear: both;
			height: 0;
			font-size: 0;
			line-height: 0;
		}
	</style>
</head>
<body>
	<div class="error">
		<div class="part">
			<div class="title">Message:</div>
			<div class="data">{$e->getMessage()}</div>
			<div class="clear"></div>
		</div>
		<div class="part">
			<div class="title">File:</div>
			<div class="data">{$e->getFile()}</div>
			<div class="clear"></div>
		</div>
		<div class="part">
			<div class="title">Line:</div>
			<div class="data">{$e->getLine()}</div>
			<div class="clear"></div>
		</div>
		<div class="part last">
			<div class="title">Backtrace:</div>
			<div class="clear"></div>
			<pre>{$this->getTraceAsString($e)}</pre>
		</div>
	</div>
</body>
</html>
HTML;
    }
}
