<?php

declare(strict_types=1);

namespace Core\Classes;

use Core\Exceptions\ConfigException;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use RecursiveRegexIterator;

/**
 * Class Config
 *
 * @package Core\Classes
 */
class Config
{
    /**
     * Config item array
     *
     * @var array
     */
    protected array $data = [];

    /**
     * Load all config data from files in path
     *
     * @param string $path
     * @param string $regex
     *
     * @throws ConfigException
     */
    public function loadFromPath(string $path, string $regex = '\.php$'): void
    {
        if (!\is_dir($path)) {
            throw new ConfigException('Path does not exist: %s', $path);
        }
        $directory = new RecursiveDirectoryIterator($path);
        if ($regex !== null) {
            $directory = new RecursiveRegexIterator($directory, "@{$regex}@");
        }
        $directory = new RecursiveIteratorIterator($directory);
        if (\iterator_count($directory) === 0) {
            throw new ConfigException('No config files loaded');
        }
        /** @var \SplFileInfo $file */
        foreach ($directory as $file) {
            $this->loadFromFile($file->getPathname());
        }
    }

    /**
     * Load config data from file
     *
     * @param string $file
     *
     * @throws ConfigException
     */
    public function loadFromFile(string $file): void
    {
        if (!\file_exists($file)) {
            throw new ConfigException('Unable to load file: %s', $file);
        }
        $data = include($file);
        if (!\is_array($data)) {
            throw new ConfigException('Included config file must return an array');
        }
        // get file name here
        $name = \basename($file);
        if (($pos = \strrpos($name, '.')) !== false) {
            $name = \substr($name, 0, $pos);
        }
        $name = \preg_replace('@[^a-z0-9_]+@', '_', $name);
        $name = \preg_replace('@[_]{2,}@', '_', $name);
        $this->merge([
            $name => $data
        ]);
    }

    /**
     * Merge data with current config items
     *
     * @param array $data
     */
    public function merge(array $data): void
    {
        $this->data = Arr::mergeRecursiveDistinct($this->data, $data);
    }

    /**
     * Get config item
     *
     * @param null|string $key
     * @param mixed $default
     * @return mixed
     */
    public function get(?string $key = null, mixed $default = null): mixed
    {
        return Arr::get($this->data, $key, $default);
    }

    /**
     * Set config item
     *
     * @param string $key
     * @param mixed $value
     */
    public function set(string $key, mixed $value): void
    {
        Arr::set($this->data, $key, $value);
    }
}
