<?php

declare(strict_types=1);

namespace Core\Components\Console;

use Core\Components\Console\Classes\{Command, Console, IO, Router};
use Core\Components\Console\Interfaces\{CommandInterface, ConsoleInterface, IOInterface, RouterInterface};

/**
 * Class Component
 *
 * @package Core\Components\Console
 */
class Component extends \Core\Classes\Component
{
    /**
     * Boot component
     *
     * Bind classes to kernel
     */
    public function boot(): void
    {
        // add io
        $this->kernel->singleton(IO::class, fn(): IO => IO::fromGlobals());
        $this->kernel->alias(IOInterface::class, IO::class);

        // add console
        $this->kernel->singleton(Console::class, fn(): Console => new Console($this->kernel->get(IOInterface::class)));
        $this->kernel->alias(ConsoleInterface::class, Console::class);

        // add command
        $this->kernel->singleton(Command::class, fn(): Command => new Command($this->kernel));
        $this->kernel->alias(CommandInterface::class, Command::class);

        // add router
        $this->kernel->singleton(Router::class, fn(): Router => new Router());
        $this->kernel->alias(RouterInterface::class, Router::class);
    }
}
