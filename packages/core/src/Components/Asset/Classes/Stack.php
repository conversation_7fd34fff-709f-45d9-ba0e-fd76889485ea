<?php

declare(strict_types=1);

namespace Core\Components\Asset\Classes;

/**
 * Class Stack
 *
 * @package Core\Components\Asset\Classes
 */
class Stack
{
    /**
     * Push types
     */
    public const PUSH_TOP = 1;
    public const PUSH_BOTTOM = 2;

    /**
     * @var Asset[] List of assets in stack
     */
    protected array $assets = [];

    /**
     * Add asset to bottom of stack
     *
     * @param Asset $asset
     */
    public function add(Asset $asset): void
    {
        $this->assets[] = $asset;
    }

    /**
     * Merge stack on top/bottom of this one
     *
     * @param Stack $stack
     * @param int $action
     */
    public function merge(Stack $stack, int $action = self::PUSH_BOTTOM): void
    {
        $array_1 = $this->assets;
        $array_2 = $stack->getAssets();
        if ($action === self::PUSH_TOP) {
            [$array_2, $array_1] = [$array_1, $array_2];
        }
        $this->assets = array_merge($array_1, $array_2);
    }

    /**
     * Get count of assets in stack
     *
     * @return int
     */
    public function count(): int
    {
        return count($this->assets);
    }

    /**
     * Get all assets in stack
     *
     * @return Asset[]
     */
    public function getAssets(): array
    {
        return $this->assets;
    }
}
