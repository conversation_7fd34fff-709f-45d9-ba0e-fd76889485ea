<?php

declare(strict_types=1);

namespace Core\Components\Asset\Assets\File;

use Closure;
use Core\Components\Asset\Assets\FileAsset;
use Core\Components\Asset\Interfaces\PreloadableAssetInterface;
use Core\Components\Asset\Traits\PreloadableAssetTrait;
use Core\Components\Http\Classes\Html;

/**
 * Class StyleAsset
 *
 * @package Core\Components\Asset\Assets\File
 */
class StyleAsset extends FileAsset implements PreloadableAssetInterface
{
    use PreloadableAssetTrait;

    /**
     * @var string|null Location of where asset should output
     */
    protected ?string $location = 'head_end';

    /**
     * Get path of asset
     *
     * If no extension is found, one is added.
     *
     * @param bool $raw
     * @return Closure|string|null
     */
    public function getPath(bool $raw = false): Closure|string|null
    {
        $path = parent::getPath($raw);
        if (!$raw && !$this->isUrl($path) && !str_ends_with($path, '.css')) {
            $path .= '.css';
        }
        return $path;
    }

    /**
     * Get type of asset
     *
     * @return string|null
     */
    public function getType(): ?string
    {
        return 'style';
    }

    /**
     * Get content type of asset
     *
     * @return string|null
     */
    public function getContentType(): ?string
    {
        return 'text/css';
    }

    /**
     * Render asset for view
     *
     * @return string
     * @throws \Core\Components\Asset\Exceptions\AssetException
     */
    public function render(): string
    {
        $attrs = [
            'rel' => 'stylesheet',
            'type' => $this->getContentType(),
            'href' => $this->getUri()
        ];
        return sprintf('<link%s>' . PHP_EOL, Html::buildAttributes($attrs));
    }
}
