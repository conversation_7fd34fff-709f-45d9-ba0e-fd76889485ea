<?php

declare(strict_types=1);

namespace Core\Components\Asset\Assets\File;

use Closure;
use Core\Classes\File;
use Core\Components\Asset\Assets\FileAsset;
use Core\Components\Asset\Classes\Manager;
use Core\Components\Asset\Exceptions\AssetException;
use Core\Components\Asset\Interfaces\PreloadableAssetInterface;
use Core\Components\Http\Classes\{Html, Http};

/**
 * Class PreloadAsset
 *
 * @package Core\Components\Asset\Assets\File
 */
class PreloadAsset extends FileAsset
{
    /**
     * Mapping of extension to their associated type for use in the 'as' attribute
     */
    protected const EXTENSION_TYPE_MAP = [
        'js' => 'script',
        'css' => 'style',
        'woff' => 'font',
        'woff2' => 'font'
    ];

    /**
     * @var string|null Type of asset (used in 'as' attribute of link tag)
     */
    protected ?string $type = null;

    /**
     * @var string|null Content type of asset
     */
    protected ?string $content_type = null;

    /**
     * Create new instance from existing asset
     *
     * @param Manager $manager
     * @param PreloadableAssetInterface $asset
     * @return static
     */
    public static function fromAsset(Manager $manager, PreloadableAssetInterface $asset): static
    {
        $preload_asset = new static($manager);
        $path = $asset->getPath(raw: true);
        if ($path instanceof Closure) {
            $path = function() use ($path, $manager, $asset): ?string {
                $new_path = $path();
                if ($new_path !== null && !str_contains($new_path, ':') && ($path_name = $manager->getAssetTypePath($asset::class)) !== null) {
                    $new_path = "{$path_name}:{$new_path}";
                }
                return $new_path;
            };
        } elseif (is_string($path) && !str_contains($path, ':') && ($path_name = $manager->getAssetTypePath($asset::class)) !== null) {
            $path = "{$path_name}:{$path}";
        }
        $preload_asset->path($path);
        $preload_asset->contentType($asset->getContentType());
        $preload_asset->type($asset->getType());
        return $preload_asset;
    }

    /**
     * Set content type of file
     *
     * @param string|null $content_type
     * @return $this
     */
    public function contentType(?string $content_type): self
    {
        $this->content_type = $content_type;
        return $this;
    }

    /**
     * Get content type of file
     *
     * @return string|null
     */
    public function getContentType(): ?string
    {
        return $this->content_type;
    }

    /**
     * Set asset type
     *
     * @param string|null $type
     * @return $this
     */
    public function type(?string $type): self
    {
        $this->type = $type;
        return $this;
    }

    /**
     * Get asset type
     *
     * @return string|null
     */
    public function getType(): ?string
    {
        return $this->type;
    }

    /**
     * Get location to output asset
     *
     * Forcefully set to head_end since preloaded assets will always live there.
     *
     * @return string|null
     */
    public function getLocation(): ?string
    {
        return 'head_end';
    }

    /**
     * Render asset for view
     *
     * @return string
     * @throws AssetException
     */
    public function render(): string
    {
        $type = $this->getType();
        $content_type = $this->getContentType();
        if ($type === null || $content_type === null) {
            $path = $this->getPath();
            if (($extension = File::getExtension($path)) !== '') {
                $type ??= self::EXTENSION_TYPE_MAP[$extension] ?? null;
                $content_type ??= Http::getMimeTypeByExtension($extension);
            }
        }
        $attrs = [
            'rel' => 'preload',
            'href' => $this->getUri(),
            'as' => $type ?? throw new AssetException('No type provided'),
            'type' => $content_type ?? throw new AssetException('No content type provided'),
            'crossorigin' => true
        ];
        return sprintf('<link%s>' . PHP_EOL, Html::buildAttributes($attrs));
    }
}
