<?php

declare(strict_types=1);

namespace Core\Components\Asset\Traits;

/**
 * Trait PreloadableAssetTrait
 *
 * @package Core\Components\Asset\Traits
 */
trait PreloadableAssetTrait
{
    /**
     * @var bool Determines if asset is preloaded
     */
    protected bool $preload = false;

    /**
     * @var string|null Content type of asset
     */
    protected ?string $content_type = null;

    /**
     * @var string|null Type of asset (used in 'as' attribute of link tag)
     */
    protected ?string $type = null;

    /**
     * Set if asset should be preloaded
     *
     * @param bool $preload
     * @return static
     */
    public function preload(bool $preload = true): static
    {
        $this->preload = $preload;
        return $this;
    }

    /**
     * Get if asset has been marked for preload
     *
     * @return bool
     */
    public function getPreload(): bool
    {
        return $this->preload;
    }

    /**
     * Get content type for asset
     *
     * @return string|null
     */
    public function getContentType(): ?string
    {
        return $this->content_type;
    }

    /**
     * Get type for asset
     *
     * @return string|null
     */
    public function getType(): ?string
    {
        return $this->type;
    }
}
