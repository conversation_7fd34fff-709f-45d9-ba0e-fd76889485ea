<?php

namespace Core\Components\Asset\Interfaces;

/**
 * Interface PreloadableAssetInterface
 *
 * @package Core\Components\Asset\Interfaces
 */
interface PreloadableAssetInterface
{
    /**
     * Set if asset should be preloaded
     *
     * @param bool $preload
     */
    public function preload(bool $preload = true);

    /**
     * Get if asset has been marked for preload
     *
     * @return bool
     */
    public function getPreload(): bool;

    /**
     * Get content type of asset
     *
     * @return string|null
     */
    public function getContentType(): ?string;

    /**
     * Get type of asset
     *
     * @return string|null
     */
    public function getType(): ?string;
}
