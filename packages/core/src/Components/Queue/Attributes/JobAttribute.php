<?php

declare(strict_types=1);

namespace Core\Components\Queue\Attributes;

use Attribute;
use Core\Components\Queue\Interfaces\JobAttributeInterface;
use Core\Components\Queue\Traits\JobAttributeTrait;

/**
 * Class JobAttribute
 *
 * @package Core\Components\Queue\Attributes
 */
#[Attribute(Attribute::TARGET_CLASS)]
class JobAttribute implements JobAttributeInterface
{
    use JobAttributeTrait;

    /**
     * JobAttribute constructor
     *
     * @param string|null $channel Channel the job should run in
     * @param int|null $max_tries Maximum number of attempts that will be made to run the job
     * @param int|null $timeout Timeout in seconds for how long the job has to run
     * @param string|null $name Name of the job
     * @param array|null $backoff Backoff configuration for job retries to follow
     */
    public function __construct(
        ?string $channel = null,
        ?int $max_tries = null,
        ?int $timeout = null,
        ?string $name = null,
        ?array $backoff = null
    ) {
        $this->channel = $channel;
        $this->max_tries = $max_tries;
        $this->timeout = $timeout;
        $this->name = $name;
        $this->backoff = $backoff;
    }
}
