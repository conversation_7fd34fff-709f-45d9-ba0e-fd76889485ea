<?php

declare(strict_types=1);

namespace Core\Components\Queue\StaticAccessors;

use Core\Classes\StaticAccessor;
use Core\Components\Queue\Interfaces\ManagerInterface;

/**
 * Class Queue
 *
 * @package Core\Components\Queue\StaticAccessors
 *
 * @method static void sendJobs()
 */
class Queue extends StaticAccessor
{
    /**
     * Get instance of queue manager
     *
     * @return ManagerInterface
     */
    protected static function getInstance(): ManagerInterface
    {
        return static::$kernel->get(ManagerInterface::class);
    }
}
