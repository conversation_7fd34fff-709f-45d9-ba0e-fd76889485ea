<?php

declare(strict_types=1);

namespace Core\Components\Queue\Classes;

use Carbon\Carbon;
use Core\Components\Queue\Interfaces\{JobProcessorInterface, WorkerInterface};
use Ramsey\Uuid\UuidInterface;
use Throwable;

/**
 * Class JobProcessor
 *
 * @package Core\Components\Queue\Classes
 */
abstract class JobProcessor implements JobProcessorInterface
{
    /**
     * @var float|null Start time of job processing (microtime float)
     */
    protected ?float $start_time = null;

    /**
     * @var int|null Time it took to process job (in milliseconds)
     */
    protected ?int $elapsed_time = null;

    /**
     * @var WorkerInterface|null Worker instance which processed job
     */
    protected ?WorkerInterface $worker = null;

    /**
     * @var bool Determines if job was handled successfully
     */
    protected bool $handled = false;

    /**
     * @var Throwable|null Exception which triggered a retry
     */
    protected ?Throwable $retry_exception = null;

    /**
     * @var Carbon|null Timestamp of when job should be retried
     */
    protected ?Carbon $retry_at = null;

    /**
     * @var Throwable|null Exception which triggered a failure
     */
    protected ?Throwable $fail_exception = null;

    /**
     * Get id from job envelope
     *
     * @return UuidInterface
     */
    public function getID(): UuidInterface
    {
        return $this->getJobEnvelope()->getID();
    }

    /**
     * Get name from job envelope
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->getJobEnvelope()->getName();
    }

    /**
     * Get tries from job envelope
     *
     * @return int
     */
    public function getTries(): int
    {
        return $this->getJobEnvelope()->getTries();
    }

    /**
     * Get max tries from job envelope
     *
     * @return int|null
     */
    public function getMaxTries(): ?int
    {
        return $this->getJobEnvelope()->getMaxTries();
    }

    /**
     * Get timeout from job envelope
     *
     * @return int|null
     */
    public function getTimeout(): ?int
    {
        return $this->getJobEnvelope()->getTimeout();
    }

    /**
     * Get backoff config from job envelope
     *
     * @return array|null
     */
    public function getBackoff(): ?array
    {
        return $this->getJobEnvelope()->getBackoff();
    }

    /**
     * Handle any actions before job is ran
     */
    protected function preRun(): void
    {
        //
    }

    /**
     * Handle any actions after job is ran
     *
     * This method will be called even if an exception is throwing during the job execution.
     */
    protected function postRun(): void
    {
        //
    }

    /**
     * Process job
     *
     * @param WorkerInterface|null $worker
     */
    public function run(?WorkerInterface $worker = null): void
    {
        $this->worker = $worker;
        $this->start_time = microtime(true);
        try {
            $this->preRun();
            $this->driver->getManager()->runJob($this->job_envelope->getJob());
            $this->onHandled();
            $this->handled = true;
        } finally {
            $this->postRun();
        }
    }

    /**
     * Handle any actions if job is successfully ran
     */
    protected function onHandled(): void
    {
        //
    }

    /**
     * Determines if job was ran successfully
     *
     * @return bool
     */
    public function isHandled(): bool
    {
        return $this->handled;
    }

    /**
     * Get start time of processing
     *
     * @return float|null
     */
    public function getStartTime(): ?float
    {
        return $this->start_time;
    }

    /**
     * Get the total execution time of a job in milliseconds
     *
     * @return int|null
     */
    public function getElapsedTime(): ?int
    {
        if ($this->elapsed_time === null) {
            $this->elapsed_time = (int) round(((microtime(true) - $this->start_time) * 1000000) / 1000);
        }
        return $this->elapsed_time;
    }

    /**
     * Trigger retry of job
     *
     * If backoff seconds are passed and no retry time is provided by job, then a future retry timestamp is generated.
     *
     * @param Throwable $e
     * @param int|null $backoff_seconds
     */
    public function retry(Throwable $e, ?int $backoff_seconds = null): void
    {
        $this->retry_exception = $e;
        if (($retry_at = $this->getJobEnvelope()->getJob()->retryAt()) !== null) {
            $this->retry_at = $retry_at;
        } elseif ($backoff_seconds !== null) {
            $this->retry_at = Carbon::now('UTC')->addSeconds($backoff_seconds);
        }
        $this->onRetry($e, $this->retry_at);
    }

    /**
     * Get retry at timestamp
     *
     * Only available after job has been marked as retry.
     *
     * @return Carbon|null
     */
    public function getRetryAt(): ?Carbon
    {
        return $this->retry_at;
    }

    /**
     * Determines if job will be retried
     *
     * @return bool
     */
    public function isRetrying(): bool
    {
        return $this->retry_exception !== null;
    }

    /**
     * Get exception which caused retry
     *
     * @return Throwable|null
     */
    public function getRetryException(): ?Throwable
    {
        return $this->retry_exception;
    }

    /**
     * Handle any actions for a retry
     *
     * @param Throwable $e
     * @param Carbon|null $retry_at
     */
    protected function onRetry(Throwable $e, ?Carbon $retry_at): void
    {
        //
    }

    /**
     * Mark job as failed
     *
     * @param Throwable $e
     */
    public function failed(Throwable $e): void
    {
        $this->fail_exception = $e;
        $envelope = $this->getJobEnvelope();
        $envelope->getJob()->failed($e, $envelope->getTries());
        $this->onFail($e);
    }

    /**
     * Determines if job is failed
     *
     * @return bool
     */
    public function isFailed(): bool
    {
        return $this->fail_exception !== null;
    }

    /**
     * Get exception which caused failure
     *
     * @return Throwable|null
     */
    public function getFailException(): ?Throwable
    {
        return $this->fail_exception;
    }

    /**
     * Handle any actions for job failure
     *
     * @param Throwable $e
     */
    protected function onFail(Throwable $e): void
    {
        //
    }
}
