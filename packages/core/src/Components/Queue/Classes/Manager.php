<?php

declare(strict_types=1);

namespace Core\Components\Queue\Classes;

use Core\Classes\Config;
use Core\Components\Queue\Exceptions\QueueException;
use Core\Components\Queue\Interfaces\{DriverInterface, JobBuilderInterface, JobInterface, ManagerInterface};
use Core\Interfaces\KernelInterface;

/**
 * Class Manager
 *
 * @package Core\Components\Queue\Classes
 */
class Manager implements ManagerInterface
{
    /**
     * List of all channels
     *
     * Format: id => human readable string
     *
     * @var array
     */
    protected array $channels = [];

    /**
     * @var string Default channel which jobs use when one is not specified
     */
    protected string $default_channel;

    /**
     * List of all job types
     *
     * Format: id => full class name
     *
     * @var array
     */
    protected array $jobs = [];

    /**
     * @var array List of active connections
     */
    protected array $connections = [];

    /**
     * @var string Default connection name
     */
    protected string $default_connection;

    /**
     * @var array Cache of configured driver instances
     */
    protected array $driver_instances = [];

    /**
     * @var array List of all job builders to send
     */
    protected array $job_builders = [];

    /**
     * Manager constructor
     *
     * @param Config $config
     * @param KernelInterface $kernel
     */
    public function __construct(protected Config $config, protected KernelInterface $kernel)
    {
        $queue = $config->get('queue', []);
        $this->channels = $queue['channels'] ?? [];
        $this->default_channel = $queue['default_channel'];
        $this->jobs = $queue['jobs'] ?? [];
        $this->connections = $queue['connections'] ?? [];
        $this->default_connection = $queue['default_connection'] ?? 'default';
    }

    /**
     * Get default channel
     *
     * @return string
     */
    public function getDefaultChannel(): string
    {
        return $this->default_channel;
    }

    /**
     * Get all channels
     *
     * @return array
     */
    public function getChannels(): array
    {
        return $this->channels;
    }

    /**
     * Determines if a channel with specified name exists
     *
     * @param string $name
     * @return bool
     */
    public function hasChannel(string $name): bool
    {
        return isset($this->channels[$name]);
    }

    /**
     * Get configuration for specific channel
     *
     * @param string $name
     * @return array|null
     */
    public function getChannel(string $name): ?array
    {
        return $this->channels[$name] ?? null;
    }

    /**
     * Get config for specified connection
     *
     * @param string $name
     * @return array|null
     */
    public function getConnectionConfig(string $name): ?array
    {
        return $this->connections[$name] ?? null;
    }

    /**
     * Get driver instance from connection name
     *
     * @param string|null $name
     * @return DriverInterface
     * @throws QueueException
     */
    public function getConnection(?string $name = null): DriverInterface
    {
        $name ??= $this->default_connection;
        if (!isset($this->driver_instances[$name])) {
            $config = $this->getConnectionConfig($name);
            if (!isset($config['driver'])) {
                throw new QueueException('No driver specified for connection: %s', $name);
            }
            $driver = $this->kernel->create($config['driver']);
            unset($config['driver']);
            $driver->configure($config);
            $this->driver_instances[$name] = $driver;
        }
        return $this->driver_instances[$name];
    }

    /**
     * Get list of all jobs
     *
     * @return array
     */
    public function getJobs(): array
    {
        return $this->jobs;
    }

    /**
     * Determines if job class is registered in the config
     *
     * @param string $class
     * @return bool
     */
    public function isJobRegistered(string $class): bool
    {
        return in_array($class, $this->jobs);
    }

    /**
     * Get job instance by class name
     *
     * @param string $job_class
     * @param array $args
     * @return JobInterface
     */
    public function getJobInstance(string $job_class, array $args = []): JobInterface
    {
        return new $job_class(...$args);
    }

    /**
     * Call handle method of job using dependency injection from registry
     *
     * @param JobInterface $job
     */
    public function runJob(JobInterface $job): void
    {
        $this->kernel->call([$job, 'handle']);
    }

    /**
     * Send individual job envelope
     *
     * @param JobBuilderInterface $job_builder
     * @throws QueueException
     */
    protected function send(JobBuilderInterface $job_builder): void
    {
        $name = $job_builder->getConnection() ?? $this->default_connection;
        $this->getConnection($name)->pushFromBuilder($job_builder);
    }

    /**
     * Send all available jobs
     */
    public function sendJobs(): void
    {
        if (count($this->job_builders) === 0) {
            return;
        }
        foreach ($this->job_builders as $job_builder) {
            $this->send($job_builder);
        }
        $this->job_builders = [];
    }

    /**
     * Queue job to be sent at termination of app
     *
     * This is abstract since queues can be handled in many ways. Just provides a common interface so the implementing
     * application can store the job somewhere.
     *
     * @param JobBuilderInterface $job_builder
     */
    public function dispatch(JobBuilderInterface $job_builder): void
    {
        $this->job_builders[] = $job_builder;
    }

    /**
     * Send job to be handled now instead of waiting to termination of app
     *
     * @param JobBuilderInterface $job_builder
     * @throws QueueException
     */
    public function dispatchNow(JobBuilderInterface $job_builder): void
    {
        $this->send($job_builder);
    }
}
