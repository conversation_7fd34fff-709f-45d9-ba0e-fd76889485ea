<?php

declare(strict_types=1);

namespace Core\Components\Resource\Exceptions;

use Core\Components\Resource\Classes\Resource;
use Core\Exceptions\AppException;
use Throwable;

/**
 * Class ResourceException
 *
 * @package App\Exceptions
 */
class ResourceException extends AppException
{
    /**
     * Convert exception into usage message which doesn't compromise security
     *
     * @param Throwable $exception
     * @return string|array
     * @throws ResourceException
     */
    public static function toMessage(Throwable $exception)
    {
        $class = get_class($exception);
        switch ($class) {
            case ActionNotAllowedException::class:
            case EntityNotFoundException::class:
            case ImmutableEntityException::class:
                $message = $exception->getMessage();
                break;
            case ValidationException::class:
            case BatchPrepareException::class:
            case BatchHandleException::class:
                $errors = $exception->getErrors();
                if (count($errors) === 0) {
                    $message = $exception->getMessage();
                    break;
                }
                $message = $errors;
                break;
            case InvalidUuidException::class:
                $message = 'Primary field UUID is not valid';
                break;
            default:
                Resource::getLogger()->error('Unable to convert exception into client error message', [
                    'exception' => $exception
                ]);
                $message = 'Unknown error';
                break;
        }
        return $message;
    }
}
