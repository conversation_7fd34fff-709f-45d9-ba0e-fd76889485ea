<?php

declare(strict_types=1);

namespace Core\Components\Resource\Traits\Request;

/**
 * Trait TransactionTrait
 *
 * @package Core\Components\Resource\Traits\Request
 */
trait TransactionTrait
{
    /**
     * @var bool|null Determines if request will use a transaction, if null will use default value
     */
    protected ?bool $use_transaction = null;

    /**
     * Set if transaction should be used for request
     *
     * Used by batch requests to prevent unnecessary nested transactions. If null, the request will use it's default
     * value.
     *
     * @param bool|null $use_transaction
     */
    public function setUseTransaction(?bool $use_transaction)
    {
        $this->use_transaction = $use_transaction;
    }

    /**
     * Determines if transaction is used for request
     *
     * @return bool|null
     */
    public function useTransaction(): ?bool
    {
        return $this->use_transaction;
    }
}
