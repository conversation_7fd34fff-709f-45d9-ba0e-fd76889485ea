<?php

namespace Core\Components\Resource\Traits\Request;

use Core\Components\Resource\Classes\FieldList;

trait FieldTrait
{
    protected $fields = null;

    /**
     * @return \Core\Components\Resource\Classes\Resource
     */
    abstract public function resource();

    /**
     * @param $base_name
     * @param array $hooks
     * @param bool $include_base
     * @return array
     */
    abstract protected function buildHooks($base_name, array $hooks, $include_base = true);

    /**
     * @return array
     */
    abstract protected function getHookNames();

    /**
     * @param $name
     * @param $value
     * @param array ...$args
     * @return mixed
     */
    abstract protected function applyCallback($name, $value, ...$args);

    public function setFields(FieldList $field_list)
    {
        $this->fields = $this->resource()->runFilterHook(
            $this->buildHooks('request.fields', $this->getHookNames()), $field_list, $this
        );
        $this->fields = $this->applyCallback('fields', $this->fields);
    }

    /**
     * @return null|FieldList
     */
    public function getFields()
    {
        return $this->fields;
    }
}
