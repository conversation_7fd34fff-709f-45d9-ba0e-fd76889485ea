<?php

namespace Core\Components\Resource\Classes;

use Closure;
use InvalidArgumentException;

class Callbacks
{
    const ACTION_OVERWRITE = 1;
    const ACTION_PREPEND = 2;
    const ACTION_APPEND = 3;

    protected $callbacks = [];

    public function add($name, Closure $closure, $action = self::ACTION_APPEND)
    {
        if (!isset($this->callbacks[$name]) || $action === self::ACTION_OVERWRITE) {
            $this->callbacks[$name] = [];
            $action = self::ACTION_APPEND;
        }
        switch ($action) {
            case self::ACTION_PREPEND:
                array_unshift($this->callbacks[$name], $closure);
                break;
            case self::ACTION_APPEND:
                $this->callbacks[$name][] = $closure;
                break;
            default:
                throw new InvalidArgumentException('Invalid action');
        }
        return $this;
    }

    public function apply($name, $value, ...$args)
    {
        if (!isset($this->callbacks[$name])) {
            return $value;
        }
        foreach ($this->callbacks[$name] as $callback) {
            $value = $callback($value, ...$args);
        }
        return $value;
    }

    public function run($name, ...$args)
    {
        if (!isset($this->callbacks[$name])) {
            return;
        }
        foreach ($this->callbacks[$name] as $callback) {
            $callback(...$args);
        }
    }
}
