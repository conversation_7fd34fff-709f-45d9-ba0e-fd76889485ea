<?php

declare(strict_types=1);

namespace Core\Components\Resource\Classes;

use Core\Components\Http\Classes\URLBuilder;
use Core\Components\Resource\Interfaces\MediaHandlerInterface;
use Core\Components\Resource\Interfaces\ResourceMediaInterface;
use Core\Exceptions\AppException;
use Core\StaticAccessors\{App, Path};

/**
 * Class MediaTypeVersion
 *
 * @package Core\Components\Resource\Classes
 */
class MediaTypeVersion
{
    /**
     * @var string|null Path to version storage
     */
    protected ?string $path = null;

    /**
     * @var string|null Name of directory to store version info
     */
    protected ?string $directory_name = null;

    /**
     * @var string|null Name of URL slug to use when building URL
     */
    protected ?string $url_slug = null;

    /**
     * @var string|null Handler class
     */
    protected ?string $handler = null;

    /**
     * MediaTypeVersion constructor
     *
     * @param MediaType $type
     * @param string $name
     */
    public function __construct(protected MediaType $type, protected string $name)
    {}

    /**
     * Set MediaType instance
     *
     * @param MediaType $type
     */
    public function setType(MediaType $type): void
    {
        $this->type = $type;
    }

    /**
     * Get MediaType instance
     *
     * @return MediaType
     */
    public function getType(): MediaType
    {
        return $this->type;
    }

    /**
     * Set name of version
     *
     * @param string $name
     * @return $this
     */
    public function name(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    /**
     * Get name of version
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * Set directory name of where to store this version
     *
     * @param string $name
     * @param bool $use_as_slug
     * @return $this
     */
    public function directoryName(string $name, bool $use_as_slug = false): self
    {
        $this->directory_name = $name;
        if ($use_as_slug) {
            $this->urlSlug($name);
        }
        return $this;
    }

    /**
     * Get directory name
     *
     * @return string
     */
    public function getDirectoryName(): string
    {
        return $this->directory_name ?? $this->name;
    }

    /**
     * Set absolute path to where file should be stored
     *
     * Overrides the generated path using directory name.
     *
     * @param string $path
     * @return $this
     */
    public function path(string $path): self
    {
        $this->path = Path::normalize($path);
        return $this;
    }

    /**
     * Get path to version
     *
     * @param string|int $path
     * @return string
     */
    public function getPath(string|int $path = ''): string
    {
        if ($this->path !== null) {
            return $this->path . $path;
        }
        return $this->type->getPath($this->getDirectoryName() . DIRECTORY_SEPARATOR . $path, $this);
    }

    /**
     * Set URL slug for public access
     *
     * @param string $slug
     * @return $this
     */
    public function urlSlug(string $slug): self
    {
        $this->url_slug = $slug;
        return $this;
    }

    /**
     * Get URL slug
     *
     * @return string
     */
    public function getUrlSlug(): string
    {
        return $this->url_slug ?? $this->name;
    }

    /**
     * Get URL builder for version
     *
     * @param string|int $path
     * @return URLBuilder
     */
    public function getUrl(string|int $path = ''): URLBuilder
    {
        $url = new URLBuilder();
        $url->path($this->getUrlSlug() . '/' . $path);
        return $this->type->getUrl($url, $this);
    }

    /**
     * Set handler class
     *
     * @param string $class
     * @return $this
     */
    public function handler(string $class): self
    {
        $this->handler = $class;
        return $this;
    }

    /**
     * Get handler class
     *
     * @return string
     * @throws AppException
     */
    public function getHandler(): string
    {
        if ($this->handler === null) {
            throw new AppException('No handler defined');
        }
        return $this->handler;
    }

    /**
     * Create new handler instance with resource
     *
     * @param ResourceMediaInterface $resource
     * @return MediaHandlerInterface
     * @throws AppException
     */
    public function newHandler(ResourceMediaInterface $resource): MediaHandlerInterface
    {
        $handler = $this->getHandler();
        $handler = App::get($handler);
        if (!($handler instanceof MediaHandlerInterface)) {
            throw new AppException('Handler must implement MediaHandlerInterface');
        }
        $handler->setResource($resource);
        $handler->setMediaTypeVersion($this);
        return $handler;
    }
}
