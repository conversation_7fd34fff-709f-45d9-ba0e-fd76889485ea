<?php

namespace Core\Components\Http\StaticAccessors;

use Core\Components\Http\Interfaces\Route\BuilderFactoryInterface as RouteBuilderFactoryInterface;
use Core\Classes\StaticAccessor;

/**
 * Class Route
 *
 * @package Core\Components\Http\StaticAccessors
 *
 * @method static \Core\Components\Http\Classes\Route\BuilderGroup group(\Closure $closure)
 * @method static \Core\Components\Http\Classes\Route\BaseBuilder path(string $path)
 * @method static \Core\Components\Http\Classes\Route\BaseBuilder get(string $path)
 * @method static \Core\Components\Http\Classes\Route\BaseBuilder post(string $path)
 * @method static \Core\Components\Http\Classes\Route\BaseBuilder put(string $path)
 * @method static \Core\Components\Http\Classes\Route\BaseBuilder patch(string $path)
 * @method static \Core\Components\Http\Classes\Route\BaseBuilder delete(string $path)
 * @method static \Core\Components\Http\Classes\Route\BaseBuilder any(string $path)
 * @method static \Core\Components\Http\Classes\Route\BaseBuilder match(string|array $methods, string $path)
 */
class Route extends StaticAccessor
{
    /**
     * Get instance of BuilderFactoryInterface
     *
     * @return \Core\Components\Http\Interfaces\Route\BuilderFactoryInterface
     */
    protected static function getInstance()
    {
        return static::$kernel->get(RouteBuilderFactoryInterface::class);
    }
}
