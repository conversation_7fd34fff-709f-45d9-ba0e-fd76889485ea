<?php

declare(strict_types=1);

namespace Core\Components\Http\Classes;

use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\Interfaces\ResponseInterface;
use Core\Classes\Exception\HttpHandler;
use Throwable;

/**
 * Class ExceptionHandler
 *
 * @package Core\Components\Http\Classes
 */
class ExceptionHandler extends HttpHandler
{
    /**
     * @param Throwable $exception
     */
    public function handleException(Throwable $exception): void
    {
        if ($exception instanceof HttpResponseException) {
            $this->handleResponse($this->getHttpResponse($exception));
            return;
        }
        parent::handleException($exception);
    }

    /**
     * Handle HttpException exception
     *
     * @param HttpResponseException $e
     *
     * @return ResponseInterface
     */
    protected function getHttpResponse(HttpResponseException $e): ResponseInterface
    {
        return $e->getResponse();
    }

    /**
     * @param ResponseInterface $response
     */
    protected function handleResponse(ResponseInterface $response): void
    {
        $response->handle();
        exit;
    }
}
