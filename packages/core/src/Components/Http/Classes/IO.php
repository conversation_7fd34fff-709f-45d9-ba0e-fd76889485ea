<?php

namespace Core\Components\Http\Classes;

use Core\Components\Http\Exceptions\IOException;
use Core\Components\Http\Interfaces\IOInterface;

/**
 * Class IO
 *
 * @package Core\Components\Http\Classes
 */
class IO implements IOInterface
{
    /**
     * Input instance
     *
     * @var Input
     */
    protected $input;

    /**
     * Output stream
     *
     * @var resource
     */
    protected $output;

    /**
     * Create instances for built-in superglobals
     *
     * @return static
     */
    public static function fromGlobals()
    {
        return new static(new Input);
    }

    /**
     * RequestIO constructor
     *
     * @param Input $input
     * @param null|resource $output
     *
     * @throws IOException
     */
    public function __construct(Input $input, $output = null)
    {
        $this->input = $input;
        if ($output === null) {
            $output = fopen('php://output', 'w');
        } elseif (!is_resource($output)) {
            throw new IOException('Output parameter must be a resource');
        }
        $this->output = $output;
    }

    /**
     * Get Input instance
     *
     * @return Input
     */
    public function getInput()
    {
        return $this->input;
    }

    /**
     * Get input stream
     *
     * @return resource
     */
    public function getInputStream()
    {
        return $this->input->getStream();
    }

    /**
     * Get output stream
     *
     * @return resource
     */
    public function getOutputStream()
    {
        return $this->output;
    }
}
