<?php

namespace Core\Components\Http\Classes;

use Closure;
use Core\Classes\Arr;
use Core\Components\Http\Exceptions\MiddlewareException;
use Core\Components\Http\Interfaces\MiddlewareInterface;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Interfaces\ResponseInterface;
use Core\Interfaces\KernelInterface;

/**
 * Class Middleware
 *
 * @package Core\Components\Http\Classes
 */
class Middleware implements MiddlewareInterface
{
    /**
     * @var KernelInterface
     */
    protected $kernel;

    /**
     * @var array
     */
    protected $files = [];

    /**
     * @var bool
     */
    protected $loaded = false;

    /**
     * @var array[]
     */
    protected $middleware = [];

    /**
     * @var MiddlewareStack[]
     */
    protected $groups = [];

    /**
     * Middleware constructor
     *
     * @param KernelInterface $kernel
     */
    public function __construct(KernelInterface $kernel)
    {
        $this->kernel = $kernel;
    }

    /**
     * @param string $path
     * @throws MiddlewareException
     */
    public function addFile($path)
    {
        if (!file_exists($path)) {
            throw new MiddlewareException('Unable to find file: %s', $path);
        }
        $this->files[] = $path;
    }

    /**
     * Load middleware from defined files
     *
     * @todo potentially add caching layer here
     */
    protected function load()
    {
        if ($this->loaded) {
            return;
        }
        foreach ($this->files as $path) {
            include_once $path;
        }
        $this->loaded = true;
    }

    /**
     * @param string $name
     * @param string $class
     * @param array $args
     * @return $this
     */
    public function add($name, $class, array $args = [])
    {
        $this->middleware[$name] = [
            'class' => $class,
            'args' => $args
        ];
        return $this;
    }

    /**
     * @param string $alias
     * @param string $name
     * @param array $args
     * @return $this
     *
     * @throws MiddlewareException
     */
    public function alias($alias, $name, array $args = [])
    {
        if (!$this->has($name)) {
            throw new MiddlewareException('Aliased middleware \'%s\' not found', $name);
        }
        $this->middleware[$alias] = [
            'alias' => $name,
            'args' => $args
        ];
        return $this;
    }

    /**
     * @param string $name
     * @param Closure $closure
     * @return $this
     */
    public function group($name, Closure $closure)
    {
        $stack = new MiddlewareStack($this);
        call_user_func($closure, $stack);
        $this->groups[$name] = $stack;
        return $this;
    }

    /**
     * @param string $name
     * @return bool
     */
    public function has($name)
    {
        $this->load();
        return isset($this->middleware[$name]);
    }

    /**
     * @param string $name
     * @return bool
     */
    public function hasGroup($name)
    {
        $this->load();
        return isset($this->groups[$name]);
    }

    /**
     * @param string $name
     * @return bool|MiddlewareStack
     */
    public function getGroup($name)
    {
        if (!$this->hasGroup($name)) {
            return false;
        }
        return $this->groups[$name];
    }

    /**
     * @param MiddlewareStack $stack
     * @param RequestInterface $request
     *
     * @throws MiddlewareException
     */
    public function handle(MiddlewareStack $stack, RequestInterface $request)
    {
        $items = $stack->getItems();
        if (count($items) === 0) {
            return;
        }

        $stack = [];
        $terminate = [];
        foreach ($items as $name => $args) {
            // handle alias
            $middleware = $this->middleware[$name];
            if (isset($middleware['alias'])) {
                $alias_args = $middleware['args'];
                $middleware = $this->middleware[$middleware['alias']];
                $middleware['args'] = Arr::mergeRecursiveDistinct($middleware['args'], $alias_args);
            }
            $middleware['args'] = Arr::mergeRecursiveDistinct($middleware['args'], $args);
            if (!is_object($middleware['class'])) {
                if (!class_exists($middleware['class'])) {
                    throw new MiddlewareException('Unable to find middleware class: %s', $middleware['class']);
                }
                if (!method_exists($middleware['class'], 'handle')) {
                    throw new MiddlewareException(
                        'Middleware class \'%s\' does not have a public handle method', $middleware['class']
                    );
                }
                if (method_exists($middleware['class'], 'terminate')) {
                    $terminate[] = $middleware['class'];
                }
            }
            $stack[] = $middleware;
        }

        $i = 0;
        $next = function (RequestInterface $request) use (&$next, &$i, $stack) {
            if (!isset($stack[$i])) {
                throw new MiddlewareException('Stack is empty');
            }
            $middleware = $stack[$i];
            $i++;
            $args = [
                'request' => $request,
                'next' => $next
            ];
            $args = $args + $middleware['args'];
            if (is_object($middleware['class']) && $middleware['class'] instanceof Closure) {
                return $this->kernel->call($middleware['class'], $args);
            }
            $class = $this->kernel->get($middleware['class']);
            return $this->kernel->call([$class, 'handle'], $args);
        };

        $response = $next($request);

        if (is_object($response) && $response instanceof ResponseInterface) {
            $response->handle();
        }

        if (count($terminate) > 0) {
            foreach ($terminate as $class) {
                $class = $this->kernel->get($class);
                $class->terminate($request, $response);
            }
        }
    }
}
