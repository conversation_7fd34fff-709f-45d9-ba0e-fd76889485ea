<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;


final class CreateBidItemContentBooleanMigration extends Migration
{
    public function up(): void
    {
        $this->updateTable('bidContent')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('isAnswerRequired')->after('isRequired')->default(0);
            })
            ->alter();

        $this->updateTable('bidItemContent')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('isAnswerRequired')->after('isRequired')->default(0);
                $table->unsignedTinyInteger('isAccepted')->after('isAnswerRequired')->default(0);
            })
            ->alter();
    }

    public function down(): void
    {
        $this->updateTable('bidContent')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['isAnswerRequired']);
            })
            ->alter();

        $this->updateTable('bidItemContent')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['isAnswerRequired', 'isAccepted']);
            })
            ->alter();
    }
}
