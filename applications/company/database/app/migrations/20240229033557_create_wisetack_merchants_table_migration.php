<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;


final class CreateWisetackMerchantsTableMigration extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!$this->schema->hasTable('wisetackMerchants')) {
            $this->newTable('wisetackMerchants')
                ->columns(function (Blueprint $table) {
                    $table->char('merchantId', 36)->primary();
                    $table->char('companyUUID', 36);
                    $table->string('status');
                    $table->string('signupLink')->nullable();
                    $table->string('prequalificationLink')->nullable();
                    $table->dateTime('deletedAt')->nullable();
                })
                ->indexes(function (Blueprint $table) {
                    $table->unique(['companyUUID', 'deletedAt'], 'uniq_company_id_deleted_at');
                })
                ->timestamps(false, false)
                ->useHistory(false)
                ->create();
        }
    }

    public function down()
    {
        $this->schema->dropIfExists('wisetackMerchants');
    }
}
