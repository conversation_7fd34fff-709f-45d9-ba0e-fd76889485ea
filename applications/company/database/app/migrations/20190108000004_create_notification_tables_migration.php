<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class CreateNotificationTablesMigration extends Migration
{
    public function up()
    {
        $this->newTable('notifications')
            ->primaryKey('notificationID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type');
            })
            ->column('itemID', Table::COLUMN_TYPE_UUID, [
                'after' => 'type'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index(['type', 'itemID'], 'type_item_id_index');
            })
            ->useHistory(false)
            ->timestamps(true, false)
            ->create();

        $this->newTable('notificationDistributions')
            ->primaryKey('notificationDistributionID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type');
            })
            ->column('notificationID', Table::COLUMN_TYPE_UUID, [
                'after' => 'notificationDistributionID'
            ])
            ->column('itemID', Table::COLUMN_TYPE_UUID, [
                'after' => 'type'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('notificationID', 'notification_id_index');
                $table->index(['type', 'itemID'], 'type_item_id_index');
            })
            ->useHistory(false)
            ->timestamps(true, false)
            ->create();
    }

    public function down()
    {
        $this->dropTables(['notifications', 'notificationDistributions']);
    }
}
