<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateSearchTableMigration extends Migration
{
    public function up()
    {
        $this->newTable('searches')
            ->primaryKey('searchID')
            ->columns(function (Blueprint $table) {
                $table->string('term', 200);
                $table->unsignedInteger('resultCount');
                $table->unsignedBigInteger('time');
            })
            ->useHistory(false)
            ->create();
    }

    public function down()
    {
        $this->dropTables(['searches']);
    }
}
