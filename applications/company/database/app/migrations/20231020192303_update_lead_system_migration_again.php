<?php
declare(strict_types=1);



use App\Classes\DB\Migration;
use App\Classes\DB\Schema;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

final class UpdateLeadSystemMigrationAgain extends Migration
{
    public function up(): void
    {
        DB::statement('ALTER TABLE `leads` MODIFY COLUMN `marketingTypeID` INT(10) UNSIGNED NULL');
        DB::statement('ALTER TABLE `leadsHistory` MODIFY COLUMN `marketingTypeID` INT(10) UNSIGNED NULL');
    }

    public function down(): void
    {
        DB::statement('ALTER TABLE `leads` MODIFY COLUMN `marketingTypeID` TINYINT(3) UNSIGNED NOT NULL');
        DB::statement('ALTER TABLE `leadsHistory` MODIFY COLUMN `marketingTypeID` TINYINT(3) UNSIGNED NOT NULL');
    }
}
