<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

final class UpdateTaskSystemMigration extends Migration
{
    public function up(): void
    {
        $this->updateTable('tasks')
            ->columns(function (Blueprint $table) {
                $table->text('completionNotes')->after('notes')->nullable();
            })
            ->index('search', Table::INDEX_TYPE_FULLTEXT, ['title', 'notes', 'completionNotes'])
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
                $table->index('status');
                $table->index('type');
                $table->index(['associationType', 'associationID']);
                $table->index('assignedToUserID');
                $table->index('reminderType');
                $table->index('completedByUserID');
            })
            ->alter();
    }

    public function down(): void
    {
        $this->updateTable('tasks')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('completionNotes');
                $table->dropIndex('search');
                $table->dropIndex(['companyID']);
                $table->dropIndex(['status']);
                $table->dropIndex(['type']);
                $table->dropIndex(['associationType', 'associationID']);
                $table->dropIndex(['assignedToUserID']);
                $table->dropIndex(['reminderType']);
                $table->dropIndex(['completedByUserID']);
            })
            ->alter();
    }
}
