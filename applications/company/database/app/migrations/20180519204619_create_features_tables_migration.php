<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema;
use Illuminate\Database\Schema\Blueprint;

class CreateFeaturesTablesMigration extends Migration
{
    public function up()
    {
        // features table
        $this->schema->create('features', function (Blueprint $table) {
            $table->increments('featureID');
            $table->string('name', 100);
            Schema::timestamps($table);
        });

        // companies features table
        $this->schema->create('companiesFeatures', function (Blueprint $table) {
            $table->increments('companyFeatureID');
            $table->unsignedInteger('companyID');
            $table->unsignedInteger('featureID');
            Schema::timestamps($table);

            $table->index('companyID');
            $table->index('featureID');
        });
    }

    public function down()
    {
        $this->dropTables(['features', 'companiesFeatures']);
    }
}
