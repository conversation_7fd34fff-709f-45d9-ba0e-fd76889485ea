<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

final class AddDefaultAssignedToUserIDToLeadsFormMigration extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        $this->updateTable('leadForms')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('defaultAssignedToUserID')
                    ->after('saveButtonLabel')
                    ->nullable()
                    ->comment('Default user ID assigned to the lead');
            })
            ->useHistory(false)
            ->alter();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        $this->updateTable('leadForms')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('defaultAssignedToUserID');
            })
            ->useHistory(false)
            ->alter();
    }
}
