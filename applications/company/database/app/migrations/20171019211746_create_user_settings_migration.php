<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema;
use Illuminate\Database\Schema\Blueprint;

class CreateUserSettingsMigration extends Migration
{
    public function up()
    {
        $this->schema->create('userSettings', function (Blueprint $table) {
            $table->increments('userSettingsID');
            $table->unsignedInteger('userID');
            $table->string('name', 100);
            $table->text('value');
            $table->index(['userID', 'name']);
            Schema::timestamps($table, true);
        });
    }

    public function down()
    {
        $this->schema->drop('userSettings');
    }
}