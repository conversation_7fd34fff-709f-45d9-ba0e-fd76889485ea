<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema;
use Illuminate\Database\Schema\Blueprint;

class CreateFileTablesMigration extends Migration
{
    public function up()
    {
        $this->newTable('files')
            ->primaryKey('fileID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type');
                $table->unsignedTinyInteger('status'); // in progress, finished, failed
                $table->string('name', 150)->nullable();
                $table->string('contentType', 100)->nullable();
                $table->string('extension', 20)->nullable();
                $table->unsignedBigInteger('size');
                $table->longText('data')->nullable();
                $table->unsignedBigInteger('time');
            })
            ->column('contentHash', Schema\Table::COLUMN_TYPE_BINARY, [
                'length' => 20,
                'after' => 'data'
            ])
            ->create();
        
        $this->newTable('fileVariants')
            ->primaryKey('fileVariantID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type');
                $table->unsignedTinyInteger('status'); // in progress, finished, failed
                $table->string('name', 150);
                $table->string('contentType', 100)->nullable(); // should be required for status finished
                $table->string('extension', 20);
                $table->unsignedBigInteger('size');
                $table->longText('data')->nullable();
                $table->unsignedBigInteger('time');
                $table->unsignedTinyInteger('isValid');
            })
            ->column('fileID', Schema\Table::COLUMN_TYPE_UUID, [
                'after' => 'fileVariantID'
            ])
            ->column('contentHash', Schema\Table::COLUMN_TYPE_BINARY, [
                'length' => 20,
                'after' => 'data'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('fileID');
            })
            ->create();
    }

    public function down()
    {
        $this->dropTables(['files', 'fileVariants'], true);
    }
}
