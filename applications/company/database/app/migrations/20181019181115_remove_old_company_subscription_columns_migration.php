<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class RemoveOldCompanySubscriptionColumnsMigration extends Migration
{
    public function up()
    {
        $this->updateTable('companies')
            ->columns(function (Blueprint $table) {
                $table->dropColumn([
                    'subscriptionCategoryID', 'oldRegistrationID', 'registrationComplete', 'customerPaymentProfileID',
                    'subscriptionPricingID', 'subscriptionNextBill', 'subscriptionExpiration',
                    'isSubscriptionCancelled', 'usersPaid'
                ]);
            })
            ->alter();
    }

    public function down()
    {
        $this->updateTable('companies')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('subscriptionCategoryID')->after('isSubscriptionLocked');
                $table->string('oldRegistrationID', 10)->nullable()->after('registrationID');
                $table->dateTime('registrationComplete')->nullable()->after('recentlyCompletedStatus');
                $table->string('customerPaymentProfileID', 50)->nullable()->after('customerProfileID');
                $table->unsignedInteger('subscriptionPricingID')->nullable()->after('customerPaymentProfileID');
                $table->dateTime('subscriptionNextBill')->nullable()->after('subscriptionPricingID');
                $table->dateTime('subscriptionExpiration')->nullable()->after('subscriptionNextBill');
                $table->unsignedTinyInteger('isSubscriptionCancelled')->nullable()->after('subscriptionExpiration');
                $table->integer('usersPaid')->nullable()->after('isSubscriptionCancelled');
            })
            ->alter();
    }
}
