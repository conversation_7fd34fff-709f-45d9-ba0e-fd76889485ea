<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema;
use Illuminate\Database\Schema\Blueprint;

class UpdateProjectFulltextTablesMigration extends Migration
{
    public function up()
    {
        Schema::fullTextIndex('project', 'global_search', ['projectDescription']);
        Schema::fullTextIndex('projectEmail', 'global_search', ['name', 'phoneNumber', 'email']);
    }

    public function down()
    {
        $this->schema->table('project', function (Blueprint $table) {
            $table->dropIndex('global_search');
        });

        $this->schema->table('projectEmail', function (Blueprint $table) {
            $table->dropIndex('global_search');
        });
    }
}
