<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

final class AddDescriptionToRegistrationProfilesMigration extends Migration
{
    public function up()
    {
        $this->schema->table('resellerRegistrationProfiles', function (Blueprint $table) {
            $table->string('description', 200)->nullable()->after('name');
        });
    }

    public function down()
    {
        $this->schema->table('resellerRegistrationProfiles', function (Blueprint $table) {
            $table->dropColumn('description');
        });
    }
}
