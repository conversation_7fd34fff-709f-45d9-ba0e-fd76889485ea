<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

final class AddResultTrackingTablesMigration extends Migration
{
    public function up()
    {
        $this->updateTable('imports')
            ->columns(function (Blueprint $table) {
                $table->dateTime('completed_at')->nullable()->after('status');
            })
            ->useHistory(false)
            ->alter();

        $this->newTable('import_ingests')
            ->primaryKey('id')
            ->columns(function (Blueprint $table) {
                $table->string('timezone', 50);
                $table->unsignedBigInteger('add_count');
                $table->unsignedBigInteger('update_count');
                $table->unsignedBigInteger('skip_count');
                $table->unsignedBigInteger('error_count');
                $table->unsignedBigInteger('time');
                $table->dateTime('created_at');
                $table->dateTime('updated_at')->nullable();
            })
            ->column('import_id', Table::COLUMN_TYPE_UUID, [
                'after' => 'id'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('import_id', 'import_id_index');
            })
            ->useHistory(false)
            ->noTimestamps()
            ->create();

        $this->newTable('import_ingest_results')
            ->primaryKey('id')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('resource_type');
                $table->unsignedBigInteger('add_count');
                $table->unsignedBigInteger('update_count');
                $table->unsignedBigInteger('skip_count');
                $table->unsignedBigInteger('error_count');
                $table->unsignedBigInteger('time');
                $table->dateTime('created_at');
                $table->dateTime('updated_at')->nullable();
            })
            ->column('import_ingest_id', Table::COLUMN_TYPE_UUID, [
                'after' => 'id'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('import_ingest_id', 'import_ingest_id_index');
            })
            ->useHistory(false)
            ->noTimestamps()
            ->create();

        $this->newTable('import_loads')
            ->primaryKey('id')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('allow_search');
                $table->unsignedTinyInteger('allow_insert');
                $table->unsignedTinyInteger('allow_update');
                $table->unsignedBigInteger('insert_count');
                $table->unsignedBigInteger('insert_skip_count');
                $table->unsignedBigInteger('update_count');
                $table->unsignedBigInteger('update_skip_count');
                $table->unsignedBigInteger('error_count');
                $table->unsignedBigInteger('time');
                $table->dateTime('created_at');
                $table->dateTime('updated_at')->nullable();
            })
            ->column('import_id', Table::COLUMN_TYPE_UUID, [
                'after' => 'id'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('import_id', 'import_id_index');
            })
            ->useHistory(false)
            ->noTimestamps()
            ->create();

        $this->newTable('import_load_results')
            ->primaryKey('id')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('resource_type');
                $table->unsignedBigInteger('insert_count');
                $table->unsignedBigInteger('insert_skip_count');
                $table->unsignedBigInteger('update_count');
                $table->unsignedBigInteger('update_skip_count');
                $table->unsignedBigInteger('error_count');
                $table->unsignedBigInteger('time');
                $table->dateTime('created_at');
                $table->dateTime('updated_at')->nullable();
            })
            ->column('import_load_id', Table::COLUMN_TYPE_UUID, [
                'after' => 'id'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('import_load_id', 'import_load_id_index');
            })
            ->useHistory(false)
            ->noTimestamps()
            ->create();
    }

    public function down()
    {
        $this->dropTables(['import_ingests', 'import_ingest_results', 'import_loads', 'import_load_results']);

        $this->updateTable('imports')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('completed_at');
            })
            ->useHistory(false)
            ->alter();
    }
}
