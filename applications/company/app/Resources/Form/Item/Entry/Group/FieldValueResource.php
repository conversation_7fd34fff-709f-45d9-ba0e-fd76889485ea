<?php

namespace App\Resources\Form\Item\Entry\Group;

use App\Interfaces\Resource\FormEntryFieldResourceInterface;
use App\ResourceDelegates\Form\Item\Entry\Group\FieldValueDelegate;
use App\Traits\Resource\FormEntryFieldResourceTrait;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\FormItemEntryGroupFieldValue;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Interfaces\SaveInterface;
use Core\Components\Resource\Requests\SaveRequest;
use Core\Components\Resource\Traits\SaveTrait;

class FieldValueResource extends Resource implements SaveInterface, FormEntryFieldResourceInterface
{
    use FormEntryFieldResourceTrait;
    use SaveTrait;
    use UserActionTrackingTrait;

    protected $available_actions = (self::ACTION_GROUP_ENABLE_ALL | self::ACTION_SAVE) & ~(self::ACTION_GROUP_BATCH | self::ACTION_SEARCH);

    protected $table = 'formItemEntryGroupFieldValues';
    protected $model = FormItemEntryGroupFieldValue::class;

    protected $generate_id = true;

    protected $allow_no_user = true;

    protected static function boot()
    {
        static::delegate(FieldValueDelegate::class);
    }

    public function save(Entity $entity)
    {
        return (new SaveRequest($this, $entity))
            ->lookupFields(['group_id', 'field_source', 'field_id']);
    }

    public function deleteByGroupID($group_id, $force = false)
    {
        $group_id = $this->getFields()->get('group_id')->saveValue($group_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.formItemEntryGroupID', $group_id)
            ->get()
            ->each(function ($model) use ($primary_field, $force) {
                $this->delete(Entity::make([
                    $primary_field->getName() => $primary_field->outputValueFromModel($model)
                ]))->force($force)->findConfig(['check_mutability' => !$force])->run();
            });
    }
}
