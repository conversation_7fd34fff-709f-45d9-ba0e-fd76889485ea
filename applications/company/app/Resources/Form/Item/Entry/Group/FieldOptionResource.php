<?php

namespace App\Resources\Form\Item\Entry\Group;

use App\Interfaces\Resource\FormEntryFieldResourceInterface;
use App\ResourceDelegates\Form\Item\Entry\Group\FieldOptionDelegate;
use App\Traits\Resource\FormEntryFieldResourceTrait;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\FormItemEntryGroupFieldOption;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Interfaces\SyncInterface;
use Core\Components\Resource\Requests\SyncRequest;
use Core\Components\Resource\Traits\SyncTrait;

class FieldOptionResource extends Resource implements SyncInterface, FormEntryFieldResourceInterface
{
    use FormEntryFieldResourceTrait;
    use SyncTrait;
    use UserActionTrackingTrait;

    public const FIELD_OPTION_SOURCE_STRUCTURE = 1;
    public const FIELD_OPTION_SOURCE_COMPANY = 2;

    protected $available_actions = (self::ACTION_GROUP_ENABLE_ALL | self::ACTION_SYNC) & ~(self::ACTION_GROUP_BATCH | self::ACTION_SEARCH);

    protected $table = 'formItemEntryGroupFieldOptions';
    protected $model = FormItemEntryGroupFieldOption::class;

    protected $generate_id = true;

    protected $allow_no_user = true;

    public static function getFieldOptionSources(): array
    {
        return [static::FIELD_OPTION_SOURCE_STRUCTURE, static::FIELD_OPTION_SOURCE_COMPANY];
    }

    public static function getFieldOptionSourceMap(): array
    {
        return [
            FormItemEntryGroupFieldOption::FIELD_OPTION_SOURCE_STRUCTURE => static::FIELD_OPTION_SOURCE_STRUCTURE,
            FormItemEntryGroupFieldOption::FIELD_OPTION_SOURCE_COMPANY => static::FIELD_OPTION_SOURCE_COMPANY
        ];
    }

    protected static function boot()
    {
        static::delegate(FieldOptionDelegate::class);
    }

    public function sync(Entity $entity)
    {
        return (new SyncRequest($this, $entity))
            ->scopeFields(['group_id', 'field_source', 'field_id', 'field_option_source'])
            ->syncedField('field_option_id');
    }

    public function deleteByGroupID($group_id, $force = false)
    {
        $group_id = $this->getFields()->get('group_id')->saveValue($group_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.formItemEntryGroupID', $group_id)
            ->get()
            ->each(function ($model) use ($primary_field, $force) {
                $this->delete(Entity::make([
                    $primary_field->getName() => $primary_field->outputValueFromModel($model)
                ]))->force($force)->findConfig(['check_mutability' => !$force])->run();
            });
    }
}
