<?php

namespace App\Resources\Form;

use App\ResourceDelegates\Form\ItemDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\FormItem;
use Core\Components\Resource\Classes\Resource;

class ItemResource extends Resource
{
    use UserActionTrackingTrait;

    public const OWNER_TYPE_SYSTEM = 1;
    public const OWNER_TYPE_COMPANY = 2;

    public const TYPE_BID = 1;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_SEARCH);

    protected $table = 'formItems';
    protected $model = FormItem::class;

    protected $allow_no_user = true;

    public static function getOwnerTypes()
    {
        return [
            static::OWNER_TYPE_SYSTEM, static::OWNER_TYPE_COMPANY
        ];
    }

    public static function getOwnerTypeMap()
    {
        return [
            FormItem::OWNER_TYPE_SYSTEM => static::OWNER_TYPE_SYSTEM,
            FormItem::OWNER_TYPE_COMPANY => static::OWNER_TYPE_COMPANY
        ];
    }

    public static function getTypes()
    {
        return [static::TYPE_BID];
    }

    public static function getTypeMap()
    {
        return [
            FormItem::TYPE_BID => self::TYPE_BID
        ];
    }

    protected static function boot()
    {
        static::delegate(ItemDelegate::class);
    }
}
