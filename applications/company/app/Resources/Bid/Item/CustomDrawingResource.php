<?php

declare(strict_types=1);

namespace App\Resources\Bid\Item;

use App\Interfaces\ResourceCompanyMediaInterface;
use App\ResourceDelegates\Bid\Item\CustomDrawingDelegate;
use App\Traits\Resource\{BulkActionTrait, CompanyMediaTrait, UserActionTrackingTrait};
use Common\Models\BidItemCustomDrawing;
use Core\Components\Resource\Classes\{Entity, Resource};
use Core\Components\Resource\Exceptions\{ImmutableEntityException, RequestFailedException};
use Exception;

/**
 * Class CustomDrawingResource
 *
 * @package App\Resources\Bid\Item
 */
class CustomDrawingResource extends Resource implements ResourceCompanyMediaInterface
{
    use BulkActionTrait;
    use CompanyMediaTrait;
    use UserActionTrackingTrait;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_BATCH | self::ACTION_SEARCH);

    /**
     * @var string Table name
     */
    protected $table = 'bidItemCustomDrawings';

    /**
     * @var string Model class name
     */
    protected $model = BidItemCustomDrawing::class;

    /**
     * @var bool Auto generate UUID for new entities
     */
    protected $generate_id = true;

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(CustomDrawingDelegate::class);
    }

    /**
     * Delete custom drawing by bid item id
     *
     * @param string $item_id Uuid
     * @param bool $force
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function deleteByItemID(string $item_id, bool $force = false): void
    {
        $item_id = $this->getFields()->get('item_id')->saveValue($item_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.bidItemID', $item_id)
            ->get()
            ->each(function ($custom_drawing) use ($primary_field, $force) {
                $id = $primary_field->outputValueFromModel($custom_drawing);
                try {
                    $this->delete(Entity::make([
                        $primary_field->getName() => $id
                    ]))->force($force)->run();
                } catch (ImmutableEntityException $e) {
                    throw $this->wrapBulkActionException(new ImmutableEntityException('Custom drawing is immutable'), $e, [
                        'bid_item_custom_drawing_id' => $id
                    ]);
                } catch (Exception $e) {
                    throw $this->wrapBulkActionException(new RequestFailedException('Unable to delete custom drawing'), $e, [
                        'bid_item_custom_drawing_id' => $id
                    ]);
                }
            });
    }
}
