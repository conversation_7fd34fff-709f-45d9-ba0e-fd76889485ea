<?php

declare(strict_types=1);

namespace App\Resources\Bid\Item;

use App\ResourceDelegates\Bid\Item\DrawingDelegate;
use App\Traits\Resource\{BulkActionTrait, UserActionTrackingTrait};
use Common\Models\BidItemDrawing;
use Core\Components\Resource\Classes\{Entity, Resource};
use Core\Components\Resource\Exceptions\{ImmutableEntityException, RequestFailedException};
use Core\Components\Resource\Interfaces\SyncInterface;
use Core\Components\Resource\Requests\SyncRequest;
use Core\Components\Resource\Traits\SyncTrait;
use Exception;

/**
 * Class DrawingResource
 *
 * @package App\Resources\Bid\Item
 */
class DrawingResource extends Resource implements SyncInterface
{
    use BulkActionTrait;
    use SyncTrait;
    use UserActionTrackingTrait;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = (self::ACTION_GROUP_ENABLE_ALL | self::ACTION_SYNC) & ~(self::ACTION_GROUP_BATCH | self::ACTION_SEARCH);

    /**
     * @var string Table name
     */
    protected $table = 'bidItemDrawings';

    /**
     * @var string Model class name
     */
    protected $model = BidItemDrawing::class;

    /**
     * @var bool Auto generate UUID for new entities
     */
    protected $generate_id = true;

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(DrawingDelegate::class);
    }

    /**
     * Determine if specified drawing is attached to a bid
     *
     * @param string $drawing_id
     * @return bool
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function isDrawingInUse(string $drawing_id): bool
    {
        $drawing_id = $this->getFields()->get('drawing_id')->saveValue($drawing_id);
        return $this->newScopedQuery()->where($this->getTableAlias() . '.drawingID', $drawing_id)->count() > 0;
    }

    /**
     * Create new sync request configured for this resource
     *
     * @param Entity $entity
     * @return SyncRequest
     */
    public function sync(Entity $entity): SyncRequest
    {
        return (new SyncRequest($this, $entity))
            ->scopeFields(['item_id'])
            ->syncedField('drawing_id');
    }

    /**
     * Delete drawing by bid item id
     *
     * @param string $item_id Uuid
     * @param bool $force
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function deleteByItemID(string $item_id, bool $force = false): void
    {
        $item_id = $this->getFields()->get('item_id')->saveValue($item_id);
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.bidItemID', $item_id)
            ->get()
            ->each(function ($drawing) use ($primary_field, $force) {
                $id = $primary_field->outputValueFromModel($drawing);
                try {
                    $this->delete(Entity::make([
                        $primary_field->getName() => $id
                    ]))->force($force)->run();
                } catch (ImmutableEntityException $e) {
                    throw $this->wrapBulkActionException(new ImmutableEntityException('Drawing is immutable'), $e, [
                        'bid_item_drawing_id' => $id
                    ]);
                } catch (Exception $e) {
                    throw $this->wrapBulkActionException(new RequestFailedException('Unable to delete drawing'), $e, [
                        'bid_item_drawing_id' => $id
                    ]);
                }
            });
    }
}
