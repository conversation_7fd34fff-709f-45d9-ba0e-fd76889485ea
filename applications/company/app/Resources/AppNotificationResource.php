<?php

declare(strict_types=1);

namespace App\Resources;

use App\ResourceDelegates\AppNotificationDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Core\Components\Resource\Classes\Resource;
use Common\Models\AppNotification;

/**
 * Class AppNotificationResource
 *
 * @package App\Resources
 */
class AppNotificationResource extends Resource
{
    use UserActionTrackingTrait;

    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 2;

    const PLACEMENT_GLOBAL = 1;

    const PLACEMENT_NOTIFICATION_CENTER = 2;

    const PLACEMENT_BANNER = 3;

    const TYPE_INFO = 1;
    const TYPE_MAINTENANCE = 2;
    const TYPE_ERROR = 3;

    const ASSOCIATION_TYPE_CUSTOMER = 1;
    const ASSOCIATION_TYPE_PROPERTY = 2;
    const ASSOCIATION_TYPE_PROJECT = 3;
    const ASSOCIATION_TYPE_LEAD = 4;
    const ASSOCIATION_TYPE_TASK = 5;
    const ASSOCIATION_TYPE_BID = 6;

    const INTENT_KEY_WISETACK_APPLICATION_COMPLETED = 1000;


    /**
     * @var string Table name
     */
    protected $table = 'appNotifications';

    /**
     * @var string Model class name
     */
    protected $model = AppNotification::class;

    protected $generate_id = true;
    protected $allow_no_user = false;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED);


    /**
     * Get available statuses
     *
     * @return array
     */
    public static function getStatuses(): array
    {
        return [static::STATUS_ACTIVE, static::STATUS_INACTIVE];
    }

    public static function getPlacements(): array
    {
        return [static::PLACEMENT_GLOBAL, static::PLACEMENT_NOTIFICATION_CENTER, static::PLACEMENT_BANNER];
    }

    public static function getTypes(): array
    {
        return [static::TYPE_INFO, static::TYPE_MAINTENANCE, static::TYPE_ERROR];
    }


    /**
     * Get available association types
     *
     * @return array
     */
    public static function getAssociationTypes(): array
    {
        return [
            static::ASSOCIATION_TYPE_CUSTOMER, static::ASSOCIATION_TYPE_PROPERTY, static::ASSOCIATION_TYPE_PROJECT,
            static::ASSOCIATION_TYPE_LEAD, static::ASSOCIATION_TYPE_TASK, static::ASSOCIATION_TYPE_BID,
        ];
    }

    public static function getAssociationTypeMap(): array
    {
        return [
            AppNotification::ASSOCIATION_TYPE_CUSTOMER       => static::ASSOCIATION_TYPE_CUSTOMER,
            AppNotification::ASSOCIATION_TYPE_PROPERTY       => static::ASSOCIATION_TYPE_PROPERTY,
            AppNotification::ASSOCIATION_TYPE_PROJECT        => static::ASSOCIATION_TYPE_PROJECT,
            AppNotification::ASSOCIATION_TYPE_LEAD           => static::ASSOCIATION_TYPE_LEAD,
            AppNotification::ASSOCIATION_TYPE_TASK           => static::ASSOCIATION_TYPE_TASK,
            AppNotification::ASSOCIATION_TYPE_BID            => static::ASSOCIATION_TYPE_BID,
        ];
    }

    /**
     * Get available association type names
     *
     * @return array
     */
    public static function getAssociationTypeNames(): array
    {
        return [
            AppNotification::ASSOCIATION_TYPE_CUSTOMER => 'Customer',
            AppNotification::ASSOCIATION_TYPE_PROPERTY => 'Property',
            AppNotification::ASSOCIATION_TYPE_PROJECT => 'Project',
            AppNotification::ASSOCIATION_TYPE_LEAD => 'Lead',
            AppNotification::ASSOCIATION_TYPE_TASK => 'Task',
            AppNotification::ASSOCIATION_TYPE_BID => 'Bid',
        ];
    }


    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(AppNotificationDelegate::class);
    }
}
