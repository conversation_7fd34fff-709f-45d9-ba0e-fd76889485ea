<?php

namespace App\Resources;

use App\ResourceDelegates\ContentPartialDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\ContentPartial;
use Core\Components\Resource\Classes\Resource;

class ContentPartialResource extends Resource
{
    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED | self::ACTION_SEARCH);

    protected $table = 'contentPartials';
    protected $model = ContentPartial::class;

    protected $generate_id = true;
    protected $allow_no_user = true;

    protected static function boot()
    {
        static::delegate(ContentPartialDelegate::class);
    }

    public function isAliasInUse($company_id, $alias)
    {
        return $this->newQuery()->where('companyID', $company_id)->where('alias', $alias)->count() !== 0;
    }

    public function getIdsFromAliases($company_id, array $aliases)
    {
        $primary_field = $this->getPrimaryField();
        $ids = [];
        $this->newQuery()
            ->where('companyID', $company_id)
            ->whereIn('alias', $aliases)
            ->get([
                $primary_field->getColumn($this->getTableAlias(), true, false),
                'alias'
            ])
            ->each(function ($item) use ($primary_field, &$ids) {
                $ids[$item->alias] = $primary_field->outputValueFromModel($item);
            });
        return $ids;
    }
}
