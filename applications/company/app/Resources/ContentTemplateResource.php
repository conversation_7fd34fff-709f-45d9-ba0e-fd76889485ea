<?php

namespace App\Resources;

use App\ResourceDelegates\ContentTemplateDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\ContentTemplate;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Validation\Classes\FieldConfig;

class ContentTemplateResource extends Resource
{
    const TYPE_BID_COVER = 1;
    const TYPE_BID_INTRO = 2;
    const TYPE_BID_SECTIONS = 3;
    const TYPE_BID_LINE_ITEMS = 4;
    const TYPE_BID_TERMS_CONDITIONS = 11;
    const TYPE_BID_IMAGES = 5;
    const TYPE_BID_MEDIA = 10;
    const TYPE_SCOPE_OF_WORK_PROJECT_INFO = 6;
    const TYPE_SCOPE_OF_WORK_MATERIALS_LIST = 7;
    const TYPE_SCOPE_OF_WORK_SECTIONS = 8;
    const TYPE_SCOPE_OF_WORK_IMAGES = 9;
    const TYPE_SCOPE_OF_WORK_TERMS_CONDITIONS = 12;

    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED);

    protected $table = 'contentTemplates';
    protected $model = ContentTemplate::class;

    protected $generate_id = true;

    protected $allow_no_user = true;

    public static function getTypes()
    {
        return [
            static::TYPE_BID_COVER, static::TYPE_BID_INTRO, static::TYPE_BID_SECTIONS,
            static::TYPE_BID_LINE_ITEMS, static::TYPE_BID_TERMS_CONDITIONS, static::TYPE_BID_IMAGES,
            static::TYPE_BID_MEDIA, static::TYPE_SCOPE_OF_WORK_PROJECT_INFO, static::TYPE_SCOPE_OF_WORK_MATERIALS_LIST,
            static::TYPE_SCOPE_OF_WORK_SECTIONS, static::TYPE_SCOPE_OF_WORK_IMAGES,
            static::TYPE_SCOPE_OF_WORK_TERMS_CONDITIONS
        ];
    }

    protected static function boot()
    {
        static::delegate(ContentTemplateDelegate::class);
    }

    public function isAliasInUse($company_id, $alias)
    {
        return $this->newQuery()->where('companyID', $company_id)->where('alias', $alias)->count() !== 0;
    }

    public function getDefaultsByType($types)
    {
        $company_id = $this->acl()->companyID();
        if (!is_array($types)) {
            $types = [$types];
        }
        $table_alias = $this->getTableAlias();
        $templates = $this->newQuery()
            ->where("{$table_alias}.companyID", $company_id)
            ->whereIn("{$table_alias}.type", $types)
            ->where("{$table_alias}.isDefault", 1)
            ->get(['contentTemplateID', 'type']);

        $primary_field = $this->getPrimaryField();
        $defaults = [];
        foreach ($templates as $template) {
            $defaults[$template->type] = $primary_field->outputValueFromModel($template);
        }
        return $defaults;
    }

    public function removeDefault($company_id, $type)
    {
        $primary_field = $this->getPrimaryField();
        $table_alias = $this->getTableAlias();
        $default_template = $this->newQuery()
            ->where("{$table_alias}.companyID", $company_id)
            ->where("{$table_alias}.type", $type)
            ->where("{$table_alias}.isDefault", 1)
            ->first();
        if ($default_template === null) {
            return;
        }
        $this->partialUpdate(Entity::make([
            $primary_field->getName() => $primary_field->outputValueFromModel($default_template),
            'is_default' => false
        ]))
            ->attach('validation_field_config', function (FieldConfig $config) {
                $config->store('check_default', false);
                return $config;
            })
            ->run();
    }
}
