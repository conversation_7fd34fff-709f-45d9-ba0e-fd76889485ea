<?php

declare(strict_types=1);

namespace App\Resources;

use App\ResourceDelegates\RegistrationDelegate;
use Common\Models\Registration;
use Core\Components\Resource\Classes\Resource;

class RegistrationResource extends Resource
{
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~self::ACTION_GROUP_NESTED;

    protected $table = 'registrations';
    protected $model = Registration::class;

    protected $generate_id = true;

    protected static function boot(): void
    {
        static::delegate(RegistrationDelegate::class);
    }
}
