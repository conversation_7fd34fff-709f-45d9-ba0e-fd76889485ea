<?php

namespace App\Resources\Product;

use App\ResourceDelegates\Product\CategoryDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\ProductCategory;
use Core\Components\Resource\Classes\Collection;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;

class CategoryResource extends Resource
{
    const OWNER_TYPE_MANUFACTURER = 1;
    const OWNER_TYPE_COMPANY = 2;

    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_GROUP_NESTED);

    protected $table = 'productCategories';
    protected $model = ProductCategory::class;

    protected $generate_id = true;

    protected $allow_no_user = true;

    public static function getOwnerTypes()
    {
        return [
            static::OWNER_TYPE_MANUFACTURER, static::OWNER_TYPE_COMPANY
        ];
    }

    public static function getOwnerTypeMap()
    {
        return [
            ProductCategory::OWNER_MANUFACTURER => static::OWNER_TYPE_MANUFACTURER,
            ProductCategory::OWNER_COMPANY => static::OWNER_TYPE_COMPANY
        ];
    }

    public static function getStatuses()
    {
        return [static::STATUS_ACTIVE, static::STATUS_ARCHIVED];
    }

    protected static function boot()
    {
        static::delegate(CategoryDelegate::class);
    }

    public function isAliasInUse($owner_type, $owner_id, $alias)
    {
        $owner_type = array_search($owner_type, static::getOwnerTypeMap());

        $count = $this->newQuery()
            ->where('ownerType', $owner_type)
            ->where('ownerID', $owner_id)
            ->where('alias', $alias)
            ->count();
        return $count !== 0;
    }

    public function getIdsFromAliases($owner_type, $owner_id, array $aliases)
    {
        $owner_type = array_search($owner_type, static::getOwnerTypeMap());

        $primary_field = $this->getPrimaryField();
        $ids = [];
        $this->newQuery()
            ->where('ownerType', $owner_type)
            ->where('ownerID', $owner_id)
            ->whereIn('alias', $aliases)
            ->get([
                $primary_field->getColumn($this->getTableAlias(), true, false),
                'alias'
            ])
            ->each(function ($category) use ($primary_field, &$ids) {
                $ids[$category->alias] = $primary_field->outputValueFromModel($category);
            });
        return $ids;
    }

    public function getList(Collection $collection)
    {
        $categories = [];
        $roots = [];
        foreach ($collection as $category) {
            if (!isset($categories[$category['id']])) {
                $categories[$category['id']] = [
                    'children' => []
                ];
            }
            $categories[$category['id']]['entity'] = $category;
            if ($category['parent_id'] === null) {
                $roots[] = $category['id'];
            } else {
                if (!isset($categories[$category['parent_id']])) {
                    $categories[$category['parent_id']] = [
                        'children' => []
                    ];
                }
                $categories[$category['parent_id']]['children'][] = $category['id'];
            }
        }

        return new Collection([
            'all' => $categories,
            'root' => $roots
        ]);
    }

    public function getNestedList(Collection $collection)
    {
        $collection = $this->getList($collection);

        $build_list = function ($category) use (&$build_list, $collection) {
            $entity = $category['entity'];

            $item_data = [
                'id' => $entity->id,
                'parent_id' => $entity->parent_id,
                'name' => $entity->name,
                'items_count' => $entity->items_count,
                'forms' => $entity->forms,
                'categories' => []
            ];

            if (count($category['children']) > 0) {
                foreach ($category['children'] as $child) {
                    $item_data['categories'][] = $build_list($collection['all'][$child]);
                }
            }

            return $item_data;
        };

        $new_collection = [];
        foreach ($collection['root'] as $root_id) {
            $new_collection[] = $build_list($collection['all'][$root_id]);
        }

        return new Collection($new_collection);
    }

    /**
     * Determine if a category has active children
     *
     * @param string $id
     */
    public function hasActiveChildren($id)
    {
        $active_children = $this->newScopedQuery()
            ->where('parentProductCategoryID', $this->getFields()->get('parent_id')->saveValue($id))
            ->where('status', ProductCategory::STATUS_ACTIVE)
            ->count();
        return $active_children > 0;
    }

    public function archiveChildrenByID($id)
    {
        $primary_field = $this->getPrimaryField();
        $id = $primary_field->saveValue($id);
        $this->newScopedQuery()
            ->where('productCategories.parentProductCategoryID', $id)
            ->each(function ($category) use ($primary_field) {
                $this->partialUpdate(Entity::make([
                    $this->getPrimaryFieldName() => $primary_field->outputValueFromModel($category),
                    'status' => static::STATUS_ARCHIVED
                ]))->run();
            });
    }

    public function deleteChildrenByID($id)
    {
        $primary_field = $this->getPrimaryField();
        $id = $primary_field->saveValue($id);
        $this->newScopedQuery()
            ->where('productCategories.parentProductCategoryID', $id)
            ->each(function ($category) use ($primary_field) {
                $this->delete(Entity::make([
                    $this->getPrimaryFieldName() => $primary_field->outputValueFromModel($category)
                ]))->run();
            });
    }
}
