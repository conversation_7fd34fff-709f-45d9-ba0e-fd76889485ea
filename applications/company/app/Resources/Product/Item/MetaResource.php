<?php

declare(strict_types=1);

namespace App\Resources\Product\Item;

use App\ResourceDelegates\Product\Item\MetaDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\ProductItemMeta;
use Core\Components\Resource\Classes\{Entity, Resource};
use Core\Components\Resource\Interfaces\SaveInterface;
use Core\Components\Resource\Requests\SaveRequest;
use Core\Components\Resource\Traits\SaveTrait;

/**
 * Class MetaResource
 *
 * @package App\Resources\Product\Item
 */
class MetaResource extends Resource implements SaveInterface
{
    use SaveTrait;
    use UserActionTrackingTrait;

    public const VALUE_TYPE_STRING = 1;
    public const VALUE_TYPE_BOOL = 2;
    public const VALUE_TYPE_INT = 3;
    public const VALUE_TYPE_FLOAT = 4;
    public const VALUE_TYPE_MULTILINE_STRING = 5;
    public const VALUE_TYPE_PRODUCT_ITEM_ID = 6;

    /**
     * @var int Actions which are allowed
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL;

    /**
     * @var string Table name
     */
    protected $table = 'productItemMeta';

    /**
     * @var string Model associated with resource
     */
    protected $model = ProductItemMeta::class;

    /**
     * @var bool Auto generate UUID for new entities
     */
    protected $generate_id = true;

    /**
     * @var bool Determines if resource can be used without user info
     */
    protected $allow_no_user = true;

    /**
     * Get available value types
     *
     * @return int[]
     */
    public static function getValueTypes(): array
    {
        return [
            static::VALUE_TYPE_STRING, static::VALUE_TYPE_BOOL, static::VALUE_TYPE_INT, static::VALUE_TYPE_FLOAT,
            static::VALUE_TYPE_MULTILINE_STRING, static::VALUE_TYPE_PRODUCT_ITEM_ID
        ];
    }

    /**
     * Boot resource
     */
    protected static function boot(): void
    {
        static::delegate(MetaDelegate::class);
    }

    /**
     * Save entity by product item id and name fields
     *
     * @param Entity $entity
     * @return SaveRequest
     */
    public function save(Entity $entity): SaveRequest
    {
        return (new SaveRequest($this, $entity))
            ->lookupFields(['product_item_id', 'name']);
    }

    /**
     * Convert value to query ready version based on type
     *
     * @param int $type
     * @param string|int|float|bool|null $value
     * @return string|null
     */
    public static function getQueryValue(int $type, string|int|float|bool|null $value): ?string
    {
        if ($value === null) {
            return null;
        }
        return match ($type) {
            static::VALUE_TYPE_INT, static::VALUE_TYPE_FLOAT => (string) $value,
            static::VALUE_TYPE_BOOL => $value === true ? '1' : '0',
            default => $value
        };
    }

    /**
     * Convert value to output ready version based on type
     *
     * @param int $type
     * @param string|null $value
     * @return string|int|float|bool|null
     */
    public static function getOutputValue(int $type, ?string $value): string|int|float|bool|null
    {
        if ($value === null) {
            return null;
        }
        return match ($type) {
            static::VALUE_TYPE_STRING, static::VALUE_TYPE_MULTILINE_STRING, static::VALUE_TYPE_PRODUCT_ITEM_ID => $value,
            static::VALUE_TYPE_INT => (int) $value,
            static::VALUE_TYPE_FLOAT => (float) $value,
            static::VALUE_TYPE_BOOL => $value === '1',
        };
    }

    /**
     * Determine if meta name is in use for product
     *
     * @param string $item_id
     * @param string $name
     * @return bool
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function isNameInUse(string $item_id, string $name): bool
    {
        $item_id = $this->getFields()->get('item_id')->saveValue($item_id);
        $count = $this->newQuery()
            ->where('productItemID', $item_id)
            ->where('name', $name)
            ->count();
        return $count !== 0;
    }

    /**
     * Delete all meta which doesn't exist in the passed ids
     *
     * @param string $item_id
     * @param array $meta_ids
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function deleteMissingMetaByItemID(string $item_id, array $meta_ids): void
    {
        $primary_field = $this->getPrimaryField();
        $item_id = $this->getFields()->get('item_id')->saveValue($item_id);
        $meta_ids = array_map(fn(string $id): string => $primary_field->saveValue($id), $meta_ids);
        $this->newScopedQuery()
            ->where('productItemMeta.productItemID', $item_id)
            ->whereNotIn('productItemMeta.productItemMetaID', $meta_ids)
            ->each(function (ProductItemMeta $meta) use ($primary_field): void {
                $this->delete(Entity::make([
                    $primary_field->getName() => $primary_field->outputValueFromModel($meta)
                ]))->run();
            });
    }
}
