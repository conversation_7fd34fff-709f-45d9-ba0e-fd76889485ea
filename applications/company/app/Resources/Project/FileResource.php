<?php

declare(strict_types=1);

namespace App\Resources\Project;

use App\Interfaces\ResourceCompanyMediaInterface;
use App\ResourceDelegates\Project\FileDelegate;
use App\Traits\Resource\{BulkActionTrait, CompanyMediaTrait, UserActionTrackingTrait};
use Common\Models\ProjectFile;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Resource\Exceptions\RequestFailedException;
use Throwable;

/**
 * Class FileResource
 *
 * @package App\Resources\Project
 */
class FileResource extends Resource implements ResourceCompanyMediaInterface
{
    use BulkActionTrait;
    use CompanyMediaTrait;
    use UserActionTrackingTrait;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_SEARCH | self::ACTION_GROUP_BATCH);

    /**
     * @var string Table name
     */
    protected $table = 'projectFiles';

    /**
     * @var string Model class name
     */
    protected $model = ProjectFile::class;

    /**
     * @var bool Determines if UUID is generated automatically
     */
    protected $generate_id = true;

    /**
     * @var bool Determines if an authenticated user is required for resource
     */
    protected $allow_no_user = true;

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(FileDelegate::class);
    }

    /**
     * Delete all files by project ID
     *
     * @param int $project_id
     * @param bool $force
     */
    public function deleteByProjectID(int $project_id, bool $force = false): void
    {
        $primary_field = $this->getPrimaryField();
        $this->newScopedQuery()
            ->where($this->getTableAlias() . '.projectID', $project_id)
            ->get()
            ->each(function ($file) use ($primary_field, $force) {
                $id = $primary_field->outputValueFromModel($file);
                try {
                    $this->delete(Entity::make([
                        $primary_field->getName() => $id
                    ]))->force($force)->run();
                } catch (ImmutableEntityException $e) {
                    throw $this->wrapBulkActionException(new ImmutableEntityException('File is immutable'), $e, [
                        'project_file_id' => $id
                    ]);
                } catch (Throwable $e) {
                    throw $this->wrapBulkActionException(new RequestFailedException('Unable to delete file'), $e, [
                        'project_file_id' => $id
                    ]);
                }
            });
    }
}
