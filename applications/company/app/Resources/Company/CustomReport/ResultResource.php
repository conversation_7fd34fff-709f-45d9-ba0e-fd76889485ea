<?php

declare(strict_types=1);

namespace App\Resources\Company\CustomReport;

use App\Classes\Acl;
use App\Classes\Log;
use App\Interfaces\ResourceCompanyMediaInterface;
use App\ResourceDelegates\Company\CustomReport\ResultDelegate;
use App\Traits\Resource\CompanyMediaTrait;
use Carbon\Carbon;
use Common\Models\CompanyCustomReportResult;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;
use Throwable;

/**
 * Class ResultResource
 *
 * @package App\Resources\Company\CustomReport
 */
class ResultResource extends Resource implements ResourceCompanyMediaInterface
{
    use CompanyMediaTrait;

    public const STATUS_GENERATING = 1;
    public const STATUS_GENERATED = 2;
    public const STATUS_EXPIRED = 3;
    public const STATUS_FAILED = 4;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~self::ACTION_GROUP_NESTED;

    /**
     * @var string Table name
     */
    protected $table = 'companyCustomReportResults';

    /**
     * @var string Model class name
     */
    protected $model = CompanyCustomReportResult::class;

    /**
     * @var bool Determines if an authenticated user is required for resource
     */
    protected $allow_no_user = true;

    /**
     * Get available statuses
     *
     * @return int[]
     */
    public static function getStatuses(): array
    {
        return [static::STATUS_GENERATING, static::STATUS_GENERATED, static::STATUS_EXPIRED, static::STATUS_FAILED];
    }

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot()
    {
        static::delegate(ResultDelegate::class);
    }

    /**
     * Clear out expired report results older than specified days
     *
     * @param int $days
     * @param Carbon|null $now
     * @return int
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     */
    public static function clearExpired(int $days = 30, Carbon $now = null): int
    {
        $now ??= Carbon::now('UTC');
        $results = CompanyCustomReportResult::query()
            ->join('companyCustomReports', 'companyCustomReports.companyCustomReportID', '=', 'companyCustomReportResults.companyCustomReportID')
            ->where('companyCustomReportResults.status', CompanyCustomReportResult::STATUS_GENERATED)
            ->where('companyCustomReportResults.generatedAt', '<', $now->subDays($days))
            ->get(['companyCustomReportResults.companyCustomReportResultID', 'companyCustomReports.companyID']);
        if (count($results) === 0) {
            return 0;
        }
        $acl = Acl::make();
        $resource = new static($acl);
        $primary_field = $resource->getPrimaryField();
        $c = 0;
        $log = Log::create('clear_expired', [
            'file' => 'company_custom_reports.log'
        ]);
        foreach ($results as $result) {
            try {
                $acl->setCompanyID($result->companyID);
                $resource->partialUpdate(Entity::make([
                    'id' => $primary_field->outputValue($result->companyCustomReportResultID),
                    'status' => self::STATUS_EXPIRED
                ]))->run();
                $c++;
            } catch (Throwable $e) {
                $log->error('Unable to update expired report', [
                    'exception' => $e,
                    'id' => $primary_field->outputValue($result->companyCustomReportResultID)
                ]);
            }
        }
        $log->info("Removed {$c} expired result files");
        return $c;
    }
}
