<?php

namespace App\Resources\Company\Invoice;

use App\ResourceDelegates\Company\Invoice\LineItemDelegate;
use Common\Models\CompanyInvoiceLineItem;
use Core\Components\Resource\Classes\Resource;

class LineItemResource extends Resource
{
    const TYPE_GENERAL = 1;
    const TYPE_ADD_ON = 2;
    const TYPE_DISCOUNT = 3;
    const TYPE_FEE = 4;
    const TYPE_CREDIT = 5;
    const TYPE_SUBSCRIPTION = 6;
    const TYPE_SUBSCRIPTION_ADD_ON = 7;
    const TYPE_SUBSCRIPTION_DISCOUNT = 8;
    const TYPE_SUBSCRIPTION_FEE = 9;

    protected $available_actions = self::ACTION_GROUP_READ_ONLY_FULL | self::ACTION_BATCH_NESTED_CREATE | self::ACTION_NESTED_CREATE;

    protected $table = 'companyInvoiceLineItems';
    protected $model = CompanyInvoiceLineItem::class;

    public static function getTypes()
    {
        return [
            static::TYPE_GENERAL, static::TYPE_ADD_ON, static::TYPE_DISCOUNT, static::TYPE_FEE, static::TYPE_CREDIT,
            static::TYPE_SUBSCRIPTION, static::TYPE_SUBSCRIPTION_ADD_ON, static::TYPE_SUBSCRIPTION_DISCOUNT,
            static::TYPE_SUBSCRIPTION_FEE
        ];
    }

    protected static function boot()
    {
        static::delegate(LineItemDelegate::class);
    }
}
