<?php

namespace App\Resources\Company;

use App\ResourceDelegates\Company\AchPaymentMethodDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\CompanyAchPaymentMethod;
use Core\Components\Resource\Classes\Resource;

class AchPaymentMethodResource extends Resource
{
    const ACCOUNT_TYPE_CHECKING = 1;
    const ACCOUNT_TYPE_SAVINGS = 2;
    const ACCOUNT_TYPE_BUSINESS_CHECKING = 3;

    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_CREATE | self::ACTION_DELETE;

    protected $table = 'companyAchPaymentMethods';
    protected $model = CompanyAchPaymentMethod::class;

    protected $allow_no_user = true;

    public static function getAccountTypes()
    {
        return [static::ACCOUNT_TYPE_CHECKING, static::ACCOUNT_TYPE_SAVINGS, static::ACCOUNT_TYPE_BUSINESS_CHECKING];
    }

    protected static function boot()
    {
        static::delegate(AchPaymentMethodDelegate::class);
    }
}
