<?php

namespace App\Resources\User;

use App\ResourceDelegates\User\PhoneDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\UserPhone;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;
use Core\Components\Validation\Classes\FieldConfig;

class PhoneResource extends Resource
{
    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_SEARCH);

    protected $table = 'userPhone';
    protected $model = UserPhone::class;

    protected $allow_no_user = true;

    protected static function boot()
    {
        static::delegate(PhoneDelegate::class);
    }

    public function removePrimary($user_id)
    {
        $primary_phone = $this->newScopedQuery()->where('userPhone.userID', $user_id)->where('userPhone.isPrimary', 1)->first();
        // this can return null during a batch update when a previous request might of already removed the current primary
        // batch validation rules should prevent any issues with it allowing a user to be left without a primary phone
        if ($primary_phone === null) {
            return;
        }
        $this->update(Entity::make([
            $this->getPrimaryFieldName() => $primary_phone->getKey(),
            'is_primary' => false
        ]))
            ->partial()
            ->attach('validation_field_config', function (FieldConfig $config) {
                $config->store('check_primary', false);
                return $config;
            })
            ->run();
    }

    public function deleteMissingPhonesByUserID($user_id, array $phone_ids)
    {
        $this->newScopedQuery()->where('userPhone.userID', $user_id)->whereNotIn('userPhone.userPhoneID', $phone_ids)->each(function ($phone) {
            $this->delete(Entity::make([
                $this->getPrimaryFieldName() => $phone->getKey()
            ]))
                ->force()
                ->run();
        });
    }
}
