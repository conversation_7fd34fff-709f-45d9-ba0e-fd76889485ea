<?php

declare(strict_types=1);

namespace App\Services;

use App\Classes\Template;
use App\Services\Training\Exceptions\ModuleNotFoundException;
use App\Services\Training\Exceptions\TrainingServiceException;
use App\Services\Training\Jobs\CompleteActionJob;
use Carbon\Carbon;
use Common\Models\{
    Company,
    CompanyTrainingSectionModule,
    CompanyTrainingSectionModuleAction,
    TrainingContent,
    TrainingSection,
    TrainingSectionModule,
    TrainingSectionModuleUser,
    TrainingSectionModuleUserAction,
    User
};
use Core\Components\Cache\StaticAccessors\Cache;
use Core\Components\DB\StaticAccessors\DB;

/**
 * Class TrainingService
 *
 * @package App\Services
 */
class TrainingService
{
    /**
     * @var string Prefix for all cache keys and tags
     */
    protected static string $cache_id = 'training_hub';

    /**
     * Get prefixed cache key built from method arguments
     *
     * @param ...$parts
     * @return string
     */
    protected static function getCacheKey(...$parts): string
    {
        return implode('.', [self::$cache_id, ...$parts]);
    }

    /**
     * Prefix all tags with main cache id
     *
     * @param array $tags
     * @return array
     */
    protected static function prefixCacheTags(array $tags): array
    {
        return array_map(fn(string $tag): string => self::$cache_id . ".{$tag}", $tags);
    }

    /**
     * Get tag list for caching with global tag and prefixed tags for each method argument
     *
     * @param ...$tags
     * @return array
     */
    protected static function getCacheTags(...$tags): array
    {
        return [self::$cache_id, ...self::prefixCacheTags($tags)];
    }

    /**
     * Assign any available company type training modules to company
     *
     * @param Company $company
     */
    public static function assignCompanyModules(Company $company): void
    {
        $company_id = $company->getKey();

        /** @var TrainingSectionModule[] $modules */
        $modules = TrainingSectionModule::query()
            ->where('accessLevel', TrainingSectionModule::ACCESS_LEVEL_COMPANY)
            ->where('status', TrainingSectionModule::STATUS_ACTIVE)
            ->with(['actions'])
            ->get(['trainingSectionModuleID']);
        foreach ($modules as $module) {
            /** @var CompanyTrainingSectionModule $company_module */
            $company_module = CompanyTrainingSectionModule::create([
                'companyID' => $company_id,
                'trainingSectionModuleID' => $module->getKey(),
                'isCompleted' => false
            ]);
            $actions = $module->actions->pluck('trainingActionID')->all();
            foreach ($actions as $action_id) {
                $company_module->actions()->create([
                    'companyTrainingSectionModuleID' => $company_module->getKey(),
                    'trainingActionID' => $action_id,
                    'isCompleted' => false
                ]);
            }
        }
    }

    /**
     * Assign any user level training modules which match their access level to user
     *
     * @param User $user
     */
    public static function assignUserModules(User $user): void
    {
        $user_id = $user->getKey();

        $access_level = [
            TrainingSectionModule::ACCESS_LEVEL_ALL_USERS,
            $user->primary ? TrainingSectionModule::ACCESS_LEVEL_PRIMARY_USER : TrainingSectionModule::ACCESS_LEVEL_NON_PRIMARY_USER
        ];

        /** @var TrainingSectionModule[] $modules */
        $modules = TrainingSectionModule::query()
            ->where('accessLevel', $access_level)
            ->where('status', TrainingSectionModule::STATUS_ACTIVE)
            ->with(['actions'])
            ->get(['trainingSectionModuleID']);
        foreach ($modules as $module) {
            /** @var TrainingSectionModuleUser $company_module */
            $user_module = TrainingSectionModuleUser::create([
                'trainingSectionModuleID' => $module->getKey(),
                'userID' => $user_id,
                'isCompleted' => false
            ]);
            $actions = $module->actions->pluck('trainingActionID')->all();
            foreach ($actions as $action_id) {
                $user_module->actions()->create([
                    'trainingSectionModuleUserID' => $user_module->getKey(),
                    'trainingActionID' => $action_id,
                    'isCompleted' => false
                ]);
            }
        }
    }

    /**
     * Determines if a user is currently has training enabled
     *
     * Centralized this check to one place so we can modify it in the future if needed.
     *
     * @param User|null $user
     * @return bool
     */
    public static function isUserInTraining(?User $user): bool
    {
        return $user !== null && $user->isTraining;
    }

    /**
     * If user is in training, enqueue a complete action job for the specified user and actions
     *
     * @param User|null $user
     * @param int|array $action
     */
    public static function queueCompleteUserAction(?User $user, int|array $action): void
    {
        if (!self::isUserInTraining($user)) {
            return;
        }
        CompleteActionJob::enqueue($user->getKey(), $action);
    }

    /**
     * Clear all training related caches
     */
    public static function clearAllCaches(): void
    {
        Cache::tags(self::$cache_id)->flush();
    }

    /**
     * Clear static caches
     */
    public static function clearStaticCaches(): void
    {
        Cache::tags(self::prefixCacheTags(['addons', 'brand_content', 'module']))->flush();
    }

    /**
     * Clear all user caches
     */
    public static function clearUserCaches(): void
    {
        Cache::tags(self::prefixCacheTags(['user_content', 'user_sections']))->flush();
    }

    /**
     * TrainingService Constructor
     *
     * @param User $user
     */
    public function __construct(protected User $user)
    {}

    /**
     * Determines if user has training enabled
     *
     * @return bool
     */
    public function isTraining(): bool
    {
        return self::isUserInTraining($this->user);
    }

    /**
     * Get and cache static content for a specific brand
     *
     * @param int $brand_id
     * @return array
     * @throws TrainingServiceException
     */
    protected function getBrandContent(int $brand_id): array
    {
        return Cache::tags(self::getCacheTags('brand_content'))
            ->remember(self::getCacheKey('brand_content', $brand_id), 604800, function () use ($brand_id): array {
                $content = TrainingContent::query()
                    ->where(fn(object $query): object => $query->where('brandID', $brand_id)->orWhereNull('brandID'))
                    ->get();
                $defaults = [];
                $branded = [];
                foreach ($content as $item) {
                    if ($item->brandID === null) {
                        if (isset($defaults[$item->name])) {
                            throw new TrainingServiceException('Multiple default values provided for %s', $item->name);
                        }
                        $defaults[$item->name] = $item->content;
                        continue;
                    }
                    if (isset($branded[$item->name])) {
                        throw new TrainingServiceException('Multiple values provided for %s for brand %d', $item->name, $brand_id);
                    }
                    $branded[$item->name] = $item->content;
                }
                foreach ($defaults as $name => $content) {
                    if (isset($branded[$name])) {
                        continue;
                    }
                    $branded[$name] = $content;
                }
                return $branded;
            });
    }

    /**
     * Get and cache content specific to user
     *
     * @param DomainService $domain_service
     * @return array
     * @throws TrainingServiceException
     * @throws \Core\Exceptions\AppException
     */
    public function getContent(DomainService $domain_service): array
    {
        $brand_id = $domain_service->findByCompanyID($this->user->companyID)->brandID;

        return Cache::tags(self::getCacheTags('user_content'))
            ->remember(self::getCacheKey('user_content', $this->user->getKey()), 3600, function () use ($brand_id): array {
                $content = $this->getBrandContent($brand_id);
                $replacements = [
                    'first_name' => $this->user->userFirstName,
                    'last_name' => $this->user->userLastName
                ];
                return array_map(fn(?string $content) => Template::replace($content, $replacements, '{{', '}}'), $content);
            });
    }

    /**
     * Get and cache all section data specific to user
     *
     * @return array
     */
    public function getSections(): array
    {
        return Cache::tags(self::getCacheTags('user_sections'))
            ->remember(self::getCacheKey('user_sections', $this->user->getKey()), 3600, function (): array {
                $columns = [
                    'trainingSectionModules.trainingSectionModuleID', 'trainingSectionModules.trainingSectionID',
                    'trainingSectionModules.title', 'trainingSectionModules.cardTitle', 'trainingSectionModules.cardIcon',
                    'trainingSectionModules.alias', 'trainingSectionModules.isRequired', 'trainingSectionModules.isHidden',
                    'trainingSectionModules.order'
                ];

                $module_query = TrainingSectionModuleUser::query()
                    ->select($columns)
                    ->join(
                        'trainingSectionModules',
                        'trainingSectionModules.trainingSectionModuleID',
                        '=',
                        'trainingSectionModulesUsers.trainingSectionModuleID'
                    )
                    ->where('trainingSectionModulesUsers.userID', $this->user->getKey())
                    ->where('trainingSectionModules.status', TrainingSectionModule::STATUS_ACTIVE);

                if ($this->user->primary) {
                    $company_module_query = CompanyTrainingSectionModule::query()
                        ->select($columns)
                        ->join(
                            'trainingSectionModules',
                            'trainingSectionModules.trainingSectionModuleID',
                            '=',
                            'companiesTrainingSectionModules.trainingSectionModuleID'
                        )
                        ->where('companiesTrainingSectionModules.companyID', $this->user->companyID)
                        ->where('trainingSectionModules.status', TrainingSectionModule::STATUS_ACTIVE);
                    $module_query->union($company_module_query);

                    $module_query = DB::table(DB::raw("({$module_query->toSql()}) as modules"))
                        ->mergeBindings($module_query->getQuery());
                }

                $modules = $module_query->orderBy('order')->get();
                $section_modules = [];
                $module_ids = [];
                foreach ($modules as $module) {
                    $module_ids[] = $module->trainingSectionModuleID;
                    $section_id = $module->trainingSectionID;
                    $section_modules[$section_id] ??= [];
                    $section_modules[$section_id][] = [
                        'id' => $module->trainingSectionModuleID,
                        'title' => $module->cardTitle ?? $module->title,
                        'icon' => $module->cardIcon,
                        'alias' => $module->alias,
                        'is_required' => $module->isRequired === 1,
                        'is_hidden' => $module->isHidden === 1
                    ];
                }

                $section_data = TrainingSection::query()
                    ->whereIn('trainingSectionID', array_keys($section_modules))
                    ->orderBy('order')
                    ->get(['trainingSectionID', 'title', 'content']);

                $sections = [];
                foreach ($section_data as $section) {
                    $sections[] = [
                        'id' => $section->trainingSectionID,
                        'title' => $section->title,
                        'content' => $section->content,
                        'modules' => $section_modules[$section->trainingSectionID]
                    ];
                }
                return [
                    'sections' => $sections,
                    'module_ids' => $module_ids
                ];
            });
    }

    /**
     * Get list of all completed training module ids
     *
     * @return array
     */
    public function getCompletedModules(): array
    {
        $module_query = TrainingSectionModuleUser::query()
            ->select(['trainingSectionModulesUsers.trainingSectionModuleID'])
            ->where('trainingSectionModulesUsers.userID', $this->user->getKey())
            ->where('trainingSectionModulesUsers.isCompleted', true);

        if ($this->user->primary) {
            $company_module_query = CompanyTrainingSectionModule::query()
                ->select(['companiesTrainingSectionModules.trainingSectionModuleID'])
                ->where('companiesTrainingSectionModules.companyID', $this->user->companyID)
                ->where('companiesTrainingSectionModules.isCompleted', true);
            $module_query->union($company_module_query);

            $module_query = DB::table(DB::raw("({$module_query->toSql()}) as modules"))
                ->mergeBindings($module_query->getQuery());
        }
        return array_map(fn(\stdClass|TrainingSectionModuleUser $module): int => $module->trainingSectionModuleID, $module_query->get()->all());
    }

    /**
     * Get and cache module content by id
     *
     * Verifies user has access to module.
     *
     * @param int $module_id
     * @return array
     * @throws ModuleNotFoundException
     */
    public function getModuleByID(int $module_id): array
    {
        if (!in_array($module_id, $this->getSections()['module_ids'])) {
            throw new ModuleNotFoundException('User does not have access to module: %d', $module_id);
        }
        return Cache::tags(self::getCacheTags('module'))
            ->remember(self::getCacheKey('module', $module_id), 0, function () use ($module_id): array {
                $module = TrainingSectionModule::query()
                    ->whereKey($module_id)
                    ->where('status', TrainingSectionModule::STATUS_ACTIVE)
                    ->first();
                if ($module === null) {
                    throw new ModuleNotFoundException('Unable to find training module: %d', $module_id);
                }
                return [
                    'id' => $module->trainingSectionModuleID,
                    'title' => $module->title,
                    'intro' => $module->intro,
                    'youtube_video_id' => $module->youtubeVideoID,
                    'youtube_video_start_time' => $module->youtubeVideoStartTime,
                    'image_url' => $module->imageUrl,
                    'content' => $module->content,
                    'try_url' => $module->tryUrl,
                    'help_center_url' => $module->helpCenterUrl
                ];
            });
    }

    /**
     * Complete any modules for user which pass the required action criteria
     */
    protected function completeModules(): void
    {
        if ($this->user->primary) {
            $completed_modules = CompanyTrainingSectionModuleAction::query()
                ->select([
                    'companiesTrainingSectionModulesActions.companyTrainingSectionModuleID',
                    DB::raw('SUM(IF(companiesTrainingSectionModulesActions.isCompleted = 1, 1, 0)) as completed'),
                    'trainingSectionModules.requiredActionCount'
                ])
                ->join(
                    'companiesTrainingSectionModules',
                    'companiesTrainingSectionModules.companyTrainingSectionModuleID',
                    '=',
                    'companiesTrainingSectionModulesActions.companyTrainingSectionModuleID'
                )
                ->join(
                    'trainingSectionModules',
                    'trainingSectionModules.trainingSectionModuleID',
                    '=',
                    'companiesTrainingSectionModules.trainingSectionModuleID'
                )
                ->where('companiesTrainingSectionModules.isCompleted', false)
                ->groupBy('companiesTrainingSectionModulesActions.companyTrainingSectionModuleID')
                ->havingRaw('completed >= trainingSectionModules.requiredActionCount')
                ->get();
            if (count($completed_modules) > 0) {
                $now = Carbon::now('UTC');
                foreach ($completed_modules as $module) {
                    CompanyTrainingSectionModule::query()->whereKey($module->companyTrainingSectionModuleID)->update([
                        'isCompleted' => true,
                        'completedAt' => $now
                    ]);
                }
            }
        }

        $completed_modules = TrainingSectionModuleUserAction::query()
            ->select([
                'trainingSectionModulesUsersActions.trainingSectionModuleUserID',
                DB::raw('SUM(IF(trainingSectionModulesUsersActions.isCompleted = 1, 1, 0)) as completed'),
                'trainingSectionModules.requiredActionCount'
            ])
            ->join(
                'trainingSectionModulesUsers',
                'trainingSectionModulesUsers.trainingSectionModuleUserID',
                '=',
                'trainingSectionModulesUsersActions.trainingSectionModuleUserID'
            )
            ->join(
                'trainingSectionModules',
                'trainingSectionModules.trainingSectionModuleID',
                '=',
                'trainingSectionModulesUsers.trainingSectionModuleID'
            )
            ->where('trainingSectionModulesUsers.isCompleted', false)
            ->groupBy('trainingSectionModulesUsersActions.trainingSectionModuleUserID')
            ->havingRaw('completed >= trainingSectionModules.requiredActionCount')
            ->get();
        if (count($completed_modules) > 0) {
            $now = Carbon::now('UTC');
            foreach ($completed_modules as $module) {
                TrainingSectionModuleUser::query()->whereKey($module->trainingSectionModuleUserID)->update([
                    'isCompleted' => true,
                    'completedAt' => $now
                ]);
            }
        }
    }

    /**
     * Complete specific action for user
     *
     * Finds all usages of action assigned to user or company and completes it. Once done, modules are checked to see
     * if they can be marked complete.
     *
     * @param int|array $action_id
     */
    public function completeAction(int|array $action_id): void
    {
        $user_id = $this->user->getKey();
        $now = Carbon::now('UTC');
        if (!is_array($action_id)) {
            $action_id = [$action_id];
        }
        if ($this->user->primary) {
            /** @var CompanyTrainingSectionModuleAction[] $actions */
            $actions = CompanyTrainingSectionModuleAction::query()
                ->join(
                    'companiesTrainingSectionModules',
                    'companiesTrainingSectionModules.companyTrainingSectionModuleID',
                    '=',
                    'companiesTrainingSectionModulesActions.companyTrainingSectionModuleID'
                )
                ->where('companiesTrainingSectionModules.companyID', $this->user->companyID)
                ->whereIn('companiesTrainingSectionModulesActions.trainingActionID', $action_id)
                ->where('companiesTrainingSectionModulesActions.isCompleted', false)
                ->get();
            foreach ($actions as $action) {
                $action->fill([
                    'isCompleted' => true,
                    'completedAt' => $now,
                    'updatedByUserID' => $user_id
                ])->save();
            }
        }
        /** @var TrainingSectionModuleUserAction[] $actions */
        $actions = TrainingSectionModuleUserAction::query()
            ->join(
                'trainingSectionModulesUsers',
                'trainingSectionModulesUsers.trainingSectionModuleUserID',
                '=',
                'trainingSectionModulesUsersActions.trainingSectionModuleUserID'
            )
            ->where('trainingSectionModulesUsers.userID', $user_id)
            ->whereIn('trainingSectionModulesUsersActions.trainingActionID', $action_id)
            ->where('trainingSectionModulesUsersActions.isCompleted', false)
            ->get();
        foreach ($actions as $action) {
            $action->fill([
                'isCompleted' => true,
                'completedAt' => $now
            ])->save();
        }

        $this->completeModules();
    }

    /**
     * Complete training for individual user
     */
    public function complete(): void
    {
        if (!$this->isTraining()) {
            return;
        }
        $this->user->fill([
            'isTraining' => false,
            'trainingCompletedAt' => Carbon::now('UTC')
        ])->save();
    }

    /**
     * Enable training for user
     *
     * * @param int $user_id
     */
    public function enableUser(int $user_id): void
    {
        // only allow if the user is an admin
        if (!$this->user->admin) {
            throw new TrainingServiceException('Only admins can enable training');
        }
        $user = User::find($user_id);
        if ($user === null) {
            throw new TrainingServiceException('Unable to find user with ID: %d', $user_id);
        }
        if ($user->isTraining) {
            throw new TrainingServiceException('User already has training enabled');
        }
        if ($user->trainingSectionModules()->count() === 0) {
            throw new TrainingServiceException('Training never enabled for user, ensure company is setup with training ability');
        }
        $user->isTraining = true;
        $user->trainingCompletedAt = null;
        $user->save();
    }

    /**
     * Enable training for company users
     *
     * * @param int $company_id
     */
    public function enableCompanyUsers(int $company_id): void
    {
        // only allow if the user is an admin
        if (!$this->user->admin) {
            throw new TrainingServiceException('Only admins can enable training');
        }
        $company = Company::find($company_id);
        if ($company === null) {
            throw new TrainingServiceException('Unable to find company with ID: %d', $company_id);
        }
        foreach ($company->users as $user) {
            if ($user->isTraining) {
                continue;
            }
            if ($user->trainingSectionModules()->count() === 0) {
                continue;
            }
            $user->isTraining = true;
            $user->trainingCompletedAt = null;
            $user->save();
        }
    }
}
