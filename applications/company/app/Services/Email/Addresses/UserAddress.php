<?php

namespace App\Services\Email\Addresses;

use App\Services\Email\Classes\MaskedAddress;
use App\Services\Email\Interfaces\SmtpSettingProviderInterface;
use App\Services\Email\Traits\Address\SmtpSettingProviderTrait;
use Common\Models\EmailMessageAddress;
use Common\Models\User;
use Core\Exceptions\AppException;
use Ramsey\Uuid\Uuid;

/**
 * Class UserAddress
 *
 * @package App\Services\Email\Addresses
 */
class UserAddress extends MaskedAddress implements SmtpSettingProviderInterface
{
    use SmtpSettingProviderTrait;

    /**
     * @var null|User
     */
    protected $user = null;

    /**
     * Set user
     *
     * @param User $user
     */
    public function setUser(User $user)
    {
        $this->user = $user;
    }

    /**
     * Get user
     *
     * @return User
     * @throws AppException
     */
    public function getUser()
    {
        if ($this->user === null) {
            throw new AppException('No company defined');
        }
        return $this->user;
    }

    /**
     * Hydrate address from email message address model
     *
     * Sets user to source SMTP settings as needed
     *
     * @param EmailMessageAddress $address
     * @return $this
     * @throws AppException
     */
    public function hydrate(EmailMessageAddress $address)
    {
        if (($user = User::where('userUUID', $address->itemID)->first()) === null) {
            throw new AppException('Unable to find user');
        }
        $this->setUser($user);
        parent::hydrate($address);
        return $this;
    }

    /**
     * Fill in instance from user model
     *
     * @param User $user
     * @return $this
     */
    public function fromModel(User $user)
    {
        $this->setAddress($user->userEmail);
        if ($user->userFirstName !== '--') {
            $this->setName($user->userFirstName . ' ' . $user->userLastName);
        }
        $this->setType(static::TYPE_USER);
        $this->setItemID(Uuid::fromBytes($user->userUUID));

        $this->setUser($user);
        return $this;
    }

    /**
     * Load SMTP settings
     *
     * If user doesn't have
     *
     * @return array
     * @throws AppException
     */
    public function loadSmtpSettings()
    {
        $user = $this->getUser();
        // if user has smtp credentials assigned, then use them
        if ($user->smtpCredentialID !== null && $user->smtpCredential->isEnabled) {
            $credential = $user->smtpCredential;
        } elseif ($user->company->smtpCredentialID !== null && $user->company->smtpCredential->isEnabled) {
            $credential = $user->company->smtpCredential;
        } else {
            $domain = $this->getDomainService()->findByCompanyID($user->companyID);
            $credential = $domain->brand->smtpCredential;
        }
        return $this->getSmtpSettingsFromModel($credential);
    }

    /**
     * Get mail domain for masking
     *
     * @return string
     * @throws AppException
     */
    protected function getMailDomain(): string
    {
        $domain = $this->getDomainService()->findByCompanyID($this->getUser()->companyID);
        return $domain->brand->mailDomain;
    }
}
