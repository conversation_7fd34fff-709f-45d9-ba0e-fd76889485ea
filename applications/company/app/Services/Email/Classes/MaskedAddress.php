<?php

declare(strict_types=1);

namespace App\Services\Email\Classes;

use App\Services\Email\Interfaces\MaskedAddressInterface;

/**
 * Class MaskedAddress
 *
 * Base class for all masked addresses
 *
 * @package App\Services\Email\Classes
 */
abstract class MaskedAddress extends Address implements MaskedAddressInterface
{
    /**
     * Get mail domain for masking
     *
     * @return string
     */
    abstract protected function getMailDomain(): string;

    /**
     * Get masked address
     *
     * @return string
     */
    public function getMaskedAddress(): string
    {
        return 'notification@' . $this->getMailDomain();
    }
}
