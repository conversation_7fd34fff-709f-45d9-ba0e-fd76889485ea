<?php

namespace App\Services\Email\Interfaces;

/**
 * Interface TypeInterface
 *
 * @package App\Services\Email\Interfaces
 */
interface TypeInterface
{
    /**
     * Build email using payload data
     *
     * @param array $payload
     * @return MessageInterface
     */
    public function build(array $payload);

    /**
     * Handle email reply
     *
     * Note: for future consideration
     *
     * @return mixed
     */
    //public function handleReply();
}
