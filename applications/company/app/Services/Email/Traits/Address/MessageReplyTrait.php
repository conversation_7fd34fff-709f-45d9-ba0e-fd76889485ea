<?php

namespace App\Services\Email\Traits\Address;

/**
 * Trait MessageReplyTrait
 *
 * Note: not in use, just an example
 *
 * @package App\Services\Email\Traits\Address
 */
trait MessageReplyTrait
{
//    public function getMessageReplyAddress(MessageInterface $message)
//    {
//        $address = 'reply-' . str_replace('-', '', $message->getID()->toString());
//        $address .= '-' . str_replace('-', '', $this->getID()->toString());
//        $address .= '@' . $this->getDomain();
//        return $address;
//    }
}
