<?php

namespace App\Services\Form\Interfaces\Structure\Group\Rule\Condition;

/**
 * Interface MetaSideValueInterface
 *
 * Used for any values derived from the form meta. This only allows the types which meta supports.
 *
 * @package App\Services\Form\Interfaces\Structure\Group\Rule\Condition
 */
interface MetaSideValueInterface extends
    NumericSideValueInterface,
    RegexSideValueInterface,
    ScalarSideValueInterface
{
    //
}
