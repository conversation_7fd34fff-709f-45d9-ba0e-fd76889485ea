<?php

namespace App\Services\Form\Interfaces\Structure;

use App\Services\Form\Classes\Structure\Group;

/**
 * Interface GroupParentInterface
 *
 * @package App\Services\Form\Interfaces\Structure
 */
interface GroupParentInterface
{
    /**
     * Get parent of group
     *
     * @return GroupParentInterface|null
     */
    public function getParent(): ?GroupParentInterface;

    /**
     * Add group to component
     *
     * @param Group $group
     * @param string|null $alias
     */
    public function addGroup(Group $group, ?string $alias = null): void;

    /**
     * Get group by id or alias
     *
     * @param string $id
     * @param bool $deep
     * @return Group|null
     */
    public function getGroup(string $id, bool $deep = false): ?Group;
}
