<?php

declare(strict_types=1);

namespace App\Services\Form\Classes;

use App\Resources\Form\Item\{Entry\GroupResource, EntryResource};
use App\Services\Form\Classes\Entry\Group;
use App\Services\Form\Classes\Structure\Group as StructureGroup;
use App\Services\Form\Exceptions\{FormException, PersistException};
use App\Services\Form\Helpers\{EntryFileInfoHelper, EntryGroupNestingHelper, EntryProductInfoHelper};
use App\Services\Form\Interfaces\Entry\GroupParentInterface;
use App\Services\Form\Traits\{ArrayImportExportTrait, IdentifierTrait};
use App\Services\Form\Types;
use Closure;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Resource\Classes\{Entity, Scope};
use Core\Components\Resource\Interfaces\AclInterface;
use Throwable;

/**
 * Class Entry
 *
 * @package App\Services\Form\Classes
 */
class Entry implements GroupParentInterface
{
    use ArrayImportExportTrait;
    use IdentifierTrait;

    /**
     * @var string[] Mapping of types to their respective classes
     */
    protected static array $form_type_map = [
        EntryResource::FORM_TYPE_COMPANY => Types\CompanyType::class
    ];

    /**
     * @var Type Type instance
     */
    protected Type $type;

    /**
     * @var int|null Form type for polymorphic relation which owns the entry
     */
    protected ?int $form_type = null;

    /**
     * @var string|null Form id of polymorphic relation which owns the entry
     */
    protected ?string $form_id = null;

    /**
     * @var Group[] List of defined groups
     */
    protected array $groups = [];

    /**
     * @var array Cache for mapping of original group id to their new ones after clone() is called
     */
    protected array $clone_group_map = [];

    /**
     * @var EntryFileInfoHelper|null File info helper cache
     */
    protected ?EntryFileInfoHelper $file_info_helper = null;

    /**
     * @var EntryProductInfoHelper|null Product info helper cache
     */
    protected ?EntryProductInfoHelper $product_info_helper = null;

    /**
     * Get entry by id
     *
     * All entry related data is pulled from database using resource system. Groups are then nested using the
     * entry group nesting helper and passed to a new instance.
     *
     * @param AclInterface $acl
     * @param string $id UUID
     * @param bool $setup
     * @return static
     * @throws FormException
     */
    public static function getByID(AclInterface $acl, string $id, bool $setup = true): self
    {
        $entry = EntryResource::make($acl)->entity($id)->scope(Scope::make()->format('form-v1'))->run()->toArray();
        if (!isset(static::$form_type_map[$entry['form_type']])) {
            throw new FormException('Unable to find type class for form type: %d', $entry['form_type']);
        }
        $class = static::$form_type_map[$entry['form_type']];
        $type = $class::getByID($acl, $entry['form_id']);

        $entry['groups'] = (new EntryGroupNestingHelper())->run($entry['groups'])['nested'];
        $instance = static::make($type, $entry);
        if ($setup) {
            $instance->setup($acl);
        }
        return $instance;
    }

    /**
     * Create new instance
     *
     * @param Type $type
     * @param array $data
     * @return static
     * @throws FormException
     */
    public static function make(Type $type, array $data = []): self
    {
        return new static($type, $data);
    }

    /**
     * Entry constructor
     *
     * @param Type $type
     * @param array $data
     * @throws FormException
     */
    public function __construct(Type $type, array $data = [])
    {
        $this->type = $type;
        if (count($data) > 0) {
            $this->hydrate($data);
        }
    }

    /**
     * Resets class after clone operation
     */
    protected function __clone()
    {
        $this->clearID();
        $this->clone_group_map = [];
        $this->groups = [];
    }

    /**
     * Clone entry
     *
     * Object clone is disabled, since we need to track the id's during the cloning process.
     *
     * @return $this
     */
    public function clone(): self
    {
        $entry = clone $this;
        foreach ($this->groups as $group) {
            $entry->addGroup($group->clone($entry));
        }
        return $entry;
    }

    /**
     * Set clone group mapping of old id to new
     *
     * @param string $old_id
     * @param string $new_id
     */
    public function setCloneGroup(string $old_id, string $new_id): void
    {
        $this->clone_group_map[$old_id] = $new_id;
    }

    /**
     * Get new group id using old group id
     *
     * @param string $old_id
     * @return string|null
     */
    public function getCloneGroup(string $old_id): ?string
    {
        return $this->clone_group_map[$old_id] ?? null;
    }

    /**
     * Get mapping of previous group ids mapped to their cloned id
     *
     * @return array
     */
    public function getCloneGroupMap(): array
    {
        return $this->clone_group_map;
    }

    /**
     * Set file info helper
     *
     * @param EntryFileInfoHelper $helper
     */
    public function setFileInfoHelper(EntryFileInfoHelper $helper): void
    {
        $this->file_info_helper = $helper;
    }

    /**
     * Get file info helper
     *
     * If does not exist, will be created and cached.
     *
     * @return EntryFileInfoHelper
     */
    public function getFileInfoHelper(): EntryFileInfoHelper
    {
        if ($this->file_info_helper === null) {
            $this->setFileInfoHelper(new EntryFileInfoHelper());
        }
        return $this->file_info_helper;
    }

    /**
     * Set product info helper
     *
     * @param EntryProductInfoHelper $helper
     */
    public function setProductInfoHelper(EntryProductInfoHelper $helper): void
    {
        $this->product_info_helper = $helper;
    }

    /**
     * Get product info helper
     *
     * If does not exist, will be created and cached.
     *
     * @return EntryProductInfoHelper
     */
    public function getProductInfoHelper(): EntryProductInfoHelper
    {
        if ($this->product_info_helper === null) {
            $this->setProductInfoHelper(new EntryProductInfoHelper());
        }
        return $this->product_info_helper;
    }

    /**
     * Setup class data from array
     *
     * Used in conjunction with cache loading and importing.
     *
     * @param array $data
     * @throws FormException
     */
    public function hydrate(array $data): void
    {
        $this->importFromArray([
            'id' => ['string', 'setID'],
            'form_type' => ['int', 'setFormType'],
            'form_id' => ['string', 'setFormID'],
            'groups' => ['list', function ($group) {
                if (!isset($group['item_group_id']) || !is_string($group['item_group_id'])) {
                    throw new FormException('Item group id is required to create nested group');
                }
                return Group::make($this, $group['item_group_id'], null, $group);
            }]
        ], $data);
    }

    /**
     * Define related components within scoped closure
     *
     * @param Closure $with
     * @return $this
     */
    public function with(Closure $with): self
    {
        $with($this);
        return $this;
    }

    /**
     * Get form type instance
     *
     * @return Type
     */
    public function getType(): Type
    {
        return $this->type;
    }

    /**
     * Set form type used for polymorphic relation to owner
     *
     * @param int $type
     * @return $this
     */
    public function setFormType(int $type): self
    {
        $this->form_type = $type;
        return $this;
    }

    /**
     * Get form type
     *
     * @return int|null
     */
    public function getFormType(): ?int
    {
        return $this->form_type;
    }

    /**
     * Set form id used for polymorphic relation to owner
     *
     * @param string $id
     * @return $this
     */
    public function setFormID(string $id): self
    {
        $this->form_id = $id;
        return $this;
    }

    /**
     * Get form id
     *
     * @return string|null
     */
    public function getFormID(): ?string
    {
        return $this->form_id;
    }

    /**
     * Add group
     *
     * @param Group $group
     */
    public function addGroup(Group $group): void
    {
        $this->groups[$group->getStructureGroup()->getID()] = $group;
    }

    /**
     * Get entry group by id
     *
     * @param string $id UUID
     * @return Group|null
     */
    public function getGroup(string $id): ?Group
    {
        return $this->groups[$id] ?? null;
    }

    /**
     * Get group instance from structure by id or alias
     *
     * @param string $id
     * @return StructureGroup|null
     */
    public function getStructureGroup(string $id): ?StructureGroup
    {
        return $this->type->getStructure()->getGroup($id);
    }

    /**
     * Validate entry before saving
     *
     * @throws \App\Services\Form\Exceptions\ValidationException
     */
    public function validate(): void
    {
        if (count($this->groups) > 0) {
            foreach ($this->groups as $group) {
                $group->validate();
            }
        }
    }

    /**
     * Get resource entity used to persist entry
     *
     * @return Entity
     */
    public function getEntity(): Entity
    {
        return Entity::make([
            'id' => $this->getID(),
            'form_type' => $this->getFormType(),
            'form_id' => $this->getFormID()
        ]);
    }

    /**
     * Persist entry and all relationships
     *
     * @param EntryResource $resource
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    public function persist(EntryResource $resource): void
    {
        try {
            $resource->updateOrCreate($this->getEntity())->run();
        } catch (Throwable $e) {
            throw (new PersistException('Unable to persist entry'))->setLastException($e);
        }

        if (count($this->groups) > 0) {
            /** @var GroupResource $group_resource */
            $group_resource = $resource->relationResource('groups');
            foreach ($this->groups as $group) {
                $group->persist($group_resource);
            }
        }
    }

    /**
     * Save new entry by validating and persisting data
     *
     * Currently cannot update an entry, but is in future plans if need arises.
     *
     * @param AclInterface $acl
     * @throws \App\Services\Form\Exceptions\ValidationException
     */
    public function save(AclInterface $acl): void
    {
        $this->validate();

        $resource = EntryResource::make($acl);
        DB::transaction(function () use ($resource) {
            $this->persist($resource);
        });
    }

    /**
     * Setup and configure for use after loading from external source
     *
     * @param AclInterface $acl
     */
    public function setup(AclInterface $acl): void
    {
        if (count($this->groups) > 0) {
            foreach ($this->groups as $group) {
                $group->setup();
            }
        }
        $this->getFileInfoHelper()->load($acl);
        $this->getProductInfoHelper()->load($acl);
    }

    /**
     * Convert into format useful for client side form library to consume
     *
     * Groups are keyed by their structure group id to make lookups easy by the client JS library.
     *
     * @return array
     */
    public function toClientFormat(): array
    {
        return $this->exportToArray([
            'id' => 'getID',
            'groups' => function() {
                if (count($this->groups) === 0) {
                    return null;
                }
                $groups = [];
                foreach ($this->groups as $id => $group) {
                    $items = $group->toClientFormat();
                    if (count($items) === 0) {
                        continue;
                    }
                    $groups[$id] = $items;
                }
                return $groups;
            }
        ]);
    }

    /**
     * Export to user friendly format useful to transferring info between companies or environments
     *
     * @return array
     */
    public function export(): array
    {
        return $this->exportToArray([
            'form_type' => 'getFormType',
            'form_id' => 'getFormID',
            'groups' => fn() => count($this->groups) > 0 ? array_values(array_map(fn($group) => $group->export(), $this->groups)) : null
        ]);
    }
}
