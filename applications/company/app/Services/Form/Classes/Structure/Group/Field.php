<?php

declare(strict_types=1);

namespace App\Services\Form\Classes\Structure\Group;

use App\Interfaces\Resource\FormFieldResourceInterface;
use App\Resources\Form\Item\Group\{FieldResource, LayoutResource};
use App\Services\Form\Components\{Fields, Overrides\FieldOverride};
use App\Services\Form\Exceptions\{FormException, ImportException, PersistException, ValidationException};
use App\Services\Form\Interfaces\Structure\{Group\FieldParentInterface, Group\LayoutItemInterface, OverridableInterface};
use App\Services\Form\Traits\{ArrayImportExportTrait, IdentifierTrait};
use Closure;
use Core\Classes\Str;
use Core\Components\Http\StaticAccessors\View;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Interfaces\AclInterface;
use Throwable;

/**
 * Class Field
 *
 * Base class for all fields. This same class is used for overrides to reduce duplicate. This can cause confusion as
 * config items for both source field and override fields live in the same class. Source info and override info should
 * never be both defined on the same instance.
 *
 * @package App\Services\Form\Classes\Structure\Group
 */
abstract class Field implements OverridableInterface, LayoutItemInterface
{
    use ArrayImportExportTrait;
    use IdentifierTrait;

    /**
     * @var int[] Mapping of aliases to field type used for exports
     */
    protected static array $type_alias_map = [
        'text' => FieldResource::TYPE_TEXT,
        'textarea' => FieldResource::TYPE_TEXTAREA,
        'select' => FieldResource::TYPE_SELECT,
        'radio' => FieldResource::TYPE_RADIO,
        'checkbox' => FieldResource::TYPE_CHECKBOX,
        'file' => FieldResource::TYPE_FILE,
        'product-list' => FieldResource::TYPE_PRODUCT_LIST
    ];

    /**
     * @var string[] Mapping of types to their respective classes
     */
    protected static array $type_map = [
        FieldResource::TYPE_TEXT => Fields\TextField::class,
        FieldResource::TYPE_TEXTAREA => Fields\TextareaField::class,
        FieldResource::TYPE_SELECT => Fields\Option\SelectField::class,
        FieldResource::TYPE_RADIO => Fields\Option\RadioField::class,
        FieldResource::TYPE_CHECKBOX => Fields\Option\CheckboxField::class,
        FieldResource::TYPE_FILE => Fields\FileField::class,
        FieldResource::TYPE_PRODUCT_LIST => Fields\ProductListField::class
    ];

    /**
     * @var FieldParentInterface Field parent
     */
    protected FieldParentInterface $parent;

    /**
     * @var string|null Optional alias
     */
    protected ?string $alias = null;

    /**
     * @var int Field type
     */
    protected int $type;

    /**
     * @var string|null Source field id if used as an override
     */
    protected ?string $source_id = null;

    /**
     * @var self|null Source field instance (used if this field is defined in form overrides)
     */
    protected ?self $source = null;

    /**
     * @var self|null Override field instance (used if this field is defined in structure)
     */
    protected ?self $override = null;

    /**
     * @var string|null Field label
     */
    protected ?string $label = null;

    /**
     * @var string|null Display label which shows when outputting field info in a template
     */
    protected ?string $display_label = null;

    /**
     * @var bool Determines if field is required on client side
     */
    protected bool $is_required = false;

    /**
     * @var string|null Tooltip which shows on client side
     */
    protected ?string $tooltip = null;

    /**
     * @var bool Determines if field is setup
     */
    protected bool $setup = false;

    /**
     * Create field instance from array
     *
     * Will use type in data to load proper class type from type mapping.
     *
     * @param FieldParentInterface $parent
     * @param array $data
     * @return static
     * @throws ImportException
     */
    public static function import(FieldParentInterface $parent, array $data): self
    {
        if (!isset($data['type'])) {
            throw new ImportException('No field type defined');
        }
        $type = $data['type'];
        if (isset(static::$type_alias_map[$type])) {
            $type = static::$type_alias_map[$type];
        }
        if (!isset(static::$type_map[$type])) {
            throw new ImportException('Invalid field type');
        }
        return (static::$type_map[$type])::make($parent, null, $data);
    }

    /**
     * Create field and assign to group
     *
     * Used for easy chaining while building forms.
     *
     * @param FieldParentInterface $parent
     * @param string|null $alias
     * @param array $data
     * @return static
     */
    public static function make(FieldParentInterface $parent, ?string $alias = null, array $data = []): self
    {
        if ($alias !== null) {
            $data['alias'] = $alias;
        }
        $field = new static($parent, $data);
        $parent->addField($field, $field->getAlias());
        return $field;
    }

    /**
     * Field constructor
     *
     * @param FieldParentInterface $parent
     * @param array $data
     */
    public function __construct(FieldParentInterface $parent, array $data = [])
    {
        $this->parent = $parent;
        if (count($data) > 0) {
            $this->hydrate($data);
        }
    }

    /**
     * Setup class data from array
     *
     * Used in conjunction with cache loading and importing.
     *
     * @param array $data
     * @throws FormException
     */
    public function hydrate(array $data): void
    {
        $this->importFromArray([
            'id' => ['string', 'setID'],
            'alias' => ['string', 'setAlias'],
            'form_item_group_field_id' => ['string', 'setSourceID'],
            'label' => ['string', 'setLabel'],
            'display_label' => ['string', 'setDisplayLabel'],
            'is_required' => ['bool', 'setIsRequired'],
            'tooltip' => ['string', 'setTooltip'],
            'config' => ['array', 'setConfig']
        ], $data);
        if (isset($data['require_override']) && $data['require_override']) {
            $this->requireOverride();
        }
    }

    /**
     * Helper method to define info related to this field within it own scoped function
     *
     * @param Closure $with
     * @return $this
     */
    public function with(Closure $with): self
    {
        $with($this, $this->parent);
        return $this;
    }

    /**
     * Get parent
     *
     * @return FieldParentInterface
     */
    public function getParent(): FieldParentInterface
    {
        return $this->parent;
    }

    /**
     * Set id of source field
     *
     * Used when importing override field from array. Setup methods will find and set the source field instance.
     *
     * @param string $id
     * @return $this
     */
    public function setSourceID(string $id): self
    {
        $this->source_id = $id;
        return $this;
    }

    /**
     * Get source field id
     *
     * @return string|null
     */
    public function getSourceID(): ?string
    {
        return $this->source_id;
    }

    /**
     * Set source field instance
     *
     * Used for fields defined as an override. This is a reference to the structure field which is getting overridden.
     *
     * @param Field $source
     * @return $this
     */
    public function setSource(self $source): self
    {
        $this->source = $source;
        $source->setOverride($this);
        return $this;
    }

    /**
     * Get source field instance
     *
     * @return Field|null
     */
    public function getSource(): ?self
    {
        return $this->source;
    }

    /**
     * Set override field instance
     *
     * Used for fields defined in structure. This references the form field which is overriding this one. If this is
     * defined, info from the override is sent instead of data from this instance.
     *
     * @param Field $override
     * @return $this
     */
    public function setOverride(self $override): self
    {
        $this->override = $override;
        $override->setAlias($this->getAlias());
        $this->parent->registerAliases($this, [$override->getID()]);
        return $this;
    }

    /**
     * Get override field instance
     *
     * @return Field|null
     */
    public function getOverride(): ?self
    {
        return $this->override;
    }

    /**
     * Set alias used for easier lookup
     *
     * @param string|null $alias
     * @return $this
     */
    public function setAlias(?string $alias): self
    {
        $this->alias = $alias;
        return $this;
    }

    /**
     * Get alias
     *
     * @return string|null
     */
    public function getAlias(): ?string
    {
        return $this->alias;
    }

    /**
     * Get field type
     *
     * @return int
     */
    public function getType(): int
    {
        return $this->type;
    }

    /**
     * Set field label
     *
     * @param string|null $label
     * @return $this
     */
    public function setLabel(?string $label): self
    {
        $this->label = $label;
        return $this;
    }

    /**
     * Get field label
     *
     * @return string|null
     */
    public function getLabel(): ?string
    {
        return $this->label;
    }

    /**
     * Set label used when rendering in document
     *
     * @param string|null $label
     * @return $this
     */
    public function setDisplayLabel(?string $label): self
    {
        $this->display_label = $label;
        return $this;
    }

    /**
     * Get display label config item
     *
     * @return string|null
     */
    public function getDisplayLabel(): ?string
    {
        return $this->display_label;
    }

    /**
     * Set if field is required
     *
     * @param bool $is_required
     * @return $this
     */
    public function setIsRequired(bool $is_required = true): self
    {
        $this->is_required = $is_required;
        return $this;
    }

    /**
     * Get is required setting
     *
     * @return bool
     */
    public function getIsRequired(): bool
    {
        return $this->is_required;
    }

    /**
     * Set if field has a tooltip
     *
     * @param string|null $tooltip
     * @return $this
     */
    public function setTooltip(?string $tooltip): self
    {
        $this->tooltip = $tooltip;
        return $this;
    }

    /**
     * Get tooltip setting
     *
     * @return string|null
     */
    public function getTooltip(): ?string
    {
        return $this->tooltip;
    }

    /**
     * Set config
     *
     * @param array $config
     * @return $this
     */
    public function setConfig(array $config): self
    {
        return $this;
    }

    /**
     * Build config
     *
     * Used to allow for field types to build array without having to deal with null values.
     *
     * @return array
     */
    protected function buildConfig(): array
    {
        return [];
    }

    /**
     * Get config
     *
     * If no config items are available, then null is returned so we don't store the empty array.
     *
     * @return array|null
     */
    public function getConfig(): ?array
    {
        $config = $this->buildConfig();
        return count($config) > 0 ? $config : null;
    }

    /**
     * Set override config
     *
     * @param array $config
     * @return $this
     */
    public function setOverrideConfig(array $config): self
    {
        return $this;
    }

    /**
     * Build override config
     *
     * @return array
     */
    protected function buildOverrideConfig(): array
    {
        return [];
    }

    /**
     * Get override config
     *
     * Passed to an associated override for this field to provide more context.
     *
     * @return array|null
     */
    public function getOverrideConfig(): ?array
    {
        $config = $this->buildOverrideConfig();
        return count($config) > 0 ? $config : null;
    }

    /**
     * Adds an override requirement to structure for this field
     *
     * @param Closure|null $closure
     * @return $this
     * @throws FormException
     */
    public function requireOverride(?Closure $closure = null): self
    {
        $override = FieldOverride::make($this->parent->getStructure())->setItem($this);
        if ($closure !== null) {
            $closure($override, $this);
        }
        return $this;
    }

    /**
     * Get location within array format for use with debugging
     *
     * @return string
     */
    public function getLocation(): string
    {
        return "{$this->parent->getLocation()}.fields.{$this->parent->getFieldIndex($this)}";
    }

    /**
     * Get lookup path for field
     *
     * @return string
     */
    public function getPath(): string
    {
        return "{$this->parent->getPath()}." . ($this->getAlias() ?? $this->getID());
    }

    /**
     * Prepare field before saving
     */
    public function prepare(): void
    {
        //
    }

    /**
     * Validate field for saving
     *
     * If source id is defined, it will searched for within the structure and set as the source instance.
     *
     * @throws ValidationException
     */
    public function validate(): void
    {
        if (($source_id = $this->getSourceID()) !== null) {
            if (($source = $this->parent->getField($source_id, deep: true)) === null) {
                throw new ValidationException('Unable to find source field: %s [%s]', $source_id, $this->getLocation());
            }
            $this->setSource($source);
        }
        $source = $this->getSource();
        $override = $this->getOverride();
        if ($source !== null && $override !== null) {
            throw new ValidationException('Field cannot have both a source field and override field [%s]', $this->getLocation());
        }
    }

    /**
     * Get entity used to persist via resource
     *
     * @return Entity
     */
    public function getEntity(): Entity
    {
        $entity = Entity::make([
            'id' => $this->getID(),
            'type' => $this->getType(),
            'label' => $this->getLabel(),
            'display_label' => $this->getDisplayLabel(),
            'is_required' => $this->getIsRequired(),
            'tooltip' => $this->getTooltip(),
            'config' => $this->getConfig()
        ]);
        return $this->parent->getFieldEntity($entity, $this);
    }

    /**
     * Persist field and any nested relationships
     *
     * @param FormFieldResourceInterface $resource
     * @throws \Core\Exceptions\AppException
     */
    public function persist(FormFieldResourceInterface $resource): void
    {
        try {
            $resource->updateOrCreate($this->getEntity())->nested()->run();
        } catch (Throwable $e) {
            throw (new PersistException('Unable to persist field [%s]', $this->getLocation()))->setLastException($e);
        }
    }

    /**
     * Configure field to set it up for usage or output to array or client formats
     *
     * @param AclInterface $acl
     * @throws FormException
     */
    protected function configure(AclInterface $acl): void
    {
        if (($source_id = $this->getSourceID()) !== null) {
            if (($source = $this->parent->getField($source_id, deep: true)) === null) {
                throw new FormException('Unable to find source field: %s [%s]', $source_id, $this->getLocation());
            }
            $this->setSource($source);
        }
    }

    /**
     * Setup field for usage
     *
     * Can only be run once per instance.
     *
     * @param AclInterface $acl
     * @throws FormException
     */
    public function setup(AclInterface $acl): void
    {
        if ($this->setup) {
            throw new FormException('Field is already setup [%s]', $this->getLocation());
        }
        $this->configure($acl);
        $this->setup = true;
    }

    /**
     * Convert to array with any relationships
     *
     * Used as caching format.
     *
     * @return array
     */
    public function toArray(): array
    {
        $field = [
            'id' => $this->getID(),
            'type' => $this->getType(),
            'label' => $this->getLabel(),
            'display_label' => $this->getDisplayLabel(),
            'is_required' => $this->getIsRequired(),
            'tooltip' => $this->getTooltip(),
            'config' => $this->getConfig()
        ];
        return $this->parent->fieldToArray($field, $this);
    }

    /**
     * Build format needed to run client side libraries
     *
     * @return array
     * @throws FormException
     */
    public function buildClientFormat(): array
    {
        if (!$this->setup) {
            throw new FormException('Setup must be called to build client format');
        }
        return [
            'id' => $this->getID(),
            'alias' => $this->getAlias(),
            'type' => $this->getType(),
            'label' => $this->getLabel(),
            'is_required' => $this->getIsRequired(),
            'tooltip' => $this->getTooltip(),
            'config' => $this->getConfig(),
            'source' => $this->parent->getFieldSource()
        ];
    }

    /**
     * Convert field into format useful for client side form library to consume
     *
     * @return array
     * @throws FormException
     */
    public function toClientFormat(): array
    {
        if (($override = $this->getOverride()) !== null) {
            return $override->toClientFormat();
        }
        return $this->buildClientFormat();
    }

    /**
     * Format for use in setup user interface
     *
     * @return array
     */
    public function toSetupFormat(): array
    {
        return [
            'id' => $this->getID(),
            'type' => $this->getType(),
            'label' => $this->getLabel(),
            'display_label' => $this->getDisplayLabel(),
            'is_required' => $this->getIsRequired(),
            'tooltip' => $this->getTooltip(),
            'config' => $this->getConfig()
        ];
    }

    /**
     * Export field to user friendly format useful to transferring info between companies or environments
     *
     * @return array
     */
    public function export(): array
    {
        $field = $this->exportToArray([
            'alias' => 'getAlias',
            'type' => 'getType',
            'label' => 'getLabel',
            'display_label' => 'getDisplayLabel',
            'is_required' => 'getIsRequired',
            'tooltip' => 'getTooltip',
            'config' => 'getConfig'
        ], [
            'aliases' => [
                'type' => static::$type_alias_map
            ]
        ]);
        return $this->parent->exportField($field, $this);
    }

    /**
     * Get variables needed to build handlebars render template
     *
     * @param int $layout_type
     * @return array
     */
    protected function getTemplateVars(int $layout_type): array
    {
        $classes = [];
        if (in_array($layout_type, [LayoutResource::TYPE_OUTPUT_TABLE_ROW, LayoutResource::TYPE_INPUT_TABLE_ROW])) {
            $classes[] = 't-layout-table-row';
        }
        return [
            'id' => $this->getAlias() ?? $this->getID(),
            'classes' => $classes,
            'label' => $this->getDisplayLabel() ?? $this->getLabel()
        ];
    }

    /**
     * Build handlebars render template
     *
     * @param int $layout_type
     * @return array
     */
    public function buildTemplate(int $layout_type): array
    {
        $vars = $this->getTemplateVars($layout_type);
        $vars['classes'] = count($vars['classes']) > 0 ? ' ' . implode(' ', $vars['classes']) : '';
        $vars['value'] = Str::indent($vars['value'], 1);
        $content = View::fetch('services.form.structure.field', $vars)->render();
        return [
            'content' => $content,
            'styles' => []
        ];
    }
}
