<?php

declare(strict_types=1);

namespace App\Services\Form\Components\EntryFields;

use App\Resources\Form\Item\Entry\Group\FieldProductResource;
use App\Services\Form\Classes\Entry\Group\{Field, Item};

/**
 * Class ProductEntryField
 *
 * @package App\Services\Form\Components\EntryFields
 */
class ProductEntryField extends Field
{
    /**
     * @var Field\Product[] List of defined products
     */
    protected array $products = [];

    /**
     * Reset class after clone operation
     */
    protected function __clone()
    {
        $this->products = [];
    }

    /**
     * Clone field and all nested products
     *
     * @param Item $item
     * @return $this
     */
    public function clone(Item $item): self
    {
        $field = parent::clone($item);
        foreach ($this->products as $product) {
            $field->addProduct($product->clone($field));
        }
        return $field;
    }

    /**
     * Setup class data from array
     *
     * Used in conjunction with cache loading and importing.
     *
     * @param array $data
     * @throws \App\Services\Form\Exceptions\FormException
     */
    public function hydrate(array $data): void
    {
        parent::hydrate($data);
        $this->importFromArray([
            'products' => ['list', fn($product) => Field\Product::make($this, $product)]
        ], $data);
    }

    /**
     * Add product
     *
     * @param Field\Product $product
     */
    public function addProduct(Field\Product $product): void
    {
        $this->products[] = $product;
    }

    /**
     * Get all products
     *
     * @return Field\Product[]
     */
    public function getProducts(): array
    {
        return $this->products;
    }

    /**
     * Persist products
     *
     * Fields are just containers for values and aren't saved directly to keep database structure cleaner.
     *
     * @param FieldProductResource $resource
     * @throws \Core\Exceptions\AppException
     */
    public function persist(FieldProductResource $resource): void
    {
        $products = $this->getProducts();
        if (count($products) > 0) {
            foreach ($products as $product) {
                $product->persist($resource);
            }
        }
    }

    /**
     * Setup and configure group for use after loading from external source
     */
    public function setup(): void
    {
        $helper = $this->getItem()->getContainer()->getEntry()->getProductInfoHelper();
        if (count($this->products) > 0) {
            foreach ($this->products as $product) {
                $product->setup($helper);
            }
        }
    }

    /**
     * Convert into format useful for client side form library to consume
     *
     * @return array
     */
    public function toClientFormat(): array
    {
        return [
            'products' => array_map(fn($product) => $product->getProductItemID(), $this->products)
        ];
    }
}
