<?php

declare(strict_types=1);

namespace App\Services\Form\Components\RuleEvents;

use App\Resources\Form\Item\Group\Rule\EventResource;
use App\Services\Form\Classes\Structure\Group\Rule\Event;
use App\Services\Form\Traits\RuleEvent\FieldTrait;

/**
 * Class FieldClearRuleEvent
 *
 * @package App\Services\Form\Components\RuleEvents
 */
class FieldClearRuleEvent extends Event
{
    use FieldTrait;

    /**
     * @var int Event type
     */
    protected int $type = EventResource::TYPE_FIELD_CLEAR;
}
