<?php

declare(strict_types=1);

namespace App\Services\Form\Components\RuleConditionSideValues;

use App\Services\Form\Classes\Structure\Group\Rule\{Condition, Condition\SideValue};
use App\Services\Form\Exceptions\ValidationException;
use App\Services\Form\Interfaces\Structure\Group\Rule\Condition\ContextSideValueInterface;

/**
 * Class FieldSideValue
 *
 * @package App\Services\Form\Components\RuleConditionSideValues
 */
class FieldSideValue extends SideValue implements ContextSideValueInterface
{
    public const KEY_LEFT = 'field';
    public const KEY_LEFT_ALT = 'field_1';
    public const KEY_RIGHT = 'field_2';

    /**
     * @var string|null Key for left side of conditional
     */
    protected ?string $left_key = self::KEY_LEFT;

    /**
     * @var string|null Key for right side of conditional
     */
    protected ?string $right_key = self::KEY_RIGHT;

    /**
     * Helper method to create new instance of side value
     *
     * @param string $field
     * @return static
     */
    public static function make(string $field): static
    {
        return new static($field);
    }

    /**
     * Create instance from conditional value
     *
     * @param string $field
     * @return static
     */
    public static function fromValue(string $field): static
    {
        return static::make($field);
    }

    /**
     * FieldSideValue constructor
     *
     * @param string $field
     */
    public function __construct(protected string $field)
    {}

    /**
     * Convert value into storage format for conditional
     *
     * @return string
     */
    public function toValue(): string
    {
        return $this->field;
    }

    /**
     * Validate side value
     *
     * Ensure field exists.
     *
     * @param Condition $condition
     * @throws ValidationException
     */
    public function validate(Condition $condition): void
    {
        if ($condition->getGroup()->getRule()->getGroup()->getField($this->field, deep: true) === null) {
            throw new ValidationException('Unable to find field: %s [%s]', $this->field, $condition->getLocation());
        }
    }
}
