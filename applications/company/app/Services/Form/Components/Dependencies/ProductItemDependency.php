<?php

declare(strict_types=1);

namespace App\Services\Form\Components\Dependencies;

use App\Resources\Form\Item\DependencyResource;
use App\Services\Form\Classes\Structure\Dependency;

/**
 * Class ProductItemDependency
 *
 * @package App\Services\Form\Components\Dependencies
 */
class ProductItemDependency extends Dependency
{
    /**
     * @var int Dependency type
     */
    protected int $type = DependencyResource::TYPE_PRODUCT_ITEM;

    /**
     * @var string|null Default product item name for use with marketplace
     */
    protected ?string $default_product_item_name = null;

    /**
     * @var string|null Product itme create interface instructions to guide user
     */
    protected ?string $product_item_create_instruction = null;

    /**
     * Set default product item name
     *
     * @param string|null $name
     * @return $this
     */
    public function setDefaultProductItemName(?string $name): self
    {
        $this->default_product_item_name = $name;
        return $this;
    }

    /**
     * Get default product item name for use when creating product while using form marketplace
     *
     * @return string|null
     */
    public function getDefaultProductItemName(): ?string
    {
        return $this->default_product_item_name;
    }

    /**
     * Set product item create instruction
     *
     * @param string|null $instruction
     * @return $this
     */
    public function setProductItemCreateInstruction(?string $instruction): self
    {
        $this->product_item_create_instruction = $instruction;
        return $this;
    }

    /**
     * Get product item create instruction to guide user when creating product during form marketplace setup
     *
     * @return string|null
     */
    public function getProductItemCreateInstruction(): ?string
    {
        return $this->product_item_create_instruction;
    }

    /**
     * Set config
     *
     * @param array $config
     * @return $this
     * @throws \App\Services\Form\Exceptions\FormException
     */
    public function setConfig(array $config): self
    {
        $this->importFromArray([
            'default_product_item_name' => ['string', 'setDefaultProductItemName'],
            'product_item_create_instruction' => ['string', 'setProductItemCreateInstruction']
        ], $config);
        return $this;
    }

    /**
     * Build config
     *
     * @return array
     */
    protected function buildConfig(): array
    {
        return $this->exportToArray([
            'default_product_item_name' => 'getDefaultProductItemName',
            'product_item_create_instruction' => 'getProductItemCreateInstruction'
        ], [
            'initial' => parent::buildConfig()
        ]);
    }
}
