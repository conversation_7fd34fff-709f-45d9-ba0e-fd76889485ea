<?php

declare(strict_types=1);

namespace App\Services\Form\Components\Groups;

use App\Resources\Form\Item\{Group\LayoutResource, GroupResource};
use App\Services\Form\Classes\Structure\Group;
use App\Services\Form\Exceptions\TemplateException;
use Core\Classes\Str;
use Core\Components\Http\StaticAccessors\View;

/**
 * Class DefaultGroup
 *
 * @package App\Services\Form\Components\Groups
 */
class DefaultGroup extends Group
{
    /**
     * @var int Group type
     */
    protected int $type = GroupResource::TYPE_DEFAULT;

    /**
     * @var bool[] List of allowed types for the group, value defines which ones are required
     */
    protected array $allowed_layout_types = [
        LayoutResource::TYPE_INPUT_SCREEN_LARGE => true,
        LayoutResource::TYPE_OUTPUT_BID_DOCUMENT => false,
        LayoutResource::TYPE_OUTPUT_JOB_DOCUMENT => false
    ];

    /**
     * @var array Map of fallback layout types if one is not available for group
     */
    protected array $layout_type_fallback = [
        LayoutResource::TYPE_OUTPUT_BID_DOCUMENT => LayoutResource::TYPE_INPUT_SCREEN_LARGE,
        LayoutResource::TYPE_OUTPUT_JOB_DOCUMENT => LayoutResource::TYPE_OUTPUT_BID_DOCUMENT
    ];

    /**
     * Build handlebars render template
     *
     * @param int $layout_type
     * @return array
     * @throws TemplateException
     */
    public function buildTemplate(int $layout_type): array
    {
        if (($layout = $this->getLayout($layout_type)) === null) {
            throw new TemplateException('Unable to find layout for type: %d', $layout_type);
        }
        $template = $layout->buildTemplate($layout_type);
        $content = null;
        if ($template['content'] !== null) {
            $content = View::fetch('services.form.structure.group', [
                'id' => $this->getAlias() ?? $this->getID(),
                'name' => $this->getName(),
                'content' => Str::indent($template['content'], 1)
            ])->render();
        }
        return [
            'content' => $content,
            'styles' => $template['styles']
        ];
    }
}
