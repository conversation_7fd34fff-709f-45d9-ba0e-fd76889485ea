<?php

declare(strict_types=1);

namespace App\Services\Form\Components\LayoutItems;

use App\Resources\Form\Item\Group\{Layout\ItemResource, LayoutResource};
use App\Services\Form\Classes\Structure\Group\Layout\Item;
use App\Services\Form\Exceptions\{FormException, ValidationException};
use Core\Components\Resource\Interfaces\AclInterface;

/**
 * Class GroupLayoutItem
 *
 * @package App\Services\Form\Components\LayoutItems
 */
class GroupLayoutItem extends Item
{
    /**
     * @var int Layout item type
     */
    protected int $item_type = ItemResource::TYPE_GROUP;

    /**
     * Setup class data from array
     *
     * Used in conjunction with cache loading and importing.
     *
     * @param array $data
     * @throws FormException
     */
    public function hydrate(array $data): void
    {
        parent::hydrate($data);
        $this->importFromArray([
            'group' => ['string', 'setAlias']
        ], $data);
    }

    /**
     * Shorthand for setting type to group and assigning an id
     *
     * @param string $id
     * @return $this
     */
    public function setGroupID(string $id): self
    {
        return $this->setItemID($id);
    }

    /**
     * Prepare item for saving
     *
     * If alias is defined, we try to find the associated group and assign its id to this layout item's item id
     * for saving.
     */
    public function prepare(): void
    {
        parent::prepare();
        if (($alias = $this->getAlias()) !== null && ($group = $this->layout->getGroup()->getGroup($alias)) !== null) {
            $this->setGroupID($group->getID());
        }
    }

    /**
     * Validate item before saving
     *
     * Verify item id points to existing group. Currently, table row layouts cannot support groups so we ensure this is
     * being assigned to a valid layout.
     *
     * @throws ValidationException
     */
    public function validate(): void
    {
        parent::validate();

        if (in_array($this->layout->getType(), [LayoutResource::TYPE_INPUT_TABLE_ROW, LayoutResource::TYPE_OUTPUT_TABLE_ROW])) {
            throw new ValidationException('Groups cannot be assigned to table row layouts');
        }
        if (($item_id = $this->getItemID()) === null) {
            throw new ValidationException('Valid group id is required [%s]', $this->getLocation());
        }
        if ($this->layout->getGroup()->getGroup($item_id) === null) {
            throw new ValidationException('Unable to find group: %s [%s]', $item_id, $this->getLocation());
        }
    }

    /**
     * Configure item to set it up for usage or output to array or client formats
     *
     * @param AclInterface $acl
     * @throws FormException
     */
    protected function configure(AclInterface $acl): void
    {
        if (($item_id = $this->getItemID()) === null) {
            throw new FormException('Group item id not defined [%s]', $this->getLocation());
        }
        if (($group = $this->layout->getGroup()->getGroup($item_id)) === null) {
            throw new FormException('Unable to find group: %s [%s]', $item_id, $this->getLocation());
        }
        $this->setItem($group);
        if (($alias = $group->getAlias()) !== null) {
            $this->setAlias($alias);
        }
    }
}
