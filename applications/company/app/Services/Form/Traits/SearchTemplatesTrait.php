<?php

declare(strict_types=1);

namespace App\Services\Form\Traits;

use App\Services\Form\Classes\Structure\Group\Template;

/**
 * Trait SearchTemplatesTrait
 *
 * @package App\Services\Form\Traits
 */
trait SearchTemplatesTrait
{
    /**
     * Get component instance by id or alias
     *
     * @param string $id
     * @return object|null
     */
    abstract public function get(string $id): ?object;

    /**
     * Find component in this class or any nested group using an alias path or specific id
     *
     * @param string $id Dot separated alias path or id
     * @return object|null
     */
    abstract public function find(string $id): ?object;

    /**
     * Get template by id or alias
     *
     * @param string $id
     * @param bool $deep
     * @return Template|null
     */
    public function getTemplate(string $id, bool $deep = false): ?Template
    {
        return ($template = $this->{$deep ? 'find' : 'get'}($id)) !== null && $template instanceof Template ? $template : null;
    }
}
