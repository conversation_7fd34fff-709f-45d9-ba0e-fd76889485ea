<?php

declare(strict_types=1);

namespace App\Services\Form\Traits;

/**
 * Trait OrderTrait
 *
 * @package App\Services\Form\Traits
 */
trait OrderableTrait
{
    /**
     * @var int|null Order value
     */
    protected ?int $order = null;

    /**
     * Set order
     *
     * @param int $order
     * @return $this
     */
    public function setOrder(int $order)
    {
        $this->order = $order;
        return $this;
    }

    /**
     * Get order
     *
     * @return int|null
     */
    public function getOrder(): ?int
    {
        return $this->order;
    }
}
