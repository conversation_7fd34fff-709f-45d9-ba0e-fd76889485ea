<?php

declare(strict_types=1);

namespace App\Services;

use App\Classes\{Acl, Func};
use App\ResourceJobs\Company\CustomReport\GenerateJob;
use App\Resources\Company\CustomReport\ResultResource;
use App\Resources\FileResource;
use App\Services\CompanyCustomReport\Exceptions\{CompanyCustomReportException, InputValidationException};
use Brick\Math\{BigDecimal, RoundingMode};
use Carbon\Carbon;
use Closure;
use Common\Models\{CompanyCustomReport, CompanyCustomReportResult};
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Resource\Classes\{BatchRequest, Entity};
use Core\Components\Resource\Requests\{CreateRequest, UpdateRequest};
use Core\Components\Validation\Classes\{FieldConfig, Rules, Validation};
use Core\Components\Validation\Rules\DateTimeRule;
use Core\StaticAccessors\Path;
use DateTimeZone;
use League\Csv\Writer;
use SplFileObject;
use Throwable;
use Ramsey\Uuid\{Uuid, UuidInterface};

/**
 * Class CompanyCustomReportService
 *
 * @package App\Services
 */
class CompanyCustomReportService
{
    /**
     * @var array Cache of service instances keyed by report uuid
     */
    protected static array $cache = [];

    /**
     * @var DateTimeZone|null Timezone of company associated with report
     */
    protected ?DateTimeZone $timezone = null;

    /**
     * @var array|null Config cache
     */
    protected ?array $config = null;

    /**
     * @var string|null Query cache
     */
    protected ?string $query = null;

    /**
     * Create instance from model or pull from cache
     *
     * @param CompanyCustomReport $model
     * @return static
     */
    public static function fromModel(CompanyCustomReport $model): self
    {
        $cache_id = $model->getUuidKey()->toString();
        if (isset(static::$cache[$cache_id])) {
            return static::$cache[$cache_id];
        }
        $instance = new static($model);
        static::$cache[$cache_id] = $instance;
        return $instance;
    }

    /**
     * Create instance from id or pull from cache
     *
     * @param UuidInterface $id
     * @return static
     * @throws CompanyCustomReportException
     */
    public static function fromID(UuidInterface $id): self
    {
        $cache_id = $id->toString();
        if (isset(static::$cache[$cache_id])) {
            return static::$cache[$cache_id];
        }
        if (($model = CompanyCustomReport::find($id->getBytes())) === null) {
            throw new CompanyCustomReportException('Unable to find custom report: %s', $id->toString());
        }
        $instance = new static($model);
        static::$cache[$cache_id] = $instance;
        return $instance;
    }

    /**
     * CompanyCustomReportService constructor
     *
     * @param CompanyCustomReport $model
     */
    protected function __construct(protected CompanyCustomReport $model)
    {}

    /**
     * Get timezone for company
     *
     * @return DateTimeZone
     */
    protected function getTimezone(): DateTimeZone
    {
        if ($this->timezone === null) {
            $this->timezone = new DateTimeZone(TimeService::getTimezoneForCompany($this->model->companyID));
        }
        return $this->timezone;
    }

    /**
     * Get path to custom report config directory
     *
     * @param string $path
     * @return string
     */
    protected function getPath(string $path): string
    {
        return Path::customReports("{$this->model->getUuidKey()->toString()}/{$path}");
    }

    /**
     * Get report config from file
     *
     * @return array
     * @throws CompanyCustomReportException
     */
    protected function getConfig(): array
    {
        if ($this->config === null) {
            $path = $this->getPath('config.php');
            if (!file_exists($path)) {
                throw new CompanyCustomReportException('Unable to find config file: %s', $path);
            }
            $config = include $path;
            if (!is_array($config)) {
                throw new CompanyCustomReportException('Config file does not return array: %s', $path);
            }
            $this->config = $config;
        }
        return $this->config;
    }

    /**
     * Get list of available columns
     *
     * @return array
     * @throws CompanyCustomReportException
     */
    public function getColumns(): array
    {
        $columns = $this->getConfig()['columns'] ?? null;
        if ($columns === null) {
            throw new CompanyCustomReportException('Columns not defined in config');
        }
        return $columns;
    }

    /**
     * Get list of available inputs
     *
     * @return array
     * @throws CompanyCustomReportException
     */
    public function getInputs(): array
    {
        return $this->getConfig()['inputs'] ?? [];
    }

    /**
     * Get and cache report query from file
     *
     * @return string
     * @throws CompanyCustomReportException
     */
    protected function getQuery(): string
    {
        if ($this->query === null) {
            $path = $this->getPath('query.sql');
            if (!file_exists($path)) {
                throw new CompanyCustomReportException('Unable to find query file: %s', $path);
            }
            if (($query = file_get_contents($path)) === false) {
                throw new CompanyCustomReportException('Unable to get query contents from file: %s', $path);
            }
            $this->query = $query;
        }
        return $this->query;
    }

    /**
     * Determines if query is scoped to company by checking for presence of company_id variable
     *
     * @return bool
     * @throws CompanyCustomReportException
     */
    public function isQueryScoped(): bool
    {
        return str_contains($this->getQuery(), '{{company_id}}');
    }

    /**
     * Get config to use in resource entity relation
     *
     * @return array[]
     * @throws CompanyCustomReportException
     */
    public function getEntityConfig(): array
    {
        $config = [
            'columns' => [],
            'inputs' => []
        ];
        foreach ($this->getColumns() as $column) {
            $type = $column['type'] ?? 'string';
            $column_config = [
                'title' => $column['title'],
                'type' => $type
            ];
            switch ($type) {
                case 'link':
                    $column_config['link_title'] = $column['link_title'] ?? null;
                    break;
            }
            $config['columns'][] = $column_config;
        }
        foreach ($this->getInputs() as $name => $input_config) {
            $input = [
                'name' => $name,
                'type' => $input_config['type'] ?? 'string',
                'label' => $input_config['label']
            ];
            if ($input_config['type'] === 'dropdown' && isset($input_config['options'])) {
                $input['options'] = $input_config['options'];
            }
            if ($input_config['type'] === 'number' && isset($input_config['number_type'])) {
                $input['number_type'] = $input_config['number_type'];
            }
            $config['inputs'][] = $input;
        }
        return $config;
    }

    /**
     * Get validation instance configured for company
     *
     * @return Validation
     * @throws CompanyCustomReportException
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     */
    protected function getValidation(): Validation
    {
        $rules = new Rules();
        $rules->getRuleInstance(DateTimeRule::class)->setTimezone($this->getTimezone());
        return Validation::make()
            ->config(FieldConfig::fromArray($this->getConfig()['inputs'] ?? []))
            ->rules($rules);
    }

    /**
     * Validate inputs and return sanitized data
     *
     * @param array $inputs
     * @return array
     * @throws CompanyCustomReportException
     * @throws InputValidationException
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     */
    protected function validateInputs(array $inputs): array
    {
        $validator = $this->getValidation()->run($inputs);
        if (!$validator->valid()) {
            throw (new InputValidationException('Report input is not valid'))->setErrors($validator->errors()->get());
        }
        return $validator->data();
    }

    /**
     * Get query param with filters applied
     *
     * @param mixed $value
     * @param array $filters
     * @return mixed
     * @throws CompanyCustomReportException
     */
    protected function getQueryParam(mixed $value, array $filters = []): mixed
    {
        foreach ($filters as $filter) {
            switch ($filter) {
                case 'uuid':
                    $value = Uuid::fromString($value)->getBytes();
                    break;
                case 'date':
                case 'utc_date':
                case 'datetime':
                case 'utc_datetime':
                    $value = Carbon::parse($value, in_array($filter, ['utc_date', 'utc_datetime']) ? 'UTC' : $this->getTimezone());
                    break;
                case 'sod':
                    if (!($value instanceof Carbon)) {
                        throw new CompanyCustomReportException('Start of day filter requires a carbon instance');
                    }
                    $value->startOfDay();
                    break;
                case 'eod':
                    if (!($value instanceof Carbon)) {
                        throw new CompanyCustomReportException('Start of day filter requires a carbon instance');
                    }
                    $value->endOfDay();
                    break;
                case 'to_utc':
                    if (!($value instanceof Carbon)) {
                        throw new CompanyCustomReportException('To UTC filter requires a carbon instance');
                    }
                    $value->timezone('UTC');
                    break;
            }
        }
        return $value;
    }

    /**
     * Convert value to use in CSV export
     *
     * @param mixed $value
     * @param string $type
     * @param array $config
     * @return string
     */
    protected function getCsvValue($value, string $type, array $config = []): string
    {
        if ($value === null || $value === '') {
            return '';
        }
        switch ($type) {
            case 'date':
            case 'datetime':
                $datetime = Carbon::parse($value, $config['timezone'] ?? 'UTC');
                if ($type === 'datetime') {
                    $datetime->timezone($this->getTimezone());
                }
                $formats = [
                    'date' => 'Y-m-d',
                    'datetime' => 'Y-m-d H:i:s'
                ];
                $value = $datetime->format($formats[$type]);
                break;
            case 'currency':
                $value = (string) BigDecimal::of($value)->toScale(2, RoundingMode::HALF_DOWN);
                break;
            case 'number':
                $value = (string) $value;
                if (str_contains($value, '.')) {
                    $value = rtrim(rtrim($value, '0'), '.');
                }
                if ($value === '') {
                    $value = '0';
                }
                break;
        }
        if (!is_string($value)) {
            $value = (string) $value;
        }
        return $value;
    }

    /**
     * Build query using supplied vars
     *
     * @param array $vars
     * @return array
     * @throws CompanyCustomReportException
     */
    protected function buildQuery(array $vars): array
    {
        $query = $this->getQuery();
        $bindings = [];
        $query = preg_replace_callback('#{{([A-Za-z0-9_\-.:|]+)}}#', function ($matches) use ($vars, &$bindings) {
            $filters = [];
            $var = $matches[1];
            if (str_contains($var, '|')) {
                $filters = explode('|', $var);
                $var = array_shift($filters);
            }
            if (!array_key_exists($var, $vars)) {
                throw new CompanyCustomReportException('Var %s is not defined', $var);
            }
            $param = $vars[$var];
            if (!is_array($param)) {
                $bindings[] = $this->getQueryParam($param, $filters);
                return '?';
            }
            $bindings = array_merge($bindings, array_map(fn($param) => $this->getQueryParam($param, $filters), $param));
            return implode(',', array_fill(0, count($param), '?'));
        }, $query);
        return [$query, $bindings];
    }

    /**
     * Get inputs payload for use with storing via model
     *
     * @param array $data
     * @return array
     * @throws CompanyCustomReportException
     */
    protected function getInputsPayload(array $data): array
    {
        $inputs = $this->getConfig()['inputs'] ?? [];
        $payload = [];
        foreach ($data as $name => $value) {
            if (!isset($inputs[$name])) {
                continue;
            }
            switch ($inputs[$name]['type']) {
                case 'date':
                    $payload[$name] = $value->format('Y-m-d');
                    break;
                case 'dropdown':
                case 'text':
                case 'number':
                    $payload[$name] = $value;
                    break;
            }
        }
        return $payload;
    }

    /**
     * Queue a report run for a user with specified inputs
     *
     * @param int $user_id
     * @param array $inputs
     * @return UuidInterface
     * @throws CompanyCustomReportException
     * @throws InputValidationException
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     */
    public function queueRun(int $user_id, array $inputs): UuidInterface
    {
        $inputs = $this->validateInputs($inputs);
        $result_id = Uuid::uuid4();
        ResultResource::make(Acl::make())->create(Entity::make([
            'id' => $result_id->toString(),
            'custom_report_id' => $this->model->getUuidKey()->toString(),
            'user_id' => $user_id,
            'inputs' => $this->getInputsPayload($inputs),
            'status' => ResultResource::STATUS_GENERATING
        ]))->run();

        GenerateJob::enqueue($result_id);

        return $result_id;
    }

    /**
     * Generate report from inputs
     *
     * @param array $inputs
     * @param int $row_count
     * @return SplFileObject
     * @throws CompanyCustomReportException
     * @throws \Core\Exceptions\AppException
     * @throws \League\Csv\CannotInsertRecord
     */
    protected function generate(array $inputs, int &$row_count = 0): SplFileObject
    {
        $input_config = $this->getConfig()['inputs'] ?? [];
        $vars = array_merge($this->getConfig()['vars'] ?? [], $inputs, ['company_id' => $this->model->companyID]);

        // loop through vars to determine if we need to manipulate any vars for the query
        foreach ($vars as $var => &$value) {
            if (!isset($input_config[$var])) {
                continue;
            }
            $config = $input_config[$var];
            if ($config['type'] === 'text') {
                $value = "%{$value}%";
                continue;
            }
            if ($config['type'] !== 'dropdown') {
                continue;
            }
            // manipulate dropdown into an array and either do a lookup of additional ids or push the id to vars
            $new_values = [];
            foreach ($value as $item) {
                $ids = $config['options'][$item]['value'];
                if (!is_array($ids)) {
                    array_push($new_values, "{$ids}");
                    continue;
                }
                foreach($ids as $id) {
                    array_push($new_values, "{$id}");
                }
            }
            $value = $new_values;
        }

        [$query, $bindings] = $this->buildQuery($vars);
        $data = DB::cursor($query, $bindings);

        try {
            $temp_file = Func::createTempFile('csv');
            $writer = Writer::createFromFileObject($temp_file);
            $columns = $this->getConfig()['columns'];
            $headers = [];
            foreach ($columns as $column => &$config) {
                if (is_string($column)) {
                    $config['column'] = $column;
                }
                if (!isset($config['column']) && !isset($config['value'])) {
                    throw new CompanyCustomReportException("No column name or value defined for {$column}");
                }
                $config['value_closure'] = isset($config['value']) && $config['value'] instanceof Closure;
                $headers[] = $config['title'] ?? '';
                unset($config);
            }
            $writer->insertOne($headers);
            foreach ($data as $datum) {
                $datum = (array) $datum;
                $row = [];
                foreach ($columns as $config) {
                    $value = null;
                    if (isset($config['value'])) {
                        $value = $config['value'];
                        if ($config['value_closure']) {
                            $value = $value($datum);
                        }
                    } else {
                        $value = $datum[$config['column']];
                    }
                    $row[] = $this->getCsvValue($value, $config['type'] ?? 'string', $config);
                }
                $writer->insertOne($row);
                $row_count++;
            }
            return $temp_file;
        } catch (Throwable $e) {
            if (isset($temp_file)) {
                $file_path = $temp_file->getPathname();
                unset($temp_file);
                if (!unlink($file_path)) {
                    throw new CompanyCustomReportException('Unable to unlink file: %s', $file_path);
                }
            }
            throw $e;
        }
    }

    /**
     * Run report for specified result
     *
     * @param CompanyCustomReportResult $result
     * @param bool $save
     * @throws CompanyCustomReportException
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Exceptions\AppException
     * @throws \League\Csv\CannotInsertRecord
     */
    public function run(CompanyCustomReportResult $result, bool $save = true)
    {
        if ($result->status !== CompanyCustomReportResult::STATUS_GENERATING) {
            throw new CompanyCustomReportException('Result must be in generating status to create CSV');
        }

        try {
            $start_time = microtime(true);

            $row_count = 0;
            $file = $this->generate($result->inputs, $row_count);

            $total_time = (int) round(((microtime(true) - $start_time) * 1000000) / 1000);

            $result_resource = ResultResource::make(Acl::make()->setCompanyID($this->model->companyID));

            $batch = BatchRequest::make()->sequential();

            $file_request = FileResource::make(Acl::make($result->user))->create(Entity::make([
                'type' => FileResource::TYPE_CUSTOM_REPORT_RESULT,
                'status' => FileResource::STATUS_FINISHED,
                'name' => 'report.csv',
                'path' => $file->getPathname(),
                'output_directory' => $result_resource->getMedia()->get('file')->getOriginal()->getPath(),
                'time' => $total_time
            ]));
            $file_request->store('_content_type', 'text/csv');
            $batch->add($file_request);

            $custom_report_request = $result_resource->partialUpdate(Entity::make([
                'id' => $result_resource->getPrimaryField()->outputValueFromModel($result),
                'status' => ResultResource::STATUS_GENERATED,
                'row_count' => $row_count,
                'time' => $total_time
            ]))
                ->attach('batch.last_request', function (CreateRequest $file_request, UpdateRequest $custom_report_request) {
                    $entity = $custom_report_request->getEntity();
                    $entity->set('file_id', $file_request->response());
                });
            $batch->add($custom_report_request);

            $batch->run();
        } catch (Throwable $e) {
            $result->fill([
                'status' => CompanyCustomReportResult::STATUS_FAILED,
                'failedAt' => Carbon::now('UTC')
            ])->save();
            throw $e;
        }
    }

    /**
     * Run test report using specified inputs
     *
     * @param array $inputs
     * @return SplFileObject
     * @throws CompanyCustomReportException
     * @throws InputValidationException
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     * @throws \Core\Exceptions\AppException
     * @throws \League\Csv\CannotInsertRecord
     */
    public function testRun(array $inputs): SplFileObject
    {
        $inputs = $this->validateInputs($inputs);
        return $this->generate($this->getInputsPayload($inputs));
    }
}
