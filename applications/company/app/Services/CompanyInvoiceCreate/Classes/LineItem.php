<?php

declare(strict_types=1);

namespace App\Services\CompanyInvoiceCreate\Classes;

use App\Resources\Company\Invoice\LineItemResource;
use App\Services\CompanyInvoiceCreate\Classes\LineItems;
use App\Services\CompanyInvoiceCreate\Exceptions\CompanyInvoiceCreateException;
use App\Traits\ArrayImportExportTrait;
use Brick\Math\{BigDecimal, RoundingMode};
use Core\Components\Resource\Classes\Entity;
use Core\Exceptions\AppException;
use InvalidArgumentException;

/**
 * Class LineItem
 *
 * @package App\Services\CompanyInvoiceCreate\Classes
 */
class LineItem
{
    use ArrayImportExportTrait;

    public const AMOUNT_TYPE_TOTAL = 1;
    public const AMOUNT_TYPE_PERCENTAGE = 2;

    /**
     * @var string[] Mapping of type to class
     */
    protected static array $type_map = [
        LineItemResource::TYPE_GENERAL => self::class,
        LineItemResource::TYPE_ADD_ON => LineItems\AddOnLineItem::class,
        LineItemResource::TYPE_DISCOUNT => LineItems\DiscountLineItem::class,
        LineItemResource::TYPE_FEE => LineItems\FeeLineItem::class,
        LineItemResource::TYPE_CREDIT => LineItems\CreditLineItem::class,
        LineItemResource::TYPE_SUBSCRIPTION => LineItems\SubscriptionLineItem::class,
        LineItemResource::TYPE_SUBSCRIPTION_ADD_ON => LineItems\SubscriptionAddOnLineItem::class,
        LineItemResource::TYPE_SUBSCRIPTION_DISCOUNT => LineItems\SubscriptionDiscountLineItem::class,
        LineItemResource::TYPE_SUBSCRIPTION_FEE => LineItems\SubscriptionFeeLineItem::class
    ];

    /**
     * @var int Type
     */
    protected int $type = LineItemResource::TYPE_GENERAL;

    /**
     * @var null|string Description for line item
     */
    protected ?string $description = null;

    /**
     * @var null|BigDecimal Amount
     */
    protected ?BigDecimal $amount = null;

    /**
     * @var null|BigDecimal Quantity
     */
    protected ?BigDecimal $quantity = null;

    /**
     * @var int Amount type
     */
    protected int $amount_type = self::AMOUNT_TYPE_TOTAL;

    /**
     * @var null|BigDecimal Base total for use with percentages
     */
    protected ?BigDecimal $base_total = null;

    /**
     * @var bool Determines if amount is negated
     */
    protected bool $negate = false;

    /**
     * @var bool Determines if line item is refundable
     */
    protected bool $is_refundable = true;

    /**
     * Get available amount types
     *
     * @return int[]
     */
    public static function getAmountTypes(): array
    {
        return [self::AMOUNT_TYPE_TOTAL, self::AMOUNT_TYPE_PERCENTAGE];
    }

    /**
     * Make new instance by type id
     *
     * @param int $type
     * @param array $data
     * @return static
     * @throws CompanyInvoiceCreateException
     */
    public static function makeByType(int $type, array $data = []): self
    {
        if (!isset(static::$type_map[$type])) {
            throw new CompanyInvoiceCreateException('Unable to find class for type: %d', $type);
        }
        return new static::$type_map[$type]($data);
    }

    /**
     * Create instance
     *
     * @param array $data
     * @return static
     * @throws AppException
     */
    public static function make(array $data = []): self
    {
        return new static($data);
    }

    /**
     * LineItem constructor
     *
     * @param array $data
     * @throws AppException
     */
    public function __construct(array $data = [])
    {
        if (count($data) > 0) {
            $this->hydrate($data);
        }
    }

    /**
     * Handle clone of line item
     */
    public function __clone()
    {
        if ($this->amount !== null) {
            $this->amount = clone $this->amount;
        }
        if ($this->quantity !== null) {
            $this->quantity = clone $this->quantity;
        }
        if ($this->base_total !== null) {
            $this->base_total = clone $this->base_total;
        }
    }

    /**
     * Setup class data from array
     *
     * @param array $data
     * @throws AppException
     */
    public function hydrate(array $data): void
    {
        $this->importFromArray([
            'description' => ['string', 'setDescription'],
            'amount_type' => ['int', 'setAmountType'],
            'amount' => ['string', 'setAmount'],
            'quantity' => ['string', 'setQuantity'],
            'is_refundable' => ['bool', 'setIsRefundable']
        ], $data);
    }

    /**
     * Get type
     *
     * @return int
     */
    public function getType(): int
    {
        return $this->type;
    }

    /**
     * Set description
     *
     * @param string $description
     * @return $this
     */
    public function setDescription(string $description): self
    {
        $this->description = $description;
        return $this;
    }

    /**
     * Get description
     *
     * @return string|null
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * Set total
     *
     * Must be a string since we don't work with dollar amounts as integers or floats due to precision problems
     *
     * @param BigDecimal $total
     * @return $this
     */
    public function setBaseTotal(BigDecimal $total): self
    {
        $this->base_total = $total;
        return $this;
    }

    /**
     * Get base total
     *
     * @return BigDecimal
     * @throws AppException
     */
    public function getBaseTotal(): BigDecimal
    {
        if ($this->base_total === null) {
            throw new AppException('Base total not defined');
        }
        return $this->base_total;
    }

    /**
     * Set amount type
     *
     * See AMOUNT_TYPE_* constants
     *
     * @param int $type
     * @return $this
     */
    public function setAmountType(int $type): self
    {
        if (!in_array($type, self::getAmountTypes(), true)) {
            throw new InvalidArgumentException('Invalid amount type');
        }
        $this->amount_type = $type;
        return $this;
    }

    /**
     * Get amount type
     *
     * @return int
     */
    public function getAmountType(): int
    {
        return $this->amount_type;
    }

    /**
     * Set amount
     *
     * @param string $amount
     * @return $this
     */
    public function setAmount(string $amount): self
    {
        if (!is_numeric($amount)) {
            throw new InvalidArgumentException('Amount must be numeric string');
        }
        $this->amount = BigDecimal::of($amount)->toScale(2);
        return $this;
    }

    /**
     * Get amount
     *
     * @return BigDecimal
     */
    public function getAmount(): ?BigDecimal
    {
        return $this->amount;
    }

    /**
     * Set quantity
     *
     * @param string $quantity
     * @return $this
     */
    public function setQuantity(string $quantity): self
    {
        if (!is_numeric($quantity)) {
            throw new InvalidArgumentException('Quantity must be numeric string');
        }
        $this->quantity = BigDecimal::of($quantity);
        return $this;
    }

    /**
     * Get quantity
     *
     * @return BigDecimal|null
     */
    public function getQuantity(): ?BigDecimal
    {
        return $this->quantity;
    }

    /**
     * Get total
     *
     * This takes into consideration the amount type before calculation. If a relative type like percentage is passed
     * then the amount is determined from the base total which has to be set on the line item.
     *
     * @return BigDecimal
     * @throws AppException
     * @throws CompanyInvoiceCreateException
     */
    public function getTotal(): BigDecimal
    {
        if (($amount = $this->getAmount()) === null || ($quantity = $this->getQuantity()) === null) {
            throw new CompanyInvoiceCreateException('Amount and quantity are required to calculate total');
        }
        if ($this->negate) {
            $amount = $amount->negated();
        }
        $total = $quantity->multipliedBy($amount);
        switch ($this->getAmountType()) {
            case self::AMOUNT_TYPE_PERCENTAGE:
                $total = $total->dividedBy('100')->multipliedBy($this->getBaseTotal());
                break;
        }
        return $total->toScale(2, RoundingMode::HALF_DOWN);
    }

    /**
     * Set is refundable flag
     *
     * @param bool $status
     * @return $this
     */
    public function setIsRefundable(bool $status): self
    {
        $this->is_refundable = $status;
        return $this;
    }

    /**
     * Get is refundable status
     *
     * @return bool
     */
    public function getIsRefundable(): bool
    {
        return $this->is_refundable;
    }

    /**
     * Convert line item data to entity
     *
     * @return Entity
     * @throws AppException
     * @throws CompanyInvoiceCreateException
     */
    public function toEntity(): Entity
    {
        return Entity::make([
            'type' => $this->getType(),
            'description' => $this->getDescription(),
            'amount' => (string) $this->getAmount(),
            'quantity' => (string) $this->getQuantity(),
            'total' => (string) $this->getTotal(),
            'is_refundable' => $this->getIsRefundable()
        ]);
    }
}
