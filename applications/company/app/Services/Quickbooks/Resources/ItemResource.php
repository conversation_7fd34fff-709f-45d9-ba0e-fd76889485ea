<?php

declare(strict_types=1);

namespace App\Services\Quickbooks\Resources;

use App\Services\Quickbooks\Classes\Resource;

/**
 * Class ItemResource
 *
 * @package App\Services\Quickbooks\Resources
 * @method \QuickBooksOnline\API\Data\IPPItem|null find($id)
 * @method \QuickBooksOnline\API\Data\IPPItem findOrFail($id)
 * @method \QuickBooksOnline\API\Data\IPPItem[] all(?\Closure $query = null)
 */
class ItemResource extends Resource
{
    /**
     * @var string entity name
     */
    protected $name = 'Item';
}
