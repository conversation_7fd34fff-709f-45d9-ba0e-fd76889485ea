<?php

namespace App\Services\AppNotification\Types\User;

use App\Classes\Acl;
use App\Resources\AppNotificationDistributionResource;
use App\Resources\AppNotificationResource;
use App\Resources\LeadResource;
use App\Services\AppNotification\AppNotificationService;
use Common\Models\AppNotification;
use Common\Models\Lead;
use Common\Models\User;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\Scope;
use Core\Exceptions\AppException;
use Ramsey\Uuid\Uuid;
use Carbon\Carbon;
use Throwable;

class LeadAssignedType
{
    /**
     * Create an in-app notification when a lead is assigned to a user.
     *
     * @param Lead $lead
     * @return void
     * @throws AppException
     */
    public static function create(Lead $lead): void
    {
        $user = User::find($lead->assignedToUserID);
        if (!$user) {
            throw new AppException('LeadAssignedType: Unable to find user with ID ' . $lead->assignedToUserID);
        }

        $title = "Lead Assigned";

        try {
            $lead_scope = Scope::make()
                ->fields(['id', 'lead_uuid', 'company_id', 'first_name', 'last_name', 'priority', 'priority_name', 'project_type_name']);
            $lead_item = LeadResource::make(Acl::make())
                ->entity($lead->leadID)
                ->scope($lead_scope)
                ->run();

            $summary = self::generateNotificationSummary($lead_item);
            $metadata = [
                'sub_type' => "lead_assigned",
            ];

            DB::beginTransaction();

            $entity = [
                'title'             => $title,
                'summary'           => $summary,
                'association_type'  => AppNotificationResource::ASSOCIATION_TYPE_LEAD,
                'association_id'    => $lead->leadID,
                'association_uuid'  => $lead->leadUUID,
                'metadata'          => json_encode($metadata),
                'placement'         => AppNotification::PLACEMENT_NOTIFICATION_CENTER,
                'type'              => AppNotification::TYPE_INFO,
                'status'            => AppNotification::STATUS_ACTIVE,
            ];

            $acl = Acl::make()->setUser($user);
            AppNotificationResource::make($acl)
                ->withAccessLevel(Field::ACCESS_LEVEL_PRIVATE, function ($resource) use (&$id, $entity) {
                    $id = $resource->create(Entity::make($entity))->run();
                });

            $distribution = [
                'app_notification_id' => $id,
                'user_id'             => $user->userID,
                'company_id'          => $lead->companyID,
                'status'              => AppNotificationDistributionResource::STATUS_UNREAD,
            ];

            AppNotificationDistributionResource::make($acl)
                ->withAccessLevel(Field::ACCESS_LEVEL_PRIVATE, function ($resource) use ($distribution) {
                    $resource->create(Entity::make($distribution))->run();
                });

            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();
            throw new AppException('LeadAssignedType: Unable to send notification', $e->getMessage());
        }
    }


    /**
     * Generates the summary for the notification.
     *
     * @param mixed $lead
     * @return string
     */
    private static function generateNotificationSummary(mixed $lead): string
    {
        // Trim all data upfront
        $first_name = trim($lead['first_name'] ?? '');
        $last_name = trim($lead['last_name'] ?? '');
        $project_type_name = trim($lead['project_type_name'] ?? '');
        $priority_name = trim($lead['priority_name'] ?? '');

        // Create full-name (handles cases where either name might be empty)
        $full_name = trim("{$first_name} {$last_name}");

        // Get priority-class if priority exists
        $priority_class = '';
        if (isset($lead['priority'])) {
            switch ($lead['priority']) {
                case LeadResource::PRIORITY_COLD:
                    $priority_class = ' t-blue';
                    break;
                case LeadResource::PRIORITY_WARM:
                    $priority_class = ' t-yellow';
                    break;
                case LeadResource::PRIORITY_HOT:
                    $priority_class = ' t-red';
                    break;
                case LeadResource::PRIORITY_DEAD:
                    $priority_class = ' t-grey';
                    break;
            }
        }

        // Build HTML components
        $name_html = "<div class=\"ns-im-wrapper\"><span class=\"ns-imw-label\">Lead:</span> {$full_name}</div>";

        $project_type_html = null;
        if (!empty($project_type_name)) {
            $project_type_html = "<div class=\"ns-im-wrapper\"><span class=\"ns-imw-label\">Project Type:</span> <span class=\"ns-imw-tag\">{$project_type_name}</span></div>";
        }

        $priority_html = null;
        if (!empty($priority_name)) {
            $priority_html = "<div class=\"ns-im-wrapper\"><span class=\"ns-imw-label\">Priority:</span> <span class=\"ns-imw-tag{$priority_class}\">{$priority_name}</span></div>";
        }

        // Assemble the final summary
        $summary = '';
        $summary .= $name_html;

        if ($priority_html !== null) $summary .= $priority_html;
        if ($project_type_html !== null) $summary .= $project_type_html;

        return $summary;
    }
}