<?php

declare(strict_types=1);

namespace App\Services\GoogleApi\Calendar\Classes;

use App\Services\GoogleApi\Calendar\Classes\Calendar\Event;
use App\Services\GoogleApi\Calendar\Exceptions\Api\{Gone\FullSyncRequiredException, ResourceNotFoundException};
use App\Services\GoogleApi\Calendar\Exceptions\{
    InsufficientPermissionsException,
    RemovedException,
    RemoveInProgressException,
    SyncInProgressException
};
use App\Services\GoogleApi\Calendar\Interfaces\EventDetailInterface;
use App\Services\GoogleApi\Calendar\Classes\Calendar\NotificationChannel;
use App\Services\GoogleApi\Calendar\Entities\EventEntity;
use App\Services\GoogleApi\Calendar\Jobs\{EventPullAllJob, ExpandPullWindowJob, ExpandPushWindowJob};
use App\Services\GoogleApi\Exceptions\{CalendarException, GoogleApiException};
use App\Services\GoogleApi\Calendar\Jobs\{NotificationChannelStartJob, RemoveJob};
use App\Services\GoogleApi\Services\CalendarService;
use App\Services\{GoogleApiService, TimeService, TrainingService};
use Carbon\Carbon;
use Closure;
use Common\Models\{
    CompanyEvent,
    GoogleCalendar,
    GoogleCalendarEvent,
    GoogleCalendarNotificationChannel,
    TrainingAction,
    UserEvent
};
use Core\Components\DB\StaticAccessors\DB;
use Generator;
use Google_Service_Calendar;
use Illuminate\Database\Eloquent\Collection;
use Ramsey\Uuid\{Uuid, UuidInterface};

/**
 * Class Calendar
 *
 * @package App\Services\GoogleApi\Calendar\Classes
 */
class Calendar
{
    public const OWNER_TYPE_USER = 1;
    public const OWNER_TYPE_COMPANY = 2;

    public const PULL_WINDOW_DAYS_BEFORE = 60;
    public const PULL_WINDOW_DAYS_AFTER = 180;

    public const PUSH_WINDOW_DAYS_BEFORE = 60;
    public const PUSH_WINDOW_DAYS_AFTER = 180;

    /**
     * @var array Mapping of owner types to their model values
     */
    protected static array $owner_type_map = [
        self::OWNER_TYPE_USER => GoogleCalendar::OWNER_TYPE_USER,
        self::OWNER_TYPE_COMPANY => GoogleCalendar::OWNER_TYPE_COMPANY
    ];

    /**
     * @var array Event types accepted by each owner type
     */
    protected static array $accepted_event_types = [
        self::OWNER_TYPE_USER => [Event::TYPE_PROJECT, Event::TYPE_USER],
        self::OWNER_TYPE_COMPANY => [Event::TYPE_COMPANY]
    ];

    /**
     * @var string[] Mapping of owner type to their models for pulled events
     */
    protected static array $owner_type_pulled_event_models = [
        self::OWNER_TYPE_USER => UserEvent::class,
        self::OWNER_TYPE_COMPANY => CompanyEvent::class
    ];

    /**
     * @var CalendarService Calendar service instance
     */
    protected CalendarService $service;

    /**
     * @var GoogleCalendar Google calendar model instance
     */
    protected GoogleCalendar $calendar;

    /**
     * Get available owner types
     *
     * @return int[]
     */
    public static function getOwnerTypes(): array
    {
        return [static::OWNER_TYPE_USER, static::OWNER_TYPE_COMPANY];
    }

    /**
     * Get owner type map
     *
     * @return array
     */
    public static function getOwnerTypeMap(): array
    {
        return self::$owner_type_map;
    }

    /**
     * Create calendar for user
     *
     * @param CalendarService $service
     * @param int $owner_type
     * @param string $id
     * @param string $name
     * @return static
     * @throws InsufficientPermissionsException
     */
    public static function create(CalendarService $service, int $owner_type, string $id, string $name): self
    {
        $user = $service->getApiService()->getUser();
        if ($owner_type === self::OWNER_TYPE_COMPANY && !$user->primary) {
            throw new InsufficientPermissionsException('Only primary users can make company calendars');
        }

        // if owner type is user, we remove any existing calendars since we only allow one at the moment. Companies
        // can have multiple.
        if ($owner_type === self::OWNER_TYPE_USER) {
            $current_calendars = $service->getCalendars($owner_type);
            if (count($current_calendars) > 0) {
                foreach ($current_calendars as $calendar) {
                    $calendar->queueRemove();
                }
            }
        }

        $owner_id = $owner_type === self::OWNER_TYPE_USER ? $user->getKey() : $user->companyID;
        $calendar_id = Uuid::uuid4();
        /** @var GoogleCalendar $calendar */
        $calendar = GoogleCalendar::create([
            'googleCalendarID' => $calendar_id->getBytes(),
            'googleServiceID' => $service->getService()->getKey(),
            'ownerType' => static::$owner_type_map[$owner_type],
            'ownerID' => $owner_id,
            'status' => GoogleCalendar::STATUS_ADDED,
            'calendarID' => $id,
            'name' => $name,
            'pullStatus' => GoogleCalendar::PULL_STATUS_NEVER,
            'pushStatus' => GoogleCalendar::PUSH_STATUS_NEVER
        ]);
        $instance = new static($service, $calendar);
        $instance->queueNotificationChannelCreate();

        // @todo move to event system (once built) to prevent this tight of coupling
        $training_action = match ($owner_type) {
            self::OWNER_TYPE_USER => TrainingAction::CONNECT_USER_GOOGLE_CALENDAR,
            self::OWNER_TYPE_COMPANY => TrainingAction::CONNECT_COMPANY_GOOGLE_CALENDAR
        };
        TrainingService::queueCompleteUserAction($user, $training_action);

        return $instance;
    }

    /**
     * Find calendar by id
     *
     * @param UuidInterface $id
     * @return Calendar|null
     */
    public static function findByID(UuidInterface $id): ?Calendar
    {
        $calendar = GoogleCalendar::query()
            ->withOAuthUser()
            ->whereKey($id->getBytes())
            ->first();
        if ($calendar === null) {
            return null;
        }
        $api_service = new GoogleApiService($calendar->oAuthUser);
        $calendar_service = new CalendarService($api_service);
        return new Calendar($calendar_service, $calendar);
    }

    /**
     * Check if calendars exist for an owner
     *
     * @param int $owner_type
     * @param int $owner_id
     * @return bool
     */
    public static function existsForOwner(int $owner_type, int $owner_id): bool
    {
        return GoogleCalendar::query()
            ->where('googleCalendars.ownerType', static::$owner_type_map[$owner_type])
            ->where('googleCalendars.ownerID', $owner_id)
            ->where('googleCalendars.status', GoogleCalendar::STATUS_ADDED)
            ->count() > 0;
    }

    /**
     * Get list of calendars for a specific owner
     *
     * @param int $owner_type
     * @param int $owner_id
     * @param bool $include_user
     * @return array
     */
    public static function getListByOwner(int $owner_type, int $owner_id, bool $include_user = false): array
    {
        /** @var Collection $calendars */
        $results = GoogleCalendar::query()
            ->withOAuthUser()
            ->where('googleCalendars.ownerType', static::$owner_type_map[$owner_type])
            ->where('googleCalendars.ownerID', $owner_id)
            ->where('googleCalendars.status', '!=', GoogleCalendar::STATUS_REMOVED)
            ->get();
        $calendars = [];
        if (count($results) > 0) {
            foreach ($results as $result) {
                $calendar = [
                    'id' => $result->getUuidKey()->toString(),
                    'name' => "{$result->name} ({$result->calendarID})",
                    'status' => $result->status,
                    'last_pull_at' => $result->lastPullAt !== null ? $result->lastPullAt->format('c') : null,
                    'last_push_at' => $result->lastPushAt !== null ? $result->lastPushAt->format('c') : null
                ];
                if ($include_user) {
                    $oauth_user = $result->oAuthUser;
                    $calendar['oauth_user'] = [
                        'id' => $oauth_user->getKey(),
                        'first_name' => $oauth_user->userFirstName,
                        'last_name' => $oauth_user->userLastName
                    ];
                }
                $calendars[] = $calendar;
            }
        }
        return $calendars;
    }

    /**
     * Enqueue pull all jobs for any calendars which haven't been synced according to their min time
     *
     * @param int $minutes
     * @param int|null $limit
     * @param Carbon|null $now
     * @return int
     */
    public static function pullStale(int $minutes = 60, ?int $limit = null, ?Carbon $now = null): int
    {
        $now ??= Carbon::now('UTC');
        $query = GoogleCalendar::query()
            ->where('status', GoogleCalendar::STATUS_ADDED)
            ->whereIn('pullStatus', [GoogleCalendar::PULL_STATUS_NEVER, GoogleCalendar::PULL_STATUS_PULLED])
            ->where(function ($query) use ($now, $minutes) {
                $query->whereNull('lastPullAt')
                    ->orWhereRaw(
                        'lastPullAt <= DATE_SUB(?, INTERVAL IFNULL(pullAfterMinutes, ?) MINUTE)',
                        [$now->toDateTimeString(), $minutes]
                    );
            });
        if ($limit !== null) {
            $query->limit($limit);
        }
        /** @var GoogleCalendar[] $calendars */
        $calendars = $query->get('googleCalendarID');
        $count = count($calendars);
        if ($count > 0) {
            foreach ($calendars as $calendar) {
                EventPullAllJob::enqueue($calendar->getUuidKey());
            }
        }
        return $count;
    }

    /**
     * Expand pull window for all calendars which have passed the wait time limit
     *
     * @param int $days
     * @param int|null $limit
     * @param Carbon|null $now
     * @return int
     */
    public static function expandPullWindows(int $days, ?int $limit = null, ?Carbon $now = null): int
    {
        $now ??= Carbon::now('UTC');
        $query = GoogleCalendar::query()
            ->select(['googleCalendarID'])
            ->selectRaw(
                'DATE_ADD(?, INTERVAL IFNULL(pullWindowDaysAfter, ?) DAY) as endDate',
                [$now->toDateTimeString(), self::PULL_WINDOW_DAYS_AFTER]
            )
            ->where('status', GoogleCalendar::STATUS_ADDED)
            ->whereRaw(
                'pullWindowEndAt <= DATE_ADD(?, INTERVAL (IFNULL(pullWindowDaysAfter, ?) - IFNULL(pullWindowExpandWaitDays, ?)) DAY)',
                [$now->toDateTimeString(), self::PULL_WINDOW_DAYS_AFTER, $days]
            );
        if ($limit !== null) {
            $query->limit($limit);
        }
        /** @var GoogleCalendar[] $calendars */
        $calendars = $query->get();
        $count = count($calendars);
        if ($count > 0) {
            foreach ($calendars as $calendar) {
                ExpandPullWindowJob::enqueue($calendar->getUuidKey(), Carbon::parse($calendar->endDate, 'UTC')->endOfDay());
            }
        }
        return $count;
    }

    /**
     * Expand push window for all calendars which have passed the wait time limit
     *
     * @param int $days
     * @param int|null $limit
     * @param Carbon|null $now
     * @return int
     */
    public static function expandPushWindows(int $days, ?int $limit = null, ?Carbon $now = null): int
    {
        $now ??= Carbon::now('UTC');
        $query = GoogleCalendar::query()
            ->select(['googleCalendarID'])
            ->selectRaw(
                'DATE_ADD(?, INTERVAL IFNULL(pushWindowDaysAfter, ?) DAY) as endDate',
                [$now->toDateTimeString(), self::PUSH_WINDOW_DAYS_AFTER]
            )
            ->where('status', GoogleCalendar::STATUS_ADDED)
            ->whereRaw(
                'pushWindowEndAt <= DATE_ADD(?, INTERVAL (IFNULL(pushWindowDaysAfter, ?) - IFNULL(pushWindowExpandWaitDays, ?)) DAY)',
                [$now->toDateTimeString(), self::PUSH_WINDOW_DAYS_AFTER, $days]
            );
        if ($limit !== null) {
            $query->limit($limit);
        }
        /** @var GoogleCalendar[] $calendars */
        $calendars = $query->get();
        $count = count($calendars);
        if ($count > 0) {
            foreach ($calendars as $calendar) {
                ExpandPushWindowJob::enqueue($calendar->getUuidKey(), Carbon::parse($calendar->endDate, 'UTC')->endOfDay());
            }
        }
        return $count;
    }

    /**
     * Calendar constructor
     *
     * @param CalendarService $service
     * @param GoogleCalendar $calendar
     */
    public function __construct(CalendarService $service, GoogleCalendar $calendar)
    {
        $this->service = $service;
        $this->calendar = $calendar;
    }

    /**
     * Get model
     *
     * @return GoogleCalendar
     */
    public function getModel(): GoogleCalendar
    {
        return $this->calendar;
    }

    /**
     * Refresh model
     *
     * If lock is true, then the row is locked for use in a transaction.
     *
     * @param bool $lock
     */
    protected function refresh(bool $lock = false): void
    {
        if (!$lock) {
            $this->calendar->refresh();
            return;
        }
        /** @var GoogleCalendar $calendar */
        $calendar = GoogleCalendar::query()->whereKey($this->getID()->getBytes())->lockForUpdate()->first();
        $this->calendar = $calendar;
    }

    /**
     * Get service instance
     *
     * @return CalendarService
     */
    public function getService(): CalendarService
    {
        return $this->service;
    }

    /**
     * Call Google API
     *
     * If allow remove is true, then we check for resource not found issues and queue the removal of the calendar. This
     * can happen if user deletes their calendar without first removing it from our system.
     *
     * @param Closure $closure
     * @param bool $allow_remove
     * @return mixed
     * @throws ResourceNotFoundException
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\ApiException
     * @throws \App\Services\GoogleApi\Exceptions\OAuth\DisconnectedException
     * @throws \Core\Exceptions\AppException
     */
    public function callApi(Closure $closure, bool $allow_remove = false)
    {
        try {
            return $this->service->callApi($closure);
        } catch (ResourceNotFoundException $e) {
            if (!$allow_remove) {
                throw $e;
            }
            $this->queueRemove(true);
            throw (new RemovedException('Calendar was deleted by user'))->setLastException($e);
        }
    }

    /**
     * Get id
     *
     * @return UuidInterface
     */
    public function getID(): UuidInterface
    {
        return $this->calendar->getUuidKey();
    }

    /**
     * Get owner type
     *
     * @return int
     */
    public function getOwnerType(): int
    {
        return array_search($this->calendar->ownerType, static::$owner_type_map);
    }

    /**
     * Get owner id
     *
     * @return int
     */
    public function getOwnerID(): int
    {
        return $this->calendar->ownerID;
    }

    /**
     * Get google id for calendar
     *
     * @return string
     */
    public function getGoogleID(): string
    {
        return $this->calendar->calendarID;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->calendar->name;
    }

    /**
     * Get status
     *
     * @return int
     */
    public function getStatus(): int
    {
        return $this->calendar->status;
    }

    /**
     * Get number of days to pull before now
     *
     * @param int $default_days
     * @return int
     */
    public function getPullWindowDaysBefore(int $default_days = self::PULL_WINDOW_DAYS_BEFORE): int
    {
        return $this->calendar->pullWindowDaysBefore ?? $default_days;
    }

    /**
     * Get number of days to pull after now
     *
     * @param int $default_days
     * @return int
     */
    public function getPullWindowDaysAfter(int $default_days = self::PULL_WINDOW_DAYS_AFTER): int
    {
        return $this->calendar->pullWindowDaysAfter ?? $default_days;
    }

    /**
     * Get pull window start date
     *
     * @return Carbon|null
     */
    public function getPullWindowStartAt(): ?Carbon
    {
        return $this->calendar->pullWindowStartAt;
    }

    /**
     * Get pull window end date
     *
     * @return Carbon|null
     */
    public function getPullWindowEndAt(): ?Carbon
    {
        return $this->calendar->pullWindowEndAt;
    }

    /**
     * Determines if calendar is currently in pulling status
     *
     * @return bool
     */
    public function isPulling(): bool
    {
        return $this->calendar->pullStatus === GoogleCalendar::PULL_STATUS_PULLING;
    }

    /**
     * Get number of days to push before now
     *
     * @param int $default_days
     * @return int
     */
    public function getPushWindowDaysBefore(int $default_days = self::PUSH_WINDOW_DAYS_BEFORE): int
    {
        return $this->calendar->pushWindowDaysBefore ?? $default_days;
    }

    /**
     * Get number of days to push after now
     *
     * @param int $default_days
     * @return int
     */
    public function getPushWindowDaysAfter(int $default_days = self::PUSH_WINDOW_DAYS_AFTER): int
    {
        return $this->calendar->pushWindowDaysAfter ?? $default_days;
    }

    /**
     * Get push window start date
     *
     * @return Carbon|null
     */
    public function getPushWindowStartAt(): ?Carbon
    {
        return $this->calendar->pushWindowStartAt;
    }

    /**
     * Get push window end date
     *
     * @return Carbon|null
     */
    public function getPushWindowEndAt(): ?Carbon
    {
        return $this->calendar->pushWindowEndAt;
    }

    /**
     * Determines if calendar is currently in pushing status
     *
     * @return bool
     */
    public function isPushing(): bool
    {
        return $this->calendar->pushStatus === GoogleCalendar::PUSH_STATUS_PUSHING;
    }

    /**
     * Get timezone for owner of calendar
     *
     * @return string
     * @todo remove once all times are UTC
     */
    public function getTimezone(): string
    {
        $owner_type = $this->getOwnerType();
        $method = $owner_type === self::OWNER_TYPE_USER ? 'getTimezoneForUser' : 'getTimezoneForCompany';
        return TimeService::$method($this->getOwnerID());
    }

    /**
     * Get active notification channel if available
     *
     * @return NotificationChannel|null
     */
    public function getActiveNotificationChannel(): ?NotificationChannel
    {
        /** @var GoogleCalendarNotificationChannel $channel */
        $channel = $this->calendar->notificationChannels()
            ->where('status', GoogleCalendarNotificationChannel::STATUS_ACTIVE)
            ->first();
        return $channel !== null ? new NotificationChannel($this, $channel) : null;
    }

    /**
     * Queue notification channel create job
     *
     * Will start a new notification channel and stop any currently existing one. Only one channel can be active at a
     * time.
     */
    public function queueNotificationChannelCreate()
    {
        NotificationChannelStartJob::enqueue($this->getID());
    }

    /**
     * Create a notification channel
     *
     * If allowed, we will stop any active notification channel before starting a new one. Only one should be active
     * at a time for a calendar.
     *
     * @param bool $stop_active
     * @return NotificationChannel
     */
    public function createNotificationChannel(bool $stop_active = true): NotificationChannel
    {
        if ($stop_active && ($active_channel = $this->getActiveNotificationChannel()) !== null) {
            $active_channel->stop();
        }
        return NotificationChannel::create($this);
    }

    /**
     * Queue pull all job
     *
     * @param bool $full_sync
     */
    public function queuePullAll(bool $full_sync = false): void
    {
        EventPullAllJob::enqueue($this->getID(), $full_sync);
    }

    /**
     * Start pull by setting calendar pull status to pulling
     *
     * @param bool $is_retry
     * @throws SyncInProgressException
     */
    public function startPull(bool $is_retry = false): void
    {
        // obtain lock on calendar row to determine current sync status and to set status to syncing
        DB::transaction(function () use ($is_retry) {
            $this->refresh(true);
            if ($this->isPulling()) {
                if ($is_retry) {
                    return;
                }
                throw new SyncInProgressException('Event pull in progress');
            }
            $this->calendar->pullStatus = GoogleCalendar::PULL_STATUS_PULLING;
            $this->calendar->save();
        });
    }

    /**
     * Stop pull by setting calendar pull status to pulled
     *
     * @param bool $failed
     */
    public function stopPull(bool $failed = false): void
    {
        $this->calendar->fill([
            'pullStatus' => !$failed ? GoogleCalendar::PULL_STATUS_PULLED : GoogleCalendar::PULL_STATUS_FAILED,
            'lastPullAt' => Carbon::now('UTC')
        ])->save();
    }

    /**
     * Start push by setting calendar push status to pushing
     *
     * @param bool $is_retry
     * @throws SyncInProgressException
     */
    public function startPush(bool $is_retry = false): void
    {
        DB::transaction(function () use ($is_retry) {
            $this->refresh(true);
            if ($this->isPushing()) {
                if ($is_retry) {
                    return;
                }
                throw new SyncInProgressException('Event push in progress');
            }
            $this->calendar->pushStatus = GoogleCalendar::PUSH_STATUS_PUSHING;
            $this->calendar->save();
        });
    }

    /**
     * Stop push by setting calendar push status to pushed
     *
     * @param bool $failed
     */
    public function stopPush(bool $failed = false): void
    {
        $this->calendar->fill([
            'pushStatus' => !$failed ? GoogleCalendar::PUSH_STATUS_PUSHED : GoogleCalendar::PUSH_STATUS_FAILED,
            'lastPushAt' => Carbon::now('UTC')
        ])->save();
    }

    /**
     * Get events from Google for calendar
     *
     * Uses pagination to fetch full list. Returns sync token and total count for use in reporting. All events are
     * converted from the Google API library version to our wrapper.
     *
     * @param array $options
     * @return Generator|EventEntity[]
     * @throws ResourceNotFoundException
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\ApiException
     * @throws \App\Services\GoogleApi\Exceptions\OAuth\DisconnectedException
     * @throws \Core\Exceptions\AppException
     */
    public function getExternalEventCursor(array $options): Generator
    {
        $i = 0;
        do {
            $events = $this->callApi(fn(Google_Service_Calendar $service) => $service->events->listEvents($this->calendar->calendarID, $options), true);
            /** @var \Google_Service_Calendar_Event $event */
            foreach ($events->getItems() as $event) {
                yield EventEntity::fromGoogleEntity($event);
                $i++;
            }
            $options['pageToken'] = $events->getNextPageToken();
        } while($options['pageToken'] !== null);
        return [
            'total_count' => $i,
            'sync_token' => $events->getNextSyncToken()
        ];
    }

    /**
     * Get all events to pull for calendar
     *
     * If incremental is true, we will try to use the sync token (if defined) to only pull the changes since the last
     * time we checked. If false or no sync token is available, a full sync is performed by clearing all pulled
     * events from local storage and grabbing all events within the defined window to store again.
     *
     * Sync token is automatically stored with calendar for use with next call.
     *
     * @param bool $incremental
     * @param Carbon|null $expand_to
     * @return Generator
     * @throws CalendarException
     * @throws ResourceNotFoundException
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\ApiException
     * @throws \App\Services\GoogleApi\Exceptions\OAuth\DisconnectedException
     * @throws \Core\Exceptions\AppException
     */
    public function getPullEventCursor(bool $incremental = true, ?Carbon $expand_to = null): Generator
    {
        if ($expand_to !== null) {
            $incremental = false;
            if (($window_start_at = $this->getPullWindowEndAt()) === null) {
                throw new CalendarException('Cannot expand pull window which has not been setup yet');
            }
            $window_end_at = $expand_to;
            if ($window_start_at->gt($window_end_at)) {
                throw new CalendarException('Window start date must be after end date for expansion');
            }
        } else {
            $window_start_at = Carbon::now('UTC')->subDays($this->getPullWindowDaysBefore())->startOfDay();
            $window_end_at = Carbon::now('UTC')->addDays($this->getPullWindowDaysAfter())->endOfDay();
        }
        $options = [
            'singleEvents' => true,
            'maxResults' => 50
        ];
        if ($incremental && $this->calendar->pullSyncToken !== null) {
            // if a sync token is available, we pass it in so google will only return results that have changes since
            // our last sync. this makes this process much more efficient than looping through all items each time.
            $options['syncToken'] = $this->calendar->pullSyncToken;
        } else {
            $incremental = false;
            $options['timeMin'] = $window_start_at->format('c');
            $options['timeMax'] = $window_end_at->format('c');
            // if this is a fresh sync we set the start, otherwise it's left alone during an expansion
            if ($expand_to === null) {
                $this->calendar->pullWindowStartAt = $window_start_at;
            }
            $this->calendar->pullWindowEndAt = $window_end_at;
        }
        $i = 0;
        try {
            $events = $this->getExternalEventCursor($options);
            foreach ($events as $event) {
                $start_at = $event->getStart();
                if ($incremental && $start_at !== null && !$start_at->utc()->between($window_start_at, $window_end_at)) {
                    continue;
                }
                yield $event;
                $i++;
            }
        } catch (FullSyncRequiredException $e) {
            $this->clearPulledEvents();
            $events = $this->getPullEventCursor(false);
            yield from $events;
            return $events->getReturn();
        }
        $info = $events->getReturn();
        $info['window_start_at'] = $window_start_at;
        $info['window_end_at'] = $window_end_at;
        $info['sync_count'] = $i;
        $info['incremental'] = $incremental;
        $this->calendar->pullSyncToken = $info['sync_token'];
        $this->calendar->save();
        return $info;
    }

    /**
     * Get all events from set of google ids
     *
     * @param array $google_ids
     * @param bool $with_details
     * @return Event[]
     */
    public function getEventsByGoogleIDs(array $google_ids, bool $with_details = false): array
    {
        $query = GoogleCalendarEvent::query()
            ->where('googleCalendarID', $this->getID()->getBytes())
            ->whereIn('googleID', $google_ids);
        if ($with_details) {
            $query->with(['item']);
        }
        return $query->get()
            ->keyBy('googleID')
            ->map(fn(GoogleCalendarEvent $event) => new Event($this, $event))
            ->all();
    }

    /**
     * Get event by item type and id
     *
     * @param int $type
     * @param int $id
     * @return Event|null
     */
    public function getEventByItemTypeAndID(int $type, int $id): ?Event
    {
        return Event::getByItemTypeAndID($this, $type, $id);
    }

    /**
     * Pull event into local storage
     *
     * @param EventDetailInterface $event_detail
     * @param string $google_id
     * @param UuidInterface|null $hit_id
     * @return Event
     */
    public function pullEvent(EventDetailInterface $event_detail, string $google_id, ?UuidInterface $hit_id = null): Event
    {
        return Event::pull($this, $event_detail, $google_id, $hit_id);
    }

    /**
     * Push event to Google and store locally
     *
     * If update is allowed, we will check if an event exists first before creating. If found an update is ran.
     *
     * @param EventDetailInterface $event_detail
     * @param bool $allow_update
     * @return Event
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\EventDeletedException
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\EventDetailMismatchException
     */
    public function pushEvent(EventDetailInterface $event_detail, bool $allow_update = true): Event
    {
        if ($allow_update && (($event = $this->getEventByItemTypeAndID($event_detail->getType(), $event_detail->getItemID()))) !== null) {
            $event->update($event_detail);
            return $event;
        }
        return Event::push($this, $event_detail);
    }

    /**
     * Get all pushed events for calendar
     *
     * @return Generator|Event[]
     */
    public function getPushedEventCursor(): Generator
    {
        $events = GoogleCalendarEvent::query()
            ->where('googleCalendarID', $this->getID()->getBytes())
            ->cursor();
        foreach ($events as $event) {
            yield new Event($this, $event);
        }
    }

    /**
     * Determines if calendar accepts a certain event type
     *
     * @param int $event_type
     * @return bool
     */
    public function acceptsEventType(int $event_type): bool
    {
        $owner_type = $this->getOwnerType();
        return isset(static::$accepted_event_types[$owner_type]) && in_array($event_type, static::$accepted_event_types[$owner_type]);
    }

    /**
     * Cancel list of events
     *
     * First groups by action and then runs bulk queries to cancel each one.
     *
     * @param array $events
     * @throws GoogleApiException
     */
    public function cancelEvents(array $events): void
    {
        $actions = [];
        foreach ($events as $event) {
            if (!($event instanceof Event)) {
                throw new GoogleApiException('Event must be an instance of %s', Event::class);
            }
            $action = $event->getAction();
            $actions[$action] ??= [];
            $actions[$action][] = $event->getID()->getBytes();
        }
        foreach ($actions as $type => $event_ids) {
            switch ($type) {
                case Event::ACTION_PULLED:
                    // delete all matching pulled events for calendar
                    $event_model = self::$owner_type_pulled_event_models[$this->getOwnerType()];
                    $event_model::query()
                        ->withGoogleEvent($this->getID())
                        ->whereIn('googleCalendarEvents.googleCalendarEventID', $event_ids)
                        ->delete();
                    GoogleCalendarEvent::query()
                        ->where('googleCalendarID', $this->getID()->getBytes())
                        ->whereIn('googleCalendarEventID', $event_ids)
                        ->delete();
                    break;
                case Event::ACTION_PUSHED:
                    // mark all events as cancelled
                    $now = Carbon::now('UTC');
                    GoogleCalendarEvent::query()
                        ->where('googleCalendarID', $this->getID()->getBytes())
                        ->whereIn('googleCalendarEventID', $event_ids)
                        ->update([
                            'isCancelled' => true,
                            'cancelledAt' => $now
                        ]);
                    break;
            }
        }
    }

    /**
     * Delete events by either action
     *
     * @param int $action
     */
    protected function clearEventsByAction(int $action): void
    {
        GoogleCalendarEvent::query()
            ->where('googleCalendarID', $this->getID()->getBytes())
            ->where('action', $action)
            ->delete();
    }

    /**
     * Clear all pulled events
     *
     * @param bool $clear_token
     */
    public function clearPulledEvents(bool $clear_token = true): void
    {
        if ($clear_token) {
            $this->calendar->pullSyncToken = null;
            $this->calendar->lastPullAt = null;
            $this->calendar->save();
        }

        // clear out any stored event data
        $event_model = self::$owner_type_pulled_event_models[$this->getOwnerType()];
        $event_model::query()
            ->withGoogleEvent($this->getID())
            ->where('googleCalendarEvents.action', GoogleCalendarEvent::ACTION_PULLED)
            ->delete();
        $this->clearEventsByAction(GoogleCalendarEvent::ACTION_PULLED);
    }

    /**
     * Start remove of calendar by setting status to removing
     *
     * @throws RemovedException|RemoveInProgressException
     */
    public function startRemove(): void
    {
        DB::transaction(function () {
            $this->refresh(true);
            if ($this->calendar->status === GoogleCalendar::STATUS_REMOVING) {
                throw new RemoveInProgressException('Calendar is being removed already');
            }
            if (!in_array($this->calendar->status, [GoogleCalendar::STATUS_ADDED, GoogleCalendar::STATUS_REMOVE_FAILED])) {
                throw new RemovedException('Calendar is already removed');
            }
            $this->calendar->status = GoogleCalendar::STATUS_REMOVING;
            $this->calendar->save();
        });
    }

    /**
     * Queue removal of calendar
     *
     * If force is true, then calendar is removed without making any calls to Google.
     *
     * @param bool $force
     * @throws RemoveInProgressException
     * @throws RemovedException
     */
    public function queueRemove(bool $force = false): void
    {
        $this->startRemove();
        RemoveJob::enqueue($this->calendar->getUuidKey(), $force);
    }

    /**
     * Remove calendar
     *
     * Notification channel is stopped, local storage is cleared for pulled and pushed events, calendar data is
     * cleared from database.
     *
     * If force is true, the removal is done without making any calls to Google.
     *
     * @param bool $force
     * @param bool $bulk
     * @throws CalendarException
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\ApiException
     * @throws \App\Services\GoogleApi\Exceptions\OAuth\DisconnectedException
     * @throws \Core\Exceptions\AppException
     */
    public function remove(bool $force = false, bool $bulk = false): void
    {
        if ($this->calendar->status !== GoogleCalendar::STATUS_REMOVING) {
            throw new CalendarException('Calendar is not in removing status');
        }
        if (($active_channel = $this->getActiveNotificationChannel()) !== null) {
            $active_channel->stop($force);
        }

        $this->clearPulledEvents(false);

        // if doing a force removal, we just bulk delete all events instead of calling API for each one. this is used
        // when we no longer have access to the account so all calls would fail anyway (due to a manual disconnect, etc.)
        if ($force) {
            $this->clearEventsByAction(GoogleCalendarEvent::ACTION_PUSHED);
        } else {
            foreach ($this->getPushedEventCursor() as $event) {
                $event->delete();
            }
        }

        if (!$bulk) {
            $this->service->clearCalendarCache();
        }

        $this->calendar->fill([
            'status' => GoogleCalendar::STATUS_REMOVED,
            'pullWindowStartAt' => null,
            'pullWindowEndAt' => null,
            'pullStatus' => GoogleCalendar::PULL_STATUS_NEVER,
            'pullSyncToken' => null,
            'lastPullAt' => null,
            'pushWindowStartAt' => null,
            'pushWindowEndAt' => null,
            'pushStatus' => GoogleCalendar::PUSH_STATUS_NEVER,
            'lastPushAt' => null,
            'removedAt' => Carbon::now('UTC')
        ])->save();
    }

    /**
     * Update status of calendar for failed removal
     */
    public function failRemove(): void
    {
        $this->calendar->fill([
            'status' => GoogleCalendar::STATUS_REMOVE_FAILED
        ])->save();
    }

    /**
     * Convert calendar into basic array entity
     *
     * @return array
     */
    public function toEntity(): array
    {
        return [
            'id' => $this->getID()->toString(),
            'name' => $this->getName(),
            'status' => $this->getStatus()
        ];
    }
}
