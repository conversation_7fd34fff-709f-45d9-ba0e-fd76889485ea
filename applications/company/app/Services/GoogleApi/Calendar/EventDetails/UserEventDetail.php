<?php

declare(strict_types=1);

namespace App\Services\GoogleApi\Calendar\EventDetails;

use App\Services\GoogleApi\Calendar\Classes\Calendar\{Event, EventDetail};
use Carbon\Carbon;
use Common\Models\UserEvent;

/**
 * Class UserEventDetail
 *
 * @package App\Services\GoogleApi\Calendar\EventDetails
 */
class UserEventDetail extends EventDetail
{
    /**
     * @var int Event type
     */
    protected int $type = Event::TYPE_USER;

    /**
     * @var UserEvent Model instance
     */
    protected UserEvent $event;

    /**
     * @var string Timezone identifier
     * @todo remove when all event dates are converted to UTC
     */
    protected string $timezone;

    /**
     * UserEventDetail constructor
     *
     * @param UserEvent $event
     * @param string $timezone
     */
    public function __construct(UserEvent $event, string $timezone)
    {
        $this->event = $event;
        $this->timezone = $timezone;
    }

    /**
     * Get item id
     *
     * @return int
     */
    public function getItemID(): int
    {
        return $this->event->getKey();
    }

    /**
     * Get title
     *
     * @return string|null
     */
    public function getTitle(): ?string
    {
        return $this->event->scheduleType;
    }

    /**
     * Get location
     *
     * @return string|null
     */
    public function getLocation(): ?string
    {
        return null;
    }

    /**
     * Get description
     *
     * @return string|null
     */
    public function getDescription(): ?string
    {
        return null;
    }

    /**
     * Determines if event is all day
     *
     * @return bool
     */
    public function isAllDay(): bool
    {
        return $this->getStart()->isStartOfDay() && $this->getEnd()->isStartOfDay();
    }

    /**
     * Get start
     *
     * @return Carbon
     */
    public function getStart(): Carbon
    {
        return Carbon::parse($this->event->scheduledStart, $this->timezone);
    }

    /**
     * Get end
     *
     * @return Carbon
     */
    public function getEnd(): Carbon
    {
        return Carbon::parse($this->event->scheduledEnd, $this->timezone);
    }

    /**
     * Get underlying model
     *
     * @return UserEvent
     */
    public function getModel(): UserEvent
    {
        return $this->event;
    }
}
