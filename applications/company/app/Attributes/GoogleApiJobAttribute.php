<?php

declare(strict_types=1);

namespace App\Attributes;

use Attribute;

/**
 * Class GoogleApiJobAttribute
 *
 * @package App\Attributes
 */
#[Attribute(Attribute::TARGET_CLASS)]
class GoogleApiJobAttribute extends JobAttribute
{
    /**
     * Get channel
     *
     * Set default to google-api if not already defined.
     *
     * @return string|null
     */
    public function getChannel(): ?string
    {
        return $this->channel ?? 'google-api';
    }

    /**
     * Get max tries
     *
     * Set default to 5 if not already defined.
     *
     * @return int|null
     */
    public function getMaxTries(): ?int
    {
        return $this->max_tries ?? 5;
    }
}
