<?php

declare(strict_types=1);

namespace App\ResourceDelegates\Bid\Item\InstallmentPaymentTerm;

use App\Resources\Bid\Item\InstallmentPaymentTerm\InstallmentResource;
use App\Resources\Bid\Item\InstallmentPaymentTermResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\UuidInterface;

class InstallmentDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list): RelationList
    {
        $list->oneOrMany('installment_payment_term')->modelRelation('installmentPaymentTerm')
            ->resource(InstallmentPaymentTermResource::class);

        return $list;
    }

    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id', true)
            ->typeUuid()
            ->column('bidItemInstallmentPaymentTermInstallmentID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([InstallmentResource::ACTION_CREATE, InstallmentResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('installment_payment_term_id')
            ->typeUuid()
            ->column('bidItemInstallmentPaymentTermID', true)
            ->validation('Installment Payment Term Id', 'required|uuid|check_installment_payment_term_id')
            ->enableAction(InstallmentResource::ACTION_FILTER)
            ->onAction(InstallmentResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('name')
            ->validation('Name', 'trim|required|max_length[100]');

        $list->field('amount_type')
            ->column('amountType')
            ->validation('Amount Type', 'required|type[int]|in_array[amount_types]');

        $list->field('amount')
            ->validation('Amount', 'trim|required|type[string]|numeric|greater_than[0][4]');

        $list->field('due_time_frame')
            ->column('dueTimeFrame')
            ->validation('Due Time Frame', 'required|type[int]|in_array[due_time_frames]');

        $list->field('order')
            ->validation('Order', 'required|type[int]');

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, InstallmentResource $resource): Rules
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_installment_payment_term_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('installment_payment_term')->entityExists($id->toString())) {
                return true;
            }
            return 'check_installment_payment_term_id';
        }, [
            'check_installment_payment_term_id' => 'Unable to find installment payment term'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config): FieldConfig
    {
        $config->store('amount_types', InstallmentResource::getAmountTypes());
        $config->store('due_time_frames', InstallmentResource::getDueTimeFrames());
        return $config;
    }

    protected function validatePercentages(array $entities): void
    {
        $bc_scale = 4;

        $percentages = array_filter($entities, function (Entity $entity) {
            return $entity->get('amount_type') === InstallmentResource::AMOUNT_TYPE_PERCENTAGE;
        });
        if (count($percentages) === 0) {
            throw new ValidationException('At least one percentage based installment is required');
        }
        $percentage = array_reduce($percentages, function ($carry, Entity $entity) use ($bc_scale) {
            return bcadd($carry, $entity->get('amount'), $bc_scale);
        }, '0');
        if (bccomp($percentage, '1', $bc_scale) !== 0) {
            throw new ValidationException('Sum of installment percentages should equal 1');
        }
    }

    public function batchNestedCreateValidateAfter(array $entities): void
    {
        $this->validatePercentages($entities);
    }

    public function batchNestedUpdateValidateAfter(array $entities): void
    {
        $this->validatePercentages($entities);
    }

    public function queryScopeGlobal(object $query, InstallmentResource $resource): object
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function scopeBuildBefore(Scope $scope): void
    {
        switch ($scope->getFormat()) {
            case 'bid-v1':
            case 'detail-v1':
                $scope->fields([
                    'id', 'name', 'amount_type', 'amount', 'due_time_frame', 'order'
                ], true);
                $scope->query(fn($query) => $query->ordered());
                break;
        }
    }
}
