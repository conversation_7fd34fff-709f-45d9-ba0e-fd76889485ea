<?php

declare(strict_types=1);

namespace App\ResourceDelegates;

use App\Resources\AppNotificationResource;
use App\Resources\Bid\ItemResource;
use App\Resources\CustomerResource;
use App\Resources\LeadResource;
use App\Resources\ProjectResource;
use App\Resources\PropertyResource;
use App\Resources\TaskResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\AppNotification;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Requests\CreateRequest;
use App\Resources\AppNotificationDistributionResource;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Relations\PolyRelation;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;

class AppNotificationDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('distributions')
            ->modelRelation('distributions')
            ->resource(AppNotificationDistributionResource::class);

        $list->polymorphic('association')
            ->typeField('association_type')->idField('association_id')
            ->types(function (PolyRelation $relation) {
                $relation->type(AppNotificationResource::ASSOCIATION_TYPE_CUSTOMER)->resource(CustomerResource::class);
                $relation->type(AppNotificationResource::ASSOCIATION_TYPE_PROPERTY)->resource(PropertyResource::class);
                $relation->type(AppNotificationResource::ASSOCIATION_TYPE_PROJECT)->resource(ProjectResource::class);
                $relation->type(AppNotificationResource::ASSOCIATION_TYPE_LEAD)->resource(LeadResource::class);
                $relation->type(AppNotificationResource::ASSOCIATION_TYPE_TASK)->resource(TaskResource::class);
                $relation->type(AppNotificationResource::ASSOCIATION_TYPE_BID)->resource(ItemResource::class);
            });

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->typeUuid()
            ->column('appNotificationID')
            ->validation('App Notification Id', 'required|uuid')
            ->label('Id');

        $list->field('title')
            ->label('Title')
            ->requireColumn()
            ->validationRules('required|trim|type[string]|max_length[255]');

        $list->field('summary')
            ->validationRules('nullable|optional|type[string]|max_length[1000]');

        $list->field('content')
            ->validationRules('nullable|optional|type[string]');

        $list->field('link')
            ->validationRules('nullable|optional|type[string]|max_length[255]');

        $type_map = AppNotificationResource::getAssociationTypeMap();
        $list->field('association_type')
            ->column('associationType')
            ->validation('Association Type', 'nullable|optional|type[int]|in_array[association_types]')
            ->saveMutator(function ($value) use ($type_map) {
                if($value === null) {
                    return null;
                }
                return array_search($value, $type_map);
            })
            ->outputMutator(function ($value) use ($type_map) {
                if($value === null) {
                    return null;
                }
                return $type_map[$value];
            })
            ->onAction(AppNotificationResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $association_type_names = AppNotificationResource::getAssociationTypeNames();
        $list->field('association_type_name')
            ->onDemand()
            ->label('Association Type')
            ->value(function (AppNotification $notification) use ($association_type_names) {
                if ($notification->associationType === null) {
                    return null;
                }
                return $association_type_names[$notification->associationType];
            });

        $list->field('association_id')
            ->column('associationID')
            ->validation('Association Id', 'nullable|optional|type[int]')
            ->onAction(AppNotificationResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('nullable|optional|numeric');
            });

        $list->field('association_uuid')
            ->column('associationUUID')
            ->typeUuid()
            ->validation('Association UUID', 'nullable|optional');

        $list->polyField('association')->typeField('association_type')->keyField('association_uuid');

        $list->field('metadata')
            ->validationRules('nullable|optional|json|max_length[1024]')
            ->label('Metadata');

        $list->field('placement')
            ->validationRules('required|type[int]|in_array[placements]');

        $list->field('type')
            ->validationRules('required|type[int]|in_array[types]');

        $list->field('status')
            ->validationRules('required|type[int]|in_array[statuses]|check_status');

        $list->field('automationType')
            ->validationRules('optional|bool');

        $list->field('primary_button_text')
            ->column('primaryButtonText')
            ->validationRules('nullable|optional|type[string]|max_length[30]');

        $list->field('secondary_button_text')
            ->column('secondaryButtonText')
            ->validationRules('nullable|optional|type[string]|max_length[30]');

        $list->field('is_completion_required')
            ->label('Is Completion Required')
            ->column('isCompletionRequired')
            ->validationRules('nullable|optional|bool');

        $this->timestampFields($list, true, false, true);

        $list->modify([
            'title', 'summary', 'content', 'link', 'type', 'status', 'association_type'
        ], function (Field $field) {
            return $field->enableAction(AppNotificationResource::ACTION_SORT);
        });

        return $list;
    }

    public function validationRules(Rules $rules, AppNotificationResource $resource)
    {
        $rules->register('check_status', function ($status, $params, Validator $validator) {
            $model = $validator->getConfig()->storage('_model');
            $prev_status = $model !== null ? $model->status : null;
            if ($prev_status !== null && $prev_status === $status) {
                return true;
            }

            $allowed_statuses_config = [AppNotificationResource::STATUS_ACTIVE, AppNotificationResource::STATUS_INACTIVE];
            if (in_array($status, $allowed_statuses_config, true)) {
                return true;
            }

            return ['status_invalid_transition', [
                'statuses' => implode(', ', $status)
            ]];
        }, [
            'status_invalid' => 'Status is not a valid value',
            'status_invalid_transition' => 'Only can transition to the following statuses: {statuses}',
            'status_final' => 'Status can no longer be changed'
        ]);

        $rules->register('check_association_id', function ($id, $params, Validator $validator) use ($resource) {
            if ($validator->errors()->has('association_type')) {
                return Rules::STOP;
            }
            $association = $validator->data('association_type');
            $association_resource = $resource->polyRelationResource('association', $association);

            if (!$association_resource->entityExists($id)) {
                return 'check_association_id';
            }
            return true;
        }, [
            'check_association_id' => 'Unable to find associated item'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('statuses', AppNotificationResource::getStatuses());
        $config->store('placements', AppNotificationResource::getPlacements());
        $config->store('types', AppNotificationResource::getTypes());
        $config->store('association_types', AppNotificationResource::getAssociationTypes());
        $config->store('association_type_not_null', function ($item_id, Validator $validator) {
            return $validator->data('association_type') !== null;
        });

        return $config;
    }

    public function anyCreateModelDataAfter($model_data, CreateRequest $request)
    {
        $model = $request->getModel();
        $model_data['isCompletionRequired'] = false;

        if ($model->isCompletionRequired) {
            $model_data['isCompletionRequired'] = $model->isCompletionRequired;
        }

        return $model_data;
    }


    public function queryScopeSearch($query, $term)
    {
        return $query->search($term);
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'collection-v1':
                $scope->fields([
                    'id', 'title', 'summary', 'content', 'link', 'placement', 'type', 'status',
                    'metadata', 'association_type', 'association_id', 'association_uuid',
                    'primary_button_text', 'secondary_button_text', 'is_completion_required', 'createdAt', 'updatedAt'
                ]);
                $scope->with(['association']);
                break;
            case 'list-v1':
                $scope->fields([
                    'id', 'title', 'summary', 'content', 'link', 'placement', 'type', 'status',
                    'primary_button_text', 'secondary_button_text', 'createdAt', 'updatedAt'
                ], true);
                $scope->with(['association']);
                break;
        }
    }

}
