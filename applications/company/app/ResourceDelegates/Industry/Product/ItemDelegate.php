<?php

namespace App\ResourceDelegates\Industry\Product;

use App\Resources\Industry\Product\ItemResource;
use App\Resources\IndustryResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\{Field, FieldList, RelationList};

class ItemDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('industry')->resource(IndustryResource::class);
        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->column('intakeIndustryProductItemID')
            ->typeUuid()
            ->onAction(ItemResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|uuid');
            })
            ->noSave();

        $list->field('intake_industry_id')
            ->column('intakeIndustryID', true)
            ->validation('Intake Industry Id', 'required|type[int]')
            ->onAction(ItemResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

         $list->field('name')
             ->validation('Name', 'trim|required|max_length[100]');

        $list->field('description')
            ->validation('Description', 'trim|nullable|optional|max_length[1000]');

        $list->field('price')
            ->validation('Price', 'required|type[string]|numeric');

        $list->field('unit')
            ->validation('Name', 'trim|required|max_length[100]');

        $list->field('product_category_name')
            ->column('productCategoryName', true)
            ->validation('Product Category Name', 'trim|required|max_length[200]');

        $list->field('system_form_item_id')
            ->typeUuid()
            ->column('systemFormItemID', true)
            ->validation('System Form Item Id', 'required|uuid');

        $list->field('form_name')
            ->column('formName')
            ->validation('Form Name', 'trim|required|max_length[100]');

        $list->field('form_content')
            ->column('formContent')
            ->validation('Form Content', 'trim|nullable|optional|max_length[1000]');

        $this->timestampFields($list, false);

        $list->modify(['name', 'price', 'unit'], function (Field $field) {
            return $field->enableAction(ItemResource::ACTION_SORT);
        });

        return $list;
    }

    public function actionAllowed($action, ItemResource $resource)
    {
        if (($action & ItemResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        return $resource->acl()->user() === null;
    }
}
