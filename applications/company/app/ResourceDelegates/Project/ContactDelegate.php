<?php

namespace App\ResourceDelegates\Project;

use App\Classes\Func;
use App\Resources\Project\ContactResource;
use App\Resources\Project\EventResource;
use App\Resources\ProjectResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\Uuid;

class ContactDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('project')->resource(ProjectResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->column('projectEmailID')
            ->noSave();

        $list->field('project_id')
            ->column('projectID', true)
            ->validation('Project Id', 'required|type[int]|check_project_id')
            ->onAction(ContactResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|type[int]');
            })
            ->onAction(ContactResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('name')
            ->validation('name', 'required|max_length[70]');

        $list->field('phone_number')
            ->column('phoneNumber')
            ->validation('Phone Number', 'nullable|optional|us_phone|us_phone_format');

        $list->field('email')
            ->validation('Email', 'nullable|optional|max_length[100]|email');

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, ContactResource $resource)
    {
        $rules->register('check_project_id', function ($id) use ($resource) {
            if ($resource->relationResource('project')->entityExists($id)) {
                return true;
            }
            return 'check_project_id';
        }, [
            'check_project_id' => 'Unable to find project'
        ]);

        return $rules;
    }

    public function validateAfter(Entity $entity)
    {
        if ($entity->get('phone_number') === null && $entity->get('email') === null) {
            throw new ValidationException('Phone number or email is required');
        }

        return $entity;
    }

    public function anyCreateModelDataAfter($model_data)
    {
        $model_data['projectEmailUUID'] = Uuid::uuid4()->getBytes();

        return $model_data;
    }

    public function queryScopeGlobal($query, ContactResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function queryScopeSearch($query, $term)
    {
        return $query->searchWithRank($term);
    }

    /**
     * Determines what actions are allowed based on the ACL of the passed resource
     *
     * Any collection and entity requests are allowed, but all other actions are only allowed based on the user's roles
     *
     * @param int $action
     * @param ContactResource $resource
     * @return bool
     */
    public function actionAllowed($action, ContactResource $resource)
    {
        if (($action & ContactResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary || $user->projectManagement || $user->sales) {
            return true;
        }
        return false;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        if ($scope->getFormat() === 'drawing-app-v1') {
            $scope->filter('type', 'eq', EventResource::TYPE_EVALUATION);
            $scope->fields(['id', 'start', 'end', 'user_id', 'cancelled_at'], true);
            $scope->with(['project']);
            $scope->disablePagination();
        }
    }
}
