<?php

namespace App\ResourceDelegates;

use App\Resources\BidContentProductItemResource;
use App\Resources\Bid\ContentResource;
use App\Resources\Product\ItemResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Request;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\UuidInterface;

class BidContentProductItemDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('bid_content')->modelRelation('bidContent')->resource(ContentResource::class);
        $list->oneOrMany('product_item')->modelRelation('productItem')->resource(ItemResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->typeUuid()
            ->column('bidContentProductItemID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([BidContentProductItemResource::ACTION_CREATE, BidContentProductItemResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('bid_content_id')
            ->typeUuid()
            ->column('bidContentID', true)
            ->validation('Bid Content Id', 'required|uuid|check_bid_content_id')
            ->onAction(BidContentProductItemResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('product_item_id')
            ->typeUuid()
            ->column('productItemID', true)
            ->validation('Product Item Id', 'required|uuid|check_product_item_id')
            ->onAction(BidContentProductItemResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, BidContentProductItemResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_bid_content_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('bid_content')->entityExists($id->toString())) {
                return true;
            }
            return 'check_bid_content_id';
        }, [
            'check_bid_content_id' => 'Unable to find bid content'
        ]);

        $rules->register('check_product_item_id', function (UuidInterface $id) use ($resource) {
            if ($resource->relationResource('product_item')->entityExists($id->toString())) {
                return true;
            }
            return 'check_product_item_id';
        }, [
            'check_item_id' => 'Unable to find product item'
        ]);

        return $rules;
    }

    protected function checkEntityExists(Entity $entity, Request $request)
    {
        $resource = $request->resource();
        $table_alias = $resource->getTableAlias();
        $count = $resource->newScopedQuery()
            ->where("{$table_alias}.bidContentID", $entity->get('bid_content_id')->getBytes())
            ->where("{$table_alias}.productItemID", $entity->get('product_item_id')->getBytes())
            ->count();
        if ($count !== 0) {
            throw new ValidationException('Entity with this bid content and product item combination already exists');
        }
    }

    public function anyCreateValidateAfter(Entity $entity, Request $request)
    {
        $this->checkEntityExists($entity, $request);
        return $entity;
    }

    public function anyUpdateValidateAfter(Entity $entity, Request $request)
    {
        $this->checkEntityExists($entity, $request);
        return $entity;
    }

    public function queryScopeGlobal($query, BidContentProductItemResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function actionAllowed($action, BidContentProductItemResource $resource)
    {
        if (in_array($action, [BidContentProductItemResource::ACTION_GET_COLLECTION, BidContentProductItemResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }
}
