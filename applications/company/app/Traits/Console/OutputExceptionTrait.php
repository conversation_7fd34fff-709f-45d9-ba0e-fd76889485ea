<?php

declare(strict_types=1);

namespace App\Traits\Console;

use Core\Exceptions\AppException;
use Throwable;

/**
 * Trait OutputExceptionTrait
 *
 * Cleanly output exceptions in a console environment.
 *
 * @package App\Traits\Console
 */
trait OutputExceptionTrait
{
    /**
     * Format exception data into readable format
     *
     * If validation exception from resource component is encountered, special handling is applied to reveal the nested
     * errors.
     *
     * @param Throwable $e
     * @param int $level
     */
    protected function formatException(Throwable $e, int $level = 1): void
    {
        $this->console->error(str_repeat('  ', $level) . $e->getMessage());
        if ($e instanceof AppException && method_exists($e, 'getErrors')) {
            $errors = $e->getErrors();
            if (count($errors) > 0) {
                $indent = str_repeat('  ', $level + 1);
                $this->console->error('%sErrors:', $indent);
                $indent .= '  ';
                foreach ($errors as $key => $error) {
                    if (!is_array($error)) {
                        $error = [$error];
                    }
                    foreach ($error as $msg) {
                        $this->console->error('%s%s [%s]', $indent, $msg, $key);
                    }
                }
            }
        }
    }

    /**
     * Output exception to console
     *
     * Handles all nested exceptions.
     *
     * @param Throwable $e
     */
    protected function outputException(Throwable $e): void
    {
        $level = 1;
        do {
            $this->formatException($e, $level);
            if (!($e instanceof AppException)) {
                break;
            }
            $level++;
        } while (($e = $e->getLastException()) !== null);
    }
}
