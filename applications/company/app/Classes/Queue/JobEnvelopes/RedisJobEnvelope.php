<?php

declare(strict_types=1);

namespace App\Classes\Queue\JobEnvelopes;

use Carbon\Carbon;
use Core\Components\Queue\Classes\JobEnvelope;

/**
 * Class RedisJobEnvelope
 *
 * @package App\Classes\Queue\JobEnvelopes
 */
class RedisJobEnvelope extends JobEnvelope
{
    /**
     * @var bool Determines if job has been saved to the database
     */
    protected bool $persisted = false;

    /**
     * @var Carbon|null Timestamp of when this job was queued up
     */
    protected ?Carbon $reserved_at = null;

    /**
     * Handle object clone
     *
     * Resets persisted and reserved_at fields.
     */
    public function __clone(): void
    {
        parent::__clone();

        $this->persisted = false;
        $this->reserved_at = null;
    }

    /**
     * Set if job is persisted to database
     *
     * @param bool $persisted
     */
    public function setIsPersisted(bool $persisted): void
    {
        $this->persisted = $persisted;
    }

    /**
     * Determines if job is persisted to database
     *
     * @return bool
     */
    public function isPersisted(): bool
    {
        return $this->persisted;
    }

    /**
     * Set reserved at timestamp
     *
     * @param Carbon|null $reserved_at
     */
    public function setReservedAt(?Carbon $reserved_at): void
    {
        $this->reserved_at = $reserved_at;
    }

    /**
     * Get reserved at timestamp
     *
     * @return Carbon|null
     */
    public function getReservedAt(): ?Carbon
    {
        return $this->reserved_at;
    }
}
