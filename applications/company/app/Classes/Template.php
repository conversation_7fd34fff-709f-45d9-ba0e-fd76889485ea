<?php

namespace App\Classes;

use Core\Exceptions\AppException;
use Core\StaticAccessors\Path;
use Core\Traits\IGSRTrait;

/**
 * Class Template
 *
 * Very basic templating system
 *
 * @package App\Classes
 */
class Template
{
    use IGSRTrait;

    /**
     * Actions
     */
    const ACTION_OVERWRITE = 1;
    const ACTION_PREPEND = 2;
    const ACTION_APPEND = 3;
    const ACTION_MERGE = 4;

    /**
     * Template path
     *
     * @var null|string
     */
    private $path = null;

    /**
     * Template constructor
     *
     * @param string|null $path
     */
    public function __construct($path = null)
    {
        if ($path !== null) {
            $this->setPath($path);
        }
    }

    /**
     * Get template as string via string type casting
     *
     * @return string
     */
    public function __toString()
    {
        return $this->render();
    }

    /**
     * Set data with specific action
     *
     * @param $key
     * @param $value
     * @param int $action
     * @return $this
     *
     * @throws AppException
     */
    public function set($key, $value, $action = self::ACTION_OVERWRITE)
    {
        if ($action !== self::ACTION_OVERWRITE && !isset($this->data[$key])) {
            $action = self::ACTION_OVERWRITE;
        }
        switch ($action) {
            case self::ACTION_OVERWRITE:
                $this->data[$key] = $value;
                break;
            case self::ACTION_PREPEND:
                $this->data[$key] = $value . $this->data[$key];
                break;
            case self::ACTION_APPEND:
                $this->data[$key] .= $value;
                break;
            default:
                throw new AppException('Invalid action');
                break;
        }
        return $this;
    }

    /**
     * Add variables in bulk
     *
     * @param $vars
     * @param int $action
     * @return $this
     * @throws AppException
     */
    public function setVars($vars, $action = self::ACTION_OVERWRITE)
    {
        switch ($action) {
            case self::ACTION_OVERWRITE:
                $this->data = $vars;
                break;
            case self::ACTION_MERGE:
                $this->data = array_merge($this->data, $vars);
                break;
            default:
                throw new AppException('Invalid action');
                break;
        }
        return $this;
    }

    /**
     * Set template path
     *
     * @param $path
     * @return $this
     */
    public function setPath($path)
    {
        $this->path = str_replace('.', '/', $path) . '.php';
        return $this;
    }

    /**
     * Export variables, include template, capture output, and return result
     *
     * @return bool|string
     *
     * @throws AppException
     */
    public function render()
    {
        if (is_null($this->path)) {
            throw new AppException('Template path is required');
        }
        $path = Path::view($this->path);
        if (!file_exists($path)) {
            throw new AppException("Template '%s' does not exist", $path);
        }
        $vars = self::compileVars($this->data);
        extract($vars);
        ob_start();
        include $path;
        $data = ob_get_clean();
        return $data;
    }

    /**
     * Flatten vars array by rendering any nested Template instances
     *
     * @param array $vars
     * @return array
     */
    protected static function compileVars(array $vars)
    {
        $_vars = [];
        foreach ($vars as $key => $value) {
            if (is_array($value)) {
                $value = self::compileVars($value);
            } elseif (is_object($value) && $value instanceof self) {
                $value = $value->render();
            }
            $_vars[$key] = $value;
        }
        return $_vars;
    }

    /**
     * Fetch template with variable array
     *
     * @param string $path
     * @param array $vars
     * @return mixed
     */
    public static function fetch($path, array $vars = [])
    {
        return (new self)->setPath($path)->setVars($vars)->render();
    }

    /**
     * Replace {vars} in subject
     *
     * @param string|null $subject
     * @param array $vars
     * @param string $prefix
     * @param string $suffix
     * @return string
     */
    public static function replace(?string $subject, array $vars, string $prefix = '{', string $suffix = '}'): string
    {
        if ($subject === null) {
            return '';
        }
        $keys = array_map(fn(string $key): string => $prefix . $key . $suffix, array_keys($vars));
        return str_replace($keys, array_values($vars), $subject);
    }

    /**
     * Fetch template and replace values in template string
     *
     * @param $path
     * @param array $vars
     * @return mixed
     */
    public static function fetchReplace($path, array $vars = [])
    {
        $content = (new self)->setPath($path)->render();
        return self::replace($content, self::compileVars($vars));
    }
}
