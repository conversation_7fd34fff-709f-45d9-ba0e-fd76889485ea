<?php

namespace App\Classes\FX\API;

use PDO;

class GetAllCompass extends Base
{
    private $results;

    public function getAllCompass()
    {
        $st = $this->db->prepare("SELECT HEX(drawingCompass.drawingID) AS drawingID, drawingCompass.nodeID, drawingCompass.xPos, drawingCompass.yPos, drawingCompass.zRotation, drawingCompass.createdAt, drawingCompass.updatedAt FROM drawingCompass
                                  JOIN appDrawing ON appDrawing.drawingID = drawingCompass.drawingID
                                  JOIN evaluation ON evaluation.evaluationID = appDrawing.evaluationID
                                  JOIN project ON project.projectID = evaluation.projectID
                                  JOIN customer ON customer.customerID = project.customerID
                                  JOIN user ON customer.companyID = user.companyID
                                  JOIN companies ON user.companyID = companies.companyID
                                  WHERE user.token = :token AND companies.isActive = '1' AND user.userActive = '1'
                                  ");
        $st->bindParam(":token", $this->token);
        $st->execute();

        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $returnObject[] = $row;
            }
            $this->results = array('message' => 'success', 'compass' => $returnObject);
        } else {
            $this->results = array('message' => 'No compass found');
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
