<?php

namespace App\Classes\FX\API;

use PDO;

class GetAllInteriorPiers extends Base
{
    private $results;

    public function getAllInteriorPiers()
    {
        $st = $this->db->prepare("SELECT HEX(drawingInteriorPiers.drawingID) AS drawingID, drawingInteriorPiers.nodeID, drawingInteriorPiers.pierNumber, drawingInteriorPiers.xPos, drawingInteriorPiers.yPos, drawingInteriorPiers.createdAt, drawingInteriorPiers.updatedAt FROM drawingInteriorPiers
                                  JOIN appDrawing ON appDrawing.drawingID = drawingInteriorPiers.drawingID
                                  JOIN evaluation ON evaluation.evaluationID = appDrawing.evaluationID
                                  JOIN project ON project.projectID = evaluation.projectID
                                  JOIN customer ON customer.customerID = project.customerID
                                  JOIN user ON customer.companyID = user.companyID
                                  JOIN companies ON user.companyID = companies.companyID
                                  WHERE user.token = :token AND companies.isActive = '1' AND user.userActive = '1' ");
        $st->bindParam(":token", $this->token);
        $st->execute();

        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $returnObject[] = $row;
            }
            $this->results = array('message' => 'success', 'interiorPiers' => $returnObject);
        } else {
            $this->results = array('message' => 'No interiorPiers found');
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
