<?php

namespace App\Classes\FX\API;

use PDO;

class GetAllDeadMans extends Base
{
    private $results;

    public function getAllDeadMans()
    {
        $st = $this->db->prepare("SELECT HEX(drawingDeadMans.drawingID) AS drawingID, drawingDeadMans.nodeID, drawingDeadMans.xPos, drawingDeadMans.yPos, drawingDeadMans.zRotation, drawingDeadMans.createdAt, drawingDeadMans.updatedAt FROM drawingDeadMans
                                  JOIN appDrawing ON appDrawing.drawingID = drawingDeadMans.drawingID
                                  JOIN evaluation ON evaluation.evaluationID = appDrawing.evaluationID
                                  JOIN project ON project.projectID = evaluation.projectID
                                  JOIN customer ON customer.customerID = project.customerID
                                  JOIN user ON customer.companyID = user.companyID
                                  JOIN companies ON user.companyID = companies.companyID
                                  WHERE user.token = :token AND companies.isActive = '1' AND user.userActive = '1' ");
        $st->bindParam(":token", $this->token);
        $st->execute();

        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $returnObject[] = $row;
            }
            $this->results = array('message' => 'success', 'deadMans' => $returnObject);
        } else {
            $this->results = array('message' => 'No deadMans found');
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
