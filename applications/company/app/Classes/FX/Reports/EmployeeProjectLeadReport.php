<?php

namespace App\Classes\FX\Reports;

use App\Classes\FX\Report;
use App\Traits\FX\Report\StartEndDateTrait;

/**
 * Class EmployeeProjectLeadReport
 *
 * Note: this was moved from old SalesReport class and modified slightly
 *
 * @package App\Classes\FX\Reports
 */
class EmployeeProjectLeadReport extends Report
{
    use StartEndDateTrait;

    /**
     * Run report
     *
     * @return array
     * @throws \Exception
     */
    public function run()
    {
        $sql = <<<SQL
SELECT
  leadsResult.projectSalesperson_1,
  leadsResult.employee,
  IFNULL(leads, 0)                         AS leads,
  IFNULL(bids, 0)                          AS bids,
  IFNULL(sales, 0)                         AS sales,
  IFNULL(grossSales, 0)                    AS grossSales,
  IFNULL(grossBids, 0)                     AS grossBids,
  IFNULL(totalMarketingCosts, 0)           AS totalMarketingCosts,
  IFNULL((sales / leads) * 100, 0)         AS closingPercentage,
  IFNULL((totalMarketingCosts / leads), 0) AS costPerLead,
  IFNULL((grossBids / bids), 0)            AS averageProjectBid,
  IFNULL((grossSales / sales), 0)          AS averageProjectSold

FROM

  (SELECT
     COUNT(*) AS leads,
     IFNULL(project.projectSalesperson, 0) AS projectSalesperson_1,
     IF(project.projectSalesperson IS NULL, '<Unspecified Salesperson>', CONCAT(user.userFirstName, ' ', user.userLastName)) AS employee
   FROM project
     JOIN customer ON project.customerID = customer.customerID
     LEFT JOIN user ON user.userID = project.projectSalesperson
   WHERE customer.companyID = :companyID
         AND project.createdAt >= :startDate
         AND project.createdAt <= :endDate
         AND project.projectCancelled IS NULL
         AND project.deletedAt IS NULL
   GROUP BY projectSalesperson_1

  ) AS leadsResult

  LEFT OUTER JOIN

  (SELECT SUM(eval.total) AS bids, IFNULL(eval.projectSalesperson, 0) AS projectSalesperson
   FROM(
         (SELECT IFNULL(p.projectSalesperson, 0) AS projectSalesperson, COUNT(bidTotal) AS total
          FROM evaluationBid AS b
            JOIN evaluation AS e ON e.evaluationID = b.evaluationID
            JOIN project AS p ON p.projectID = e.projectID
            JOIN property AS t ON t.propertyID = p.propertyID
            JOIN customer AS c ON c.customerID = t.customerID
            LEFT JOIN user AS u ON u.userID = p.projectSalesperson
          WHERE c.companyID = :companyID
                AND e.evaluationCancelled IS NULL
                AND e.deletedAt IS NULL
                AND p.projectCancelled IS NULL
                AND p.deletedAt IS NULL
                AND b.bidFirstSent IS NOT NULL
                AND b.bidFirstSent >= :startDate
                AND b.bidFirstSent <= :endDate
          GROUP BY projectSalesperson
         )
         UNION ALL
         (SELECT IFNULL(p.projectSalesperson, 0) AS projectSalesperson, COUNT(bidTotal) AS total
          FROM customBid AS b
            JOIN evaluation AS e ON e.evaluationID = b.evaluationID
            JOIN project AS p ON p.projectID = e.projectID
            JOIN property AS t ON t.propertyID = p.propertyID
            JOIN customer AS c ON c.customerID = t.customerID
            LEFT JOIN user AS u ON u.userID = p.projectSalesperson
          WHERE c.companyID = :companyID
                AND e.evaluationCancelled IS NULL
                AND e.deletedAt IS NULL
                AND p.projectCancelled IS NULL
                AND p.deletedAt IS NULL
                AND b.bidFirstSent IS NOT NULL
                AND b.bidFirstSent >= :startDate
                AND b.bidFirstSent <= :endDate
          GROUP BY projectSalesperson
         )
       ) AS eval GROUP BY projectSalesperson)
    AS bidsResult ON bidsResult.projectSalesperson = leadsResult.projectSalesperson_1

  LEFT OUTER JOIN

  (SELECT SUM(eval.total) AS grossSales, IFNULL(eval.projectSalesperson, 0) AS projectSalesperson
   FROM(
         (SELECT IFNULL(p.projectSalesperson, 0) AS projectSalesperson, bidTotal + IFNULL(bidScopeChangeTotal, 0) AS total
          FROM evaluationBid AS b
            JOIN evaluation AS e ON e.evaluationID = b.evaluationID
            JOIN project AS p ON p.projectID = e.projectID
            JOIN property AS t ON t.propertyID = p.propertyID
            JOIN customer AS c ON c.customerID = t.customerID
            LEFT JOIN user AS u ON u.userID = p.projectSalesperson
          WHERE c.companyID = :companyID
                AND e.evaluationCancelled IS NULL
                AND e.deletedAt IS NULL
                AND p.projectCancelled IS NULL
                AND p.deletedAt IS NULL
                AND b.bidFirstSent IS NOT NULL
                AND b.bidAccepted IS NOT NULL
                AND b.bidAccepted >= :startDate
                AND b.bidAccepted <= :endDate
         )
         UNION ALL
         (SELECT IFNULL(p.projectSalesperson, 0) AS projectSalesperson, bidTotal + IFNULL(bidScopeChangeTotal, 0) AS total
          FROM customBid AS b
            JOIN evaluation AS e ON e.evaluationID = b.evaluationID
            JOIN project AS p ON p.projectID = e.projectID
            JOIN property AS t ON t.propertyID = p.propertyID
            JOIN customer AS c ON c.customerID = t.customerID
            LEFT JOIN user AS u ON u.userID = p.projectSalesperson
          WHERE c.companyID = :companyID
                AND e.evaluationCancelled IS NULL
                AND e.deletedAt IS NULL
                AND p.projectCancelled IS NULL
                AND p.deletedAt IS NULL
                AND b.bidFirstSent IS NOT NULL
                AND b.bidAccepted IS NOT NULL
                AND b.bidAccepted >= :startDate
                AND b.bidAccepted <= :endDate
         )
       ) AS eval
   GROUP BY projectSalesperson) AS grossSalesResult
    ON grossSalesResult.projectSalesperson = leadsResult.projectSalesperson_1

  LEFT OUTER JOIN

  (SELECT SUM(eval.total) AS grossBids, IFNULL(eval.projectSalesperson, 0) AS projectSalesperson
   FROM(
         (SELECT IFNULL(p.projectSalesperson, 0) AS projectSalesperson, bidTotal + IFNULL(bidScopeChangeTotal, 0) AS total
          FROM evaluationBid AS b
            JOIN evaluation AS e ON e.evaluationID = b.evaluationID
            JOIN project AS p ON p.projectID = e.projectID
            JOIN property AS t ON t.propertyID = p.propertyID
            JOIN customer AS c ON c.customerID = t.customerID
            LEFT JOIN user AS u ON u.userID = p.projectSalesperson
          WHERE c.companyID = :companyID
                AND e.evaluationCancelled IS NULL
                AND e.deletedAt IS NULL
                AND p.projectCancelled IS NULL
                AND p.deletedAt IS NULL
                AND b.bidFirstSent IS NOT NULL
                AND b.bidFirstSent >= :startDate
                AND b.bidFirstSent <= :endDate
         )
         UNION ALL
         (SELECT IFNULL(p.projectSalesperson, 0) AS projectSalesperson, bidTotal + IFNULL(bidScopeChangeTotal, 0) AS total
          FROM customBid AS b
            JOIN evaluation AS e ON e.evaluationID = b.evaluationID
            JOIN project AS p ON p.projectID = e.projectID
            JOIN property AS t ON t.propertyID = p.propertyID
            JOIN customer AS c ON c.customerID = t.customerID
            LEFT JOIN user AS u ON u.userID = p.projectSalesperson
          WHERE c.companyID = :companyID
                AND e.evaluationCancelled IS NULL
                AND e.deletedAt IS NULL
                AND p.projectCancelled IS NULL
                AND p.deletedAt IS NULL
                AND b.bidFirstSent IS NOT NULL
                AND b.bidFirstSent >= :startDate
                AND b.bidFirstSent <= :endDate
         )
       ) AS eval
   GROUP BY projectSalesperson) AS grossBidsResult
    ON grossBidsResult.projectSalesperson = leadsResult.projectSalesperson_1

  JOIN

  (SELECT SUM(spendAmount) AS totalMarketingCosts
   FROM marketingSpend AS s
     LEFT JOIN marketingType AS t ON t.marketingTypeID = s.marketingTypeID
   WHERE t.companyID = :companyID
         AND s.startDate <= :endDate
         AND s.endDate >= :startDate) AS marketingCostsResult

  LEFT OUTER JOIN

  (SELECT IFNULL(projectSalesperson, 0) AS projectSalesperson, COUNT(total) AS sales FROM
    ((SELECT bidAccepted AS total, IFNULL(projectSalesperson, 0) AS projectSalesperson
      FROM evaluationBid AS b
        JOIN evaluation AS e ON e.evaluationID = b.evaluationID
        JOIN project AS p ON p.projectID = e.projectID
        JOIN property AS t ON t.propertyID = p.propertyID
        JOIN customer AS c ON c.customerID = t.customerID
        LEFT JOIN user AS u ON u.userID = p.projectSalesperson
        WHERE c.companyID = :companyID
            AND e.evaluationCancelled IS NULL
            AND e.deletedAt IS NULL
            AND p.projectCancelled IS NULL
            AND p.deletedAt IS NULL
            AND b.bidFirstSent IS NOT NULL
            AND b.bidAccepted IS NOT NULL
            AND b.bidAccepted >= :startDate
            AND b.bidAccepted <= :endDate
     )
     UNION ALL
     (SELECT bidAccepted AS total, IFNULL(projectSalesperson, 0) AS projectSalesperson
      FROM customBid AS b
        JOIN evaluation AS e ON e.evaluationID = b.evaluationID
        JOIN project AS p ON p.projectID = e.projectID
        JOIN property AS t ON t.propertyID = p.propertyID
        JOIN customer AS c ON c.customerID = t.customerID
        LEFT JOIN user AS u ON u.userID = p.projectSalesperson
        WHERE c.companyID = :companyID
            AND e.evaluationCancelled IS NULL
            AND e.deletedAt IS NULL
            AND p.projectCancelled IS NULL
            AND p.deletedAt IS NULL
            AND b.bidFirstSent IS NOT NULL
            AND b.bidAccepted IS NOT NULL
            AND b.bidAccepted >= :startDate
            AND b.bidAccepted <= :endDate
     )
    )
      AS eval
  GROUP BY projectSalesperson) AS salesResult ON salesResult.projectSalesperson = leadsResult.projectSalesperson_1
SQL;

        $query = $this->getDb()->prepare($sql);
        $query->bindValue(':companyID', $this->getCompanyID());
        $query->bindValue(':startDate', $this->getStartDate()->toDateTimeString());
        $query->bindValue(':endDate', $this->getEndDate()->toDateTimeString());
        $query->execute();

        $results = $this->fetchResults($query);

        $headers = [
            'employee' => [
                'name' => 'Employee',
                'description' => ''
            ],
            'leads' => [
                'name' => 'Leads',
                'description' => ''
            ],
            'bids' => [
                'name' => 'Bids',
                'description' => ''
            ],
            'sales' => [
                'name' => 'Sales',
                'description' => ''
            ],
            'grossSales' => [
                'name' => 'Gross Sales',
                'description' => 'Sum of Accepted Bids',
                'type' => Report::VALUE_TYPE_MONEY
            ],
            'grossBids' => [
                'name' => 'Gross Bids',
                'description' => 'Sum of All Bids',
                'type' => Report::VALUE_TYPE_MONEY
            ],
            'costPerLead' => [
                'name' => 'Average Cost Per Lead',
                'description' => 'Cost Per Lead = Total Marketing Costs/Leads',
                'type' => Report::VALUE_TYPE_MONEY
            ],
            'averageProjectBid' => [
                'name' => 'Average Project Bid',
                'description' => 'Average Project Bid = Gross Bids/Bids',
                'type' => Report::VALUE_TYPE_MONEY
            ],
            'averageProjectSold' => [
                'name' => 'Average Project Sold',
                'description' => 'Average Project Sold = Gross Sales/Sales',
                'type' => Report::VALUE_TYPE_MONEY
            ]
        ];

        $return = [
            'headers' => $headers,
            'data' => []
        ];
        foreach ($results as $employee_data) {
            foreach ($employee_data as $key => &$value) {
                if (!isset($headers[$key]['type'])) {
                    continue;
                }
                $value = $this->formatValue($headers[$key]['type'], $value);
            }
            $return['data'][] = $employee_data;
        }
        return $return;
    }
}
