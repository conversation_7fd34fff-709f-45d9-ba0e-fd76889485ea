<?php

declare(strict_types=1);

namespace App\ResourceJobs\User;

use App\Attributes\JobAttribute;
use App\Classes\Acl;
use App\Classes\MailingList;
use App\Resources\UserResource;
use App\Services\DomainService;
use Common\Models\Reseller;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\EntityNotFoundException;
use Core\StaticAccessors\Config;
use Throwable;

/**
 * Class MailingListRemoveJob
 *
 * @package App\ResourceJobs\User
 */
#[JobAttribute(type: 25)]
class MailingListRemoveJob extends Job
{
    /**
     * MailingListRemoveJob constructor
     *
     * @param int $user_id
     */
    public function __construct(protected int $user_id)
    {}

    /**
     * Handle job
     *
     * Unsubscribe member from registration mailing list in MailChimp software
     *
     * @param DomainService $domain_service
     * @throws JobFailedException
     * @throws \Core\Exceptions\AppException
     */
    public function handle(DomainService $domain_service): void
    {
        $user_resource = UserResource::make(Acl::make());

        try {
            $user_scope = Scope::make()
                ->fields([
                    'id', 'email'
                ])
                ->with([
                    'company' => [
                        'fields' => ['reseller_id']
                    ]
                ]);
            $user = $user_resource
                ->entity($this->user_id)
                ->scope($user_scope)
                ->run();
        } catch (EntityNotFoundException $e) {
            throw new JobFailedException('Unable to find user: %s', $this->user_id);
        }

        try {
            $mailing_list = new MailingList();
            if (!$mailing_list->isEnabled()) {
                return;
            }

            $list_id = Config::get('mail.mailchimp.list_id');
            // if no list id, then we don't proceed
            if ($list_id === null) {
                return;
            }

            $mailing_list->removeListMember($list_id, $user->email);
        } catch (Throwable $e) {
            throw (new JobFailedException('Unable to unsubscribe member from mailing list'))->setLastException($e);
        }
    }
}
