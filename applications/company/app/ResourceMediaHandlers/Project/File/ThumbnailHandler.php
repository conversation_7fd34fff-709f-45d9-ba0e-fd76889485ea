<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Project\File;

use App\Classes\Func;
use App\ResourceMediaHandlers\BaseCompanyFileVariantHandler;
use App\Resources\File\VariantResource;
use App\Traits\ResourceMedia\ImageTrait;
use Common\Models\ProjectFile;
use Core\Components\Asset\StaticAccessors\Asset;
use Core\Components\Http\Classes\ContentType;
use Core\Components\Http\Classes\Http;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Entity;
use Core\Exceptions\AppException;
use Spatie\PdfToImage\Pdf;

/**
 * Class ThumbnailHandler
 *
 * @package App\ResourceMediaHandlers\Project\File
 */
class ThumbnailHandler extends BaseCompanyFileVariantHandler
{
    use ImageTrait;

    /**
     * @var string[][] Mapping of content types to default icon name
     */
    protected static array $content_type_map = [
        'exact' => [
            'application/pdf' => 'pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'ms_word',
            'application/msword' => 'ms_word',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'ms_excel',
            'application/vnd.ms-excel' => 'ms_excel'
        ],
        'partial' => [
            'image/*' => 'image',
            'video/*' => 'video',
            'audio/*' => 'audio',
            '*/*' => 'file'
        ]
    ];

    /**
     * Generate thumbnail image from variant entity
     *
     * @param Entity $entity
     * @return array
     * @throws AppException
     * @throws \Spatie\PdfToImage\Exceptions\PdfDoesNotExist
     */
    public function generate(Entity $entity): array
    {
        try {
            $temp_file = Func::createTempFile(null, false);

            $original_path = $this->getOriginalPathFromEntity($entity);
            if (Http::getMimeType($original_path) === 'application/pdf') {
                $pdf = new Pdf($original_path);
                $pdf->setResolution(300);
                $pdf->saveImage($temp_file);
                $original_path = $temp_file;
            }

            $manager = $this->newImageManager();
            $image = $manager->make($original_path)
                ->fit(240, 240, function ($constraint) {
                    $constraint->upsize();
                })
                ->save($temp_file);
            $extension = Http::getExtensionByMimeType($image->mime());
            $data = $this->saveFileVariant($entity, "thumbnail.{$extension}", $temp_file);

            $image->destroy();
            return $data;
        } finally {
            if (isset($temp_file) && file_exists($temp_file)) {
                unlink($temp_file);
            }
        }
    }

    /**
     * Get company id from model
     *
     * @param ProjectFile $project_file
     * @return int|null
     */
    protected function getCompanyID(ProjectFile $project_file): ?int
    {
        $user = $this->resource->acl()->user();
        if ($user !== null) {
            return $user->companyID;
        }
        $company_id = ProjectFile::query()
            ->withCustomer()
            ->whereKey($project_file->getKey())
            ->value('companyID');
        return $company_id !== null ? (int) $company_id : null;
    }

    /**
     * Get default icon path from content type
     *
     * @param string $content_type
     * @return string
     * @throws AppException
     */
    protected function getPathByContentType(string $content_type): string
    {
        if (isset(static::$content_type_map['exact'][$content_type])) {
            return static::$content_type_map['exact'][$content_type];
        }
        $content_type = ContentType::parse($content_type);
        foreach (static::$content_type_map['partial'] as $partial_content_type => $name) {
            if (!ContentType::parse($partial_content_type)->matches($content_type)) {
                continue;
            }
            return Asset::path('resourceImage', "media-thumbnails/{$name}.png");
        }
        throw new AppException('No path found for content type: %s', $content_type);
    }

    /**
     * Get response for media controller
     *
     * @param mixed $id
     * @param array $config
     * @return FileResponse
     * @throws AppException
     * @throws \Core\Components\Resource\Exceptions\MediaNotFoundException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $project_file = $this->resource->findOrFail($id);

        if (!$this->resource->hasMediaCompanyID()) {
            if (($company_id = $this->getCompanyID($project_file)) === null) {
                throw new AppException('No company id defined for project file: %s', $id);
            }
            $this->resource->setMediaCompanyID($company_id);
        }

        $file = $project_file->file;
        if (in_array($file->contentType, ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'])) {
            $data = $this->getVariantFromField(VariantResource::TYPE_PROJECT_FILE_THUMBNAIL, 'file_id', $project_file);
        } else {
            $data = [
                'path' => $this->getPathByContentType($file->contentType),
                'content_type' => 'image/png',
                'filename' => 'thumbnail.png'
            ];
        }

        $response = Response::file($data['path']);
        $response->contentType($data['content_type']);
        $response->filename($data['filename']);
        return $response;
    }
}
