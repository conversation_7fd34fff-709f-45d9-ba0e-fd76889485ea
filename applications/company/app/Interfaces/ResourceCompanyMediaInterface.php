<?php

namespace App\Interfaces;

use Core\Components\Http\Classes\URLBuilder;

/**
 * Interface ResourceCompanyMediaInterface
 *
 * @package App\Interfaces
 */
interface ResourceCompanyMediaInterface extends \Core\Components\Resource\Interfaces\ResourceMediaInterface
{
    /**
     * Determines if media company id has been set
     *
     * @return bool
     */
    public function hasMediaCompanyID(): bool;

    /**
     * Set media company id
     *
     * Used for paths to files
     *
     * @param int|null $company_id
     */
    public function setMediaCompanyID(?int $company_id): void;

    /**
     * Get media company id
     *
     * @return int|null
     */
    public function getMediaCompanyID(): ?int;

    /**
     * Create media URL from existing URL builder instance
     *
     * @param URLBuilder $url
     * @return URLBuilder
     */
    public function buildMediaUrl(URLBuilder $url): URLBuilder;
}
