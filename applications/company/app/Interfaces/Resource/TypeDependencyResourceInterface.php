<?php

namespace App\Interfaces\Resource;

use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Requests\UpdateOrCreateRequest;

/**
 * Interface TypeDependencyResourceInterface
 *
 * @package App\Interfaces\Resource
 */
interface TypeDependencyResourceInterface
{
    /**
     * Update or create entity
     *
     * @param Entity $entity
     * @return UpdateOrCreateRequest
     */
    public function updateOrCreate(Entity $entity): UpdateOrCreateRequest;
}
