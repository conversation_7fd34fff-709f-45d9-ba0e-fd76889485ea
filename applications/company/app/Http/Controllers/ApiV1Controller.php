<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Core\Components\Http\StaticAccessors\Response;

/**
 * Class ApiV1Controller
 *
 * @package App\Http\Controllers
 */
class ApiV1Controller
{
    /**
     * Return no content response to show server is reachable, used by offline portions of application
     *
     * @return \Core\Components\Http\Classes\Response
     */
    public function ping()
    {
        return Response::create('', 204);
    }
}
