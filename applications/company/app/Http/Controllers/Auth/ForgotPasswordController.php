<?php

declare(strict_types=1);

namespace App\Http\Controllers\Auth;

use App\Exceptions\Api\UnprocessableEntityException;
use App\NotificationJobs\User\PasswordResetNotificationJob;
use Common\Models\{User, UserPasswordReset};
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\RequestValidation\Classes\Validation;
use Ramsey\Uuid\Uuid;

/**
 * Class ForgotPasswordController
 *
 * @package App\Http\Controllers\Auth
 */
class ForgotPasswordController
{
    /**
     * Process forgot password request
     *
     * Verify data, create user password reset entry, and send notification to user.
     *
     * @param Validation $validation
     * @return JSONResponse
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     */
    public function process(Validation $validation): JSONResponse
    {
        $validation->config([
            'email' => [
                'label' => 'Email',
                'rules' => 'trim|required|email'
            ]
        ]);
        $validator = $validation->run();
        $errors = $validator->errors();
        if ($validator->valid()) {
            $user = User::with('company')->where('userEmail', $validator->data('email'))->first();
            if ($user === null) {
                $errors->add('email', 'Unable to find user with that email');
            }
        }
        if ($errors->count() > 0) {
            throw UnprocessableEntityException::fromValidator($validator);
        }

        UserPasswordReset::ofUser($user)->whereNull('completedAt')->delete();

        $id = Uuid::uuid4();

        PasswordResetNotificationJob::enqueue($id);

        // save password reset entry
        $user->passwordResets()->create([
            'userPasswordResetID' => $id->getBytes()
        ]);

        return Response::json([
            'email' => $validator->data('email')
        ]);
    }
}
