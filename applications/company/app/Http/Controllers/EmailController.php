<?php

namespace App\Http\Controllers;

use App\Classes\Acl;
use App\Resources\CompanyResource;
use App\Resources\UserResource;
use Common\Models\EvaluationDrawing;
use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Exceptions\EntityNotFoundException;
use Core\Components\Resource\Exceptions\MediaNotFoundException;
use Core\StaticAccessors\Path;
use Exception;

class EmailController
{
    public function image(RequestInterface $request)
    {
        $input = $request->input();

        $type = null;
        if (($id = $input->get('bid')) !== null) {
            $type = 'drawing';
        } elseif (
            ($id = $input->get('sales')) !== null ||
            ($id = $input->get('installer')) !== null
        ) {
            $type = 'user';
        } elseif (($id = $input->get('company')) !== null) {
            $type = 'company';
        }

        if ($type === null) {
            throw new HttpResponseException(404);
        }
        switch ($type) {
            case 'drawing':
                $drawing = EvaluationDrawing::select(
                    'ed.evaluationDrawing as drawing_name', 'e.evaluationID as evaluation_id',
                    'p.projectID as project_id', 'c.companyID as company_id'
                )
                    ->from('evaluationDrawing as ed')
                    ->join('evaluationBid as eb', 'eb.evaluationID', '=', 'ed.evaluationID')
                    ->join('evaluation as e', 'e.evaluationID', '=', 'eb.evaluationID')
                    ->join('project as p', 'p.projectID', '=', 'e.projectID')
                    ->join('customer as c', 'c.customerID', '=', 'p.customerID')
                    ->where('eb.bidID', $id)
                    ->whereNull('e.deletedAt')
                    ->orderBy('ed.evaluationDrawingDate', 'desc')
                    ->limit(1)
                    ->first();
                if ($drawing === null) {
                    throw new HttpResponseException(404);
                }
                $drawing_path = Path::evaluationDrawing(
                    $drawing->drawing_name, $drawing->company_id, $drawing->project_id, $drawing->evaluation_id
                );
                return Response::file($drawing_path)->download($drawing->drawing_name);
            case 'user':
                try {
                    $userResource = UserResource::make(Acl::make());
                    return $userResource->getMediaHandler('image', 'email_thumbnail')->getResponse($id);
                } catch (Exception $e) {
                    if ($e instanceof EntityNotFoundException || $e instanceof MediaNotFoundException) {
                        throw new HttpResponseException(404);
                    } else {
                        error_log($e);
                        throw new HttpResponseException(500);
                    }
                }
            case 'company':
                try {
                    $companyResource = CompanyResource::make(Acl::make());
                    return $companyResource->getMediaHandler('logo', 'email_thumbnail')->getResponse($id);
                } catch (Exception $e) {
                    if ($e instanceof EntityNotFoundException || $e instanceof MediaNotFoundException) {
                        throw new HttpResponseException(404);
                    } else {
                        error_log($e);
                        throw new HttpResponseException(500);
                    }
                }
            default:
                throw new HttpResponseException(404);
        }
    }

    /**
     * Unsubscribe customer
     *
     * @deprecated Unsubscribe is no longer handled in this fashion
     *
     * @return \Core\Components\Http\Responses\ViewResponse
     */
    public function unsubscribe()
    {
        // @TODO Need to update view: pages.email.unsubscribe and add unsubscribe link back to emails
        // @TODO before we can use unsubscribe again
        throw new HttpResponseException(404);
    }
}
