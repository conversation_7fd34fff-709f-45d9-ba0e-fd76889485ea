<?php

namespace App\Http\Controllers;

use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\StaticAccessors\Response;
use Core\StaticAccessors\Path;

class MainController
{
    public function page(RequestInterface $request)
    {
        $request_path = ltrim($request->uri()->path(), '/');
        $base_path = Path::get('appLegacy');
        // test ajax directory
        $path = $base_path . 'ajax/' . $request_path;
        if (is_file($path)) {
            return Response::includeFile($path);
        }
        // test main directory
        $path = $base_path . $request_path;
        if (is_file($path)) {
            return Response::includeFile($path);
        }
        throw new HttpResponseException(404);
    }
}
