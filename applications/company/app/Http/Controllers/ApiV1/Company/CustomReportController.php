<?php

declare(strict_types=1);

namespace App\Http\Controllers\ApiV1\Company;

use App\Exceptions\Api\UnprocessableEntityException;
use App\Http\Responses\ApiResponse;
use App\Resources\Company\CustomReportResource;
use App\Services\CompanyCustomReport\Exceptions\InputValidationException;
use App\Services\CompanyCustomReportService;
use App\Traits\Resource\Controller\ActionTrait;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\StaticAccessors\Response;
use Ramsey\Uuid\Uuid;
use Throwable;

/**
 * Class CustomReportController
 *
 * @package App\Http\Controllers\ApiV1\Company
 */
class CustomReportController
{
    use ActionTrait;

    /**
     * @var string Resource class
     */
    protected $resource = CustomReportResource::class;

    /**
     * CustomReportController constructor
     */
    public function __construct()
    {
        $this->registerFormat('list-v1', 'application/vnd.adg.fx.list-v1+json');
    }

    /**
     * Run report and send job for result to be generated
     *
     * @param string $id
     * @param RequestInterface $request
     * @return ApiResponse
     * @throws \App\Exceptions\ApiException
     */
    public function run($id, RequestInterface $request): ApiResponse
    {
        try {
            $id = Uuid::fromString($id);
            $service = CompanyCustomReportService::fromID($id);
            try {
                $id = $service->queueRun(Auth::user()->getKey(), $request->input()->post());
                return Response::api([
                    'result_id' => $id->toString()
                ]);
            } catch (InputValidationException $e) {
                throw UnprocessableEntityException::fromErrors($e->getErrors());
            }
        } catch (Throwable $e) {
            $this->handleException($e);
        }
    }
}
