<?php

declare(strict_types=1);

namespace App\Http\Controllers\ApiV1\System\Form;

use App\Exceptions\Api\BadRequestException;
use App\Resources\System\Form\ItemResource;
use App\Services\Form\Exceptions\FormException;
use App\Services\Form\Types\SystemType;
use App\Traits\Resource\Controller\PolyActionTrait;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Http\Requests\ResourceRequest;
use Throwable;

/**
 * Class ItemController
 *
 * @package App\Http\Controllers\ApiV1\System\Form
 */
class ItemController
{
    use PolyActionTrait;

    /**
     * @var string Resource class
     */
    protected $resource = ItemResource::class;

    /**
     * ItemController constructor
     */
    public function __construct()
    {
        $this->registerFormat('collection-v1', 'application/vnd.adg.fx.collection-v1+json');
    }

    /**
     * Retrieve system form in create format
     *
     * @param string $id
     * @param ResourceRequest $request
     * @return \Core\Components\Http\Responses\JSONResponse
     * @throws \Core\Exceptions\AppException|Throwable
     */
    public function retrieve($id, ResourceRequest $request)
    {
        try {
            $this->registerFormat('setup-v1', 'application/vnd.adg.fx.setup-v1+json');
            $request = $this->handleRequest($request);
            if ($request->getFormat() !== 'setup-v1') {
                throw new BadRequestException(1003);
            }
            $type = SystemType::getByID(Auth::acl(), $id);
            return Response::json($type->toSetupFormat());
        } catch (Throwable $e) {
            if ($e instanceof FormException) {
                $e = (new BadRequestException(1015, 'Unable to retrieve form'))->setLastException($e);
            }
            $this->handleException($e);
        }
    }
}
