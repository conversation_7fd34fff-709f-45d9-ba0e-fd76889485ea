<?php

declare(strict_types=1);

namespace App\Http\Controllers\App;

use App\Classes\Func;
use Core\Components\Http\Responses\{FileResponse, ViewResponse};
use Core\Components\Http\StaticAccessors\{Response, View};
use Core\Exceptions\AppException;
use Dompdf\Dompdf;

class UserAgreementController
{
    public function index(): ViewResponse
    {
        return Response::view('pages.app.user-agreement', [
            'content' => View::fetch('pages.app.user-agreement.default')
        ]);
    }

    /**
     * @todo Add caching to PDF generation since this information is pretty static
     */
    public function download(): FileResponse
    {
        $pdf = new Dompdf();
        $pdf->loadHtml(View::fetch('pages.app.user-agreement.pdf', [
            'content' => View::fetch('pages.app.user-agreement.default')
        ])->render());
        $pdf->render();
        $temp_file = Func::createTempFile('pdf', false);
        if (!file_put_contents($temp_file, $pdf->output())) {
            throw new AppException('Unable to write to file: %s', $temp_file);
        }
        return Response::file($temp_file)->download('user_agreement.pdf')
            ->contentType('application/pdf')
            ->onStreamEnd(fn() => unlink($temp_file));
    }
}
