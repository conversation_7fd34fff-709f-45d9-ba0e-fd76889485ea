<?php

declare(strict_types=1);

namespace App\Http\Controllers\App\API\Integrations;

use App\Classes\{Email, Log, Security};
use App\NotificationJobs\Email\Message\RecipientBounceNotificationJob;
use App\Services\Email\Classes\{Manager, Message};
use App\Services\Email\Interfaces\AddressInterface;
use App\Services\Email\Types\System\EmailMessageRecipientBounceType;
use Carbon\Carbon;
use Common\Models\{
    EmailAutoResponseRecipient,
    EmailMessageAddress,
    EmailMessageRecipient,
    MailgunWebhookLog,
    SmtpCredential
};
use Core\Classes\Str;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\StaticAccessors\{Response, View};
use Core\Components\Validation\Rules\InternetRule;
use Core\Exceptions\AppException;
use Core\StaticAccessors\{App, Config};
use Exception;
use Monolog\Logger;
use PHPMailer\PHPMailer\PHPMailer;
use Throwable;
use Ramsey\Uuid\{Uuid, UuidInterface};

/**
 * Class MailgunController
 *
 * @package App\Http\Controllers\App\API\Integrations
 */
class MailgunController
{
    /**
     * @var null|Logger
     */
    protected ?Logger $log = null;

    /**
     * Get logger instance
     *
     * @return Logger
     */
    protected function getLog(): Logger
    {
        if ($this->log === null) {
            $this->log = Log::create('mailgun_event_webhook', [
                'email' => [
                    'subject' => 'Mailgun Event Webhook'
                ],
                'slack' => [
                    'username' => 'mailgun-event-webhook',
                    'config' => 'mail'
                ],
                'file' => 'mail.log'
            ]);
        }
        return $this->log;
    }

    /**
     * Log raw request to database for better debugging and return ID for quick lookup
     *
     * @param RequestInterface $request
     * @return UuidInterface
     * @throws Exception
     */
    protected function logRequest(RequestInterface $request): UuidInterface
    {
        $id = Uuid::uuid4();
        try {
            (new MailgunWebhookLog([
                'mailgunWebhookLogID' => $id->getBytes(),
                'body' => $request->get(),
                'createdAt' => Carbon::now('UTC')
            ]))->quietSave();
        } catch (Throwable $e) {
            $this->getLog()->warning('Unable to log request to database', [
                'exception' => $e,
                'webhook_log_id' => $id->toString(),
                'body' => $request->get()
            ]);
        }
        return $id;
    }

    /**
     * Validate request came from Mailgun
     *
     * @param RequestInterface $request
     * @param string $path
     * @return bool
     */
    protected function validateRequest(RequestInterface $request, $path = ''): bool
    {
        $token = $request->get("{$path}token");
        $timestamp = $request->get("{$path}timestamp");
        $signature = $request->get("{$path}signature");
        if (empty($token) || empty($timestamp) || empty($signature)) {
            return false;
        }
        if (!Security::compareHMAC($signature, $timestamp . $token, Config::get('mail.mailgun.api_key'))) {
            return false;
        }
        return true;
    }

    /**
     * Pull delivery status text from request and format for error log
     *
     * @param RequestInterface $request
     * @return string
     */
    protected function getDeliveryStatusText(RequestInterface $request): string
    {
        $delivery_status = '[' . $request->get('event-data.delivery-status.code') . ']';
        if (($ds_message = $request->get('event-data.delivery-status.message')) === '') {
            $ds_message = $request->get('event-data.delivery-status.description');
        }
        $delivery_status .= " {$ds_message}";
        return $delivery_status;
    }

    /**
     * Get recipient based on address and optional message id
     *
     * If no message id is provided, the latest email sent to that address within the last 4 hours will be selected.
     *
     * @param string $recipient_address
     * @param string|null $message_id
     * @return EmailMessageRecipient|null
     */
    protected function getRecipient(string $recipient_address, ?string $message_id = null): ?EmailMessageRecipient
    {
        $query = EmailMessageRecipient::query()
            ->select([
                'emailMessageRecipients.*', 'emailMessageAddresses.address as recipientAddress',
                'emailMessageAddresses.name as recipientName', 'emailMessages.type as messageType',
                'emailMessages.sentBySmtpCredentialID as messageSentBySmtpCredentialID'
            ])
            ->join('emailMessages', 'emailMessages.emailMessageID', '=', 'emailMessageRecipients.emailMessageID')
            ->join('emailMessageAddresses', 'emailMessageAddresses.emailMessageAddressID', '=', 'emailMessageRecipients.emailMessageAddressID')
            ->where('emailMessageAddresses.address', $recipient_address)
            // this address type check is somewhat redundant since we only add these address types to the recipients
            // table in the first place
            ->whereIn('emailMessageAddresses.addressType', [
                EmailMessageAddress::ADDRESS_TYPE_TO, EmailMessageAddress::ADDRESS_TYPE_CC,
                EmailMessageAddress::ADDRESS_TYPE_BCC
            ]);
        if ($message_id !== null) {
            $query->where('emailMessages.emailMessageID', Uuid::fromString($message_id)->getBytes());
        } else {
            $query->where('emailMessages.sentAt', '>=', Carbon::now('UTC')->subHours(4))
                ->orderBy('emailMessages.sentAt', 'asc')
                ->limit(1);
        }
        return $query->lockForUpdate()->first();
    }

    /**
     * Handle delivered event and update status
     *
     * @param RequestInterface $request
     * @param array $user_vars
     * @throws AppException
     */
    protected function handleDeliveredEvent(RequestInterface $request, array $user_vars): void
    {
        if (($recipient = $request->get('event-data.recipient')) === null) {
            throw new AppException('No recipient defined');
        }
        DB::transaction(function () use ($recipient, $user_vars) {
            if (($recipient = $this->getRecipient($recipient, $user_vars['message_id'] ?? null)) === null) {
                throw new AppException('Unable to find recipient to handle delivered status');
            }
            // if a recipient already failed, then we don't want to ever mark it as delivered. this can possibly
            // happen with delayed bounces
            if ($recipient->status === EmailMessageRecipient::STATUS_FAILED) {
                return;
            }
            $recipient->status = EmailMessageRecipient::STATUS_DELIVERED;
            $recipient->deliveredAt = Carbon::now('UTC');
            $recipient->save();
        });
    }

    /**
     * Handle opened event and update status
     *
     * @param RequestInterface $request
     * @param array $user_vars
     * @throws AppException
     */
    protected function handleOpenedEvent(RequestInterface $request, array $user_vars): void
    {
        if (($recipient = $request->get('event-data.recipient')) === null) {
            throw new AppException('No recipient defined');
        }
        DB::transaction(function () use ($request, $recipient, $user_vars) {
            if (($recipient = $this->getRecipient($recipient, $user_vars['message_id'] ?? null)) === null) {
                throw new AppException('Unable to find recipient to handle opened status');
            }
            // if we haven't been notified of delivery, how could they open it yet... don't trust mailgun enough
            // to think this won't happen
            if ($recipient->status !== EmailMessageRecipient::STATUS_DELIVERED) {
                return;
            }
            $recipient->status = EmailMessageRecipient::STATUS_OPENED;
            $recipient->openedAt = Carbon::now('UTC');
            if (($ip = $request->get('event-data.ip')) !== null && ($ip = InternetRule::validateIp($ip, true)) !== false) {
                $ip_type_map = [
                    InternetRule::IP_TYPE_V4 => EmailMessageRecipient::OPENED_BY_IP_ADDRESS_TYPE_V4,
                    InternetRule::IP_TYPE_V6 => EmailMessageRecipient::OPENED_BY_IP_ADDRESS_TYPE_V6
                ];
                $recipient->openedByIpAddressType = $ip_type_map[$ip[0]];
                $recipient->openedByIpAddress = inet_pton($ip[1]);
            }
            $client_info = $request->get('event-data.client-info', []);
            if (!is_array($client_info)) {
                $client_info = [];
            }
            $opened_by_client_info = [
                'geolocation' => $request->get('event-data.geolocation')
            ];
            foreach ($client_info as $key => $value) {
                $opened_by_client_info[str_replace('-', '_', $key)] = $value;
            }
            $recipient->openedByClientInfo = $opened_by_client_info;
            $recipient->save();
        });
    }

    /**
     * Handle failed event and update status
     *
     * If a bounce, failure, or quota email should be sent to the sender, it has handled.
     *
     * @param RequestInterface $request
     * @param array $user_vars
     * @param UuidInterface $webhook_log_id
     * @throws AppException
     * @throws \App\Services\Email\Exceptions\MessageException
     */
    protected function handleFailedEvent(RequestInterface $request, array $user_vars, UuidInterface $webhook_log_id): void
    {
        if (($recipient = $request->get('event-data.recipient')) === null) {
            throw new AppException('No recipient defined');
        }
        $severity = $request->get('event-data.severity');
        if ($severity !== 'permanent') {
            // don't log non permanent (i.e. temporary) failures currently
            return;
        }
        $updated = false;
        DB::transaction(function () use (&$recipient, $user_vars, &$updated) {
            if (($recipient = $this->getRecipient($recipient, $user_vars['message_id'] ?? null)) === null) {
                throw new AppException('Unable to find recipient to handle permanent failed status');
            }
            // if already in failed status, then we ignore
            if ($recipient->status === EmailMessageRecipient::STATUS_FAILED) {
                return;
            }
            $recipient->status = EmailMessageRecipient::STATUS_FAILED;
            // deliveredAt could be filled out when a delayed bounce happens
            $recipient->deliveredAt = null;
            // openedAt could be filled out if spam detection loads the tracking pixel on the recipient
            // mail server. a delayed bounce could be sent after this meaning we should clear out these
            // fields just in case
            $recipient->openedAt = null;
            $recipient->openedByIpAddressType = null;
            $recipient->openedByIpAddress = null;
            $recipient->openedByClientInfo = null;
            $recipient->failedAt = Carbon::now('UTC');
            $recipient->save();
            $updated = true;
        });

        // if record wasn't recently updated, we just skip since the message failed before
        if (!$updated) {
            return;
        }

        $log_config = [
            'suppress-bounce' => [Log::ERROR, 'Message bounced due to suppression'],
            'suppress-complaint' => [Log::ERROR, 'Message bounced due to complaint suppression'],
            'bounce' => [Log::ERROR, 'Message bounced due to bad address or recipient server'],
            'generic' => [Log::CRITICAL, 'Message bounced due to generic or unknown problem'],
            'blacklist' => [Log::CRITICAL, 'Message bounced due to IP blacklisting'],
            'quota' => [Log::INFO, 'Message bounced due to full inbox']
        ];
        $log_failure = true;

        /** @var Manager $email_manager */
        $email_manager = App::get(Manager::class);
        $is_bounce = $recipient->messageType === $email_manager->getTypeByClass(EmailMessageRecipientBounceType::class);

        // standardize reason based on data
        $reason = $request->get('event-data.reason');
        if (!isset($log_config[$reason])) {
            $reason = 'generic';
        }
        switch ($reason) {
            case 'generic':
                $code = $request->get('event-data.delivery-status.code');
                // errors which come from Mailgun which seem standardized somehow but aren't documented
                $error_rules = [
                    [
                        // ip blacklisting
                        'code' => [550, 553, 554],
                        'message' => ['rbl', 'sbl', 'dnsbl', 'spam', 'junk', 'blocked', 'black listed'],
                        'reason' => 'blacklist'
                    ],
                    [
                        'code' => [498, 550, 554, 602, 612],
                        'message' => ['mx', 'access denied', 'too old', 'mailbox unavailable', 'rejected', 'loop'],
                        'reason' => 'bounce'
                    ],
                    [
                        'code' => [452, 550, 552],
                        'message' => ['quota', 'full'],
                        'reason' => 'quota'
                    ]
                ];
                if (($ds_message = $request->get('event-data.delivery-status.message')) === '') {
                    $ds_message = $request->get('event-data.delivery-status.description');
                }
                // loop through rules and see if message matches a more specific reason
                foreach ($error_rules as $rule) {
                    if (!in_array($code, $rule['code']) || !Str::contains($ds_message, $rule['message'], true)) {
                        continue;
                    }
                    $reason = $rule['reason'];
                    break;
                }
                break;
        }

        // send a bounce notification as long as this failure wasn't from a bounce email
        $recipient_id = $recipient->getUuidKey()->toString();
        if (!$is_bounce) {
            $send_bounce = true;
            // if reason is a blacklist, we resend email using backup if possible
            if ($reason === 'blacklist' && $recipient->messageSentBySmtpCredentialID !== null) {
                if (($sent_by_smtp_credential = SmtpCredential::find($recipient->messageSentBySmtpCredentialID)) === null) {
                    throw new AppException('Unable to find smtp credential: %d', $recipient->messageSentBySmtpCredentialID);
                }
                // if backup credential exists, then we resend using it to only the failed recipient
                if ($sent_by_smtp_credential->backupSmtpCredentialID !== null) {
                    $new_message_id = Message::resend($recipient->getUuid('emailMessageID')->toString(), function (Message $new_message) use ($recipient) {
                        $event_recipient = strtolower($recipient->recipientAddress);
                        $address = null;
                        foreach ($new_message->getRecipients() as $type => $recipients) {
                            /** @var AddressInterface $recipient */
                            foreach ($recipients as $recipient) {
                                // normalize address to lowercase since mailgun will change case of domains on email address
                                if (strtolower($recipient->getAddress()) !== $event_recipient) {
                                    continue;
                                }
                                $address = $recipient;
                                break 2;
                            }
                        }
                        if ($address === null) {
                            throw new AppException('Unable to find address %s in recipients list of message', $event_recipient);
                        }
                        $new_message->clearRecipients();
                        $new_message->to($address);
                        return $new_message;
                    }, $sent_by_smtp_credential->backupSmtpCredentialID);
                    $this->getLog()->critical('Message resent using backup SMTP connection due to blacklisting', [
                        'recipient_id' => $recipient_id,
                        'recipient' => $recipient->recipientAddress . ($recipient->recipientName !== null ? " - {$recipient->recipientName}" : ''),
                        'delivery_status' => $this->getDeliveryStatusText($request),
                        'resent_message_id' => $new_message_id->toString(),
                        'webhook_log_id' => $webhook_log_id->toString()
                    ]);
                    $send_bounce = false;
                    $log_failure = false;
                }
            }
            if ($send_bounce) {
                $reason_type_map = [
                    'suppress-bounce' => RecipientBounceNotificationJob::TYPE_SUPPRESS_BOUNCE,
                    'suppress-complaint' => RecipientBounceNotificationJob::TYPE_SUPPRESS_COMPLAINT,
                    'bounce' => RecipientBounceNotificationJob::TYPE_BOUNCE,
                    'generic' => RecipientBounceNotificationJob::TYPE_GENERIC,
                    'blacklist' => RecipientBounceNotificationJob::TYPE_GENERIC,
                    'quota' => RecipientBounceNotificationJob::TYPE_INBOX_FULL
                ];
                RecipientBounceNotificationJob::enqueue($recipient->getUuidKey(), $reason_type_map[$reason]);
                if (in_array($reason, ['suppress-bounce', 'bounce'])) {
                    $log_failure = Config::get('mail.log_bounces', false);
                }
            }
        }

        if ($log_failure) {
            $this->getLog()->addRecord($log_config[$reason][0], $log_config[$reason][1], [
                'recipient_id' => $recipient_id,
                'recipient' => $recipient->recipientAddress . ($recipient->recipientName !== null ? " - {$recipient->recipientName}" : ''),
                'delivery_status' => $this->getDeliveryStatusText($request),
                'webhook_log_id' => $webhook_log_id->toString()
            ]);
        }
    }

    /**
     * Handle Mailgun events
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     * @throws Exception
     */
    public function eventWebhook(RequestInterface $request): \Core\Components\Http\Responses\JSONResponse
    {
        $webhook_log_id = $this->logRequest($request);

        try {
            if (!$this->validateRequest($request, 'signature.')) {
                throw new AppException('Unable to verify request origin');
            }

            $user_vars = $request->get('event-data.user-variables', []);
            // if tracking has been disabled, we just stop here
            if (isset($user_vars['track']) && is_bool($user_vars['track']) && !$user_vars['track']) {
                return Response::json(['success' => true]);
            }

            $event = $request->get('event-data.event');
            switch ($event) {
                case 'delivered':
                    $this->handleDeliveredEvent($request, $user_vars);
                    break;
                case 'opened':
                    $this->handleOpenedEvent($request, $user_vars);
                    break;
                case 'failed':
                    $this->handleFailedEvent($request, $user_vars, $webhook_log_id);
                    break;
                default:
                    throw new AppException('Event not handled: %s', $event);
            }

            return Response::json(['success' => true]);
        } catch (Throwable $e) {
            $this->getLog()->error('Unable to process event', [
                'exception' => $e,
                'webhook_log_id' => $webhook_log_id->toString()
            ]);
            return Response::json(['error' => 'Unable to process event'], 406);
        }
    }

    /**
     * Handle any incoming messages which we are not tracking by sending an unmonitored mailbox reply
     *
     * @param RequestInterface $request
     * @param Email $email
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function unmonitoredWebhook(RequestInterface $request, Email $email): \Core\Components\Http\Responses\JSONResponse
    {
        $webhook_log_id = $this->logRequest($request);

        try {
            if (!$this->validateRequest($request)) {
                throw new AppException('Unable to verify request origin');
            }

            $recipient = $request->get('recipient');

            $addresses = PHPMailer::parseAddresses($request->get('from'));
            if (count($addresses) !== 1) {
                throw new AppException('Unable to parse from address');
            }
            $to = $addresses[0];
            $auto_response_recipient = EmailAutoResponseRecipient::query()
                ->where('type', EmailAutoResponseRecipient::TYPE_NOTIFICATION_INBOX_UNMONITORED)
                ->where('email', $to['address'])
                ->orderBy('createdAt', 'desc')
                ->limit(1)
                ->first();
            if ($auto_response_recipient === null || $auto_response_recipient->createdAt->lt(Carbon::now('UTC')->subDay())) {
                $body = View::fetch('emails.text.system.unmonitored', [
                    'recipient' => $recipient,
                    'subject' => $request->get('subject', '')
                ])->render();

                $email->type(Email::TYPE_SYSTEM);
                $email->from($recipient);
                $email->subject('Auto Response: Unmonitored Mailbox');
                $email->to($to['address'], $to['name'] ?? '');
                $email->text($body);
                $email->send();

                EmailAutoResponseRecipient::create([
                    'emailAutoResponseRecipientID' => Uuid::uuid4()->getBytes(),
                    'type' => EmailAutoResponseRecipient::TYPE_NOTIFICATION_INBOX_UNMONITORED,
                    'email' => $to['address']
                ]);
            } elseif ($auto_response_recipient !== null) {
                $auto_response_recipient->refresh();
                $auto_response_recipient->hits = $auto_response_recipient->hits + 1;
                $auto_response_recipient->lastHitAt = Carbon::now('UTC');
                $auto_response_recipient->save();
            }

            return Response::json(['success' => true]);
        } catch (Exception $e) {
            $this->getLog()->error('Error encountered', [
                'exception' => $e,
                'webhook_log_id' => $webhook_log_id->toString()
            ]);
            return Response::json(['error' => 'Error encountered'], 406);
        }
    }
}
