<?php

declare(strict_types=1);

namespace App\Http\Controllers\App\API;

use App\Exceptions\ApiException;
use App\Services\IntakeService;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Http\StaticAccessors\Response;
use Throwable;

/**
 * Class IntakeController
 *
 * @package App\Http\Controllers\App
 */
class IntakeController
{
    /**
     * Get list of available calendars
     *
     * @return JSONResponse
     * @throws ApiException
     */
    public function options(): JSONResponse
    {
        try {
            $intake_service = new IntakeService();
            $response = $intake_service->getAllOptions();
            return Response::json($response);
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }
}
