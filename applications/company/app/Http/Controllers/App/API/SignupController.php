<?php

declare(strict_types=1);

namespace App\Http\Controllers\App\API;

use App\Classes\Acl;
use App\Classes\Log;
use App\Exceptions\{ApiException, Api\ForbiddenException, Api\UnprocessableEntityException};
use App\Services\Form\Types\CompanyType;
use App\Services\Form\Types\SystemType;
use Carbon\Carbon;
use Common\Models\CompanySetup;
use Common\Models\CompanySetupProductItem;
use App\Resources\{Company\Form\ItemResource as CompanyFormItemResource,
    Company\PaymentMethodResource,
    CompanyResource,
    Company\SubscriptionResource as CompanySubscriptionResource,
    Form\Item\Group\Field\ProductResource,
    Product\CategoryResource,
    Product\ItemResource as ProductItemResource,
    SubscriptionResource,
    UnitResource};
use App\Resources\Industry\Product\ItemResource;
use App\Services\GoogleApi\Exceptions\GoogleApiException;
use App\Services\GoogleApi\Services\MapsService;
use App\Services\IntakeService;
use Common\Models\CompanyIntake;
use Common\Models\Timezone;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Responses\NoContentResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\RequestValidation\Classes\Validation as RequestValidation;
use Core\Components\Resource\Classes\{BatchRequest, Entity, Field, Scope};
use Core\Components\Resource\Requests\{CreateRequest, UpdateRequest};
use Core\Components\Validation\Classes\FieldConfig;
use Core\StaticAccessors\Config;
use Ramsey\Uuid\Uuid;
use Throwable;

/**
 * Class SetupController
 *
 * @package App\Http\Controllers\App\API
 */
class SignupController
{
    /**
     * Save address for company
     *
     * @param RequestValidation $validation
     * @return NoContentResponse
     * @throws ApiException
     * @throws ForbiddenException
     */
    public function saveAddress(RequestValidation $validation): NoContentResponse
    {
        $user = Auth::user();
        if (!$user->primary) {
            throw new ForbiddenException(1009, 'Access denied');
        }
        try {
            $config = FieldConfig::fromArray([
                'address' => [
                    'label' => 'address',
                    'rules' => 'trim|required|max_length[100]'
                ],
                'address_2' => [
                    'label' => 'address 2',
                    'rules' => 'trim|optional|max_length[50]'
                ],
                'city' => [
                    'label' => 'city',
                    'rules' => 'trim|required|max_length[40]'
                ],
                'state' => [
                    'label' => 'state',
                    'rules' => 'trim|required|in_array[states]'
                ],
                'postal_code' => [
                    'label' => 'postal code',
                    'rules' => 'trim|required|max_length[12]'
                ]
            ]);

            $lists = Config::get('lists');
            $config->store('states', array_merge(array_keys($lists['states']), array_keys($lists['us_territories']), array_keys($lists['provinces'])));

            $validation->config($config);
            $validator = $validation->run();
            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }

            // @todo move to job
            $maps_service = new MapsService();
            $geocode_data = [
                'timezone_id' => Timezone::DEFAULT_TIMEZONE,
                'latitude' => '0.00000000',
                'longitude' => '0.00000000'
            ];
            try {
                $full_address = "{$validator->data('address')}, {$validator->data('city')}, {$validator->data('state')} {$validator->data('zip')}";
                if (($result = $maps_service->getFirstGeolocation($full_address)) !== null) {
                    $geocode_data['latitude'] = $result['latitude'] ?? '0.00000000';
                    $geocode_data['longitude'] = $result['longitude'] ?? '0.00000000';
                    if ($result['has_coordinates']) {
                        $geocode_data['timezone_id'] = $maps_service->getTimezone("{$geocode_data['latitude']}, {$geocode_data['longitude']}");
                    }
                }
            } catch(GoogleApiException $e) {
                // ignore exceptions to prevent interruptions to registration, exceptions are already logged by library
            }

            $data = Entity::make([
                'id' => $user->companyID,
                'address' => $validator->data('address'),
                'address_2' => $validator->data('address_2'),
                'city' => $validator->data('city'),
                'state' => $validator->data('state'),
                'zip' => $validator->data('postal_code'),
                'latitude' => $geocode_data['latitude'],
                'longitude' => $geocode_data['longitude'],
                'timezone_id' => $geocode_data['timezone_id'] ?? Timezone::DEFAULT_TIMEZONE,
                'signup_status' => CompanyResource::SIGNUP_STATUS_INTAKE
            ]);

            CompanyResource::make(Auth::acl())->setAccessLevel(Field::ACCESS_LEVEL_PRIVATE)->partialUpdate($data)->run();
            return Response::noContent();
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Create the products and forms for the industry selected during intake
     *
     * @param $user
     * @param int $industry_id
     * @return NoContentResponse
     * @throws ApiException
     */
    protected function saveIntakeProductsWithForms($user, $industry_id): NoContentResponse
    {
        try {
            $acl = Auth::acl();
            $industry_product_scope = Scope::make()
                ->filter('intake_industry_id', 'eq', $industry_id);

            $products = ItemResource::make(Acl::make())->collection()->scope($industry_product_scope)->run()->toArray();
            $units = UnitResource::make($acl)->collection()->run()->keyBy('name');

            // find product categories to import
            $product_category_ids = [];
            foreach ($products as $product) {
                if (empty($product['product_category_name'])) {
                    continue;
                }
                if (in_array($product['product_category_name'], $product_category_ids)) {
                    continue;
                }
                array_push($product_category_ids, $product['product_category_name']);
            }

            // import product categories
            $category_resource = CategoryResource::make($acl);

            $new_categories = [];
            foreach ($product_category_ids as $category) {
                $category_item = [
                    'owner_type' => CategoryResource::OWNER_TYPE_COMPANY,
                    'owner_id' => $user->companyID,
                    'name' => $category
                ];
                $product_category_id = $category_resource->create(Entity::make($category_item))->run();
                $new_categories[$category] = $product_category_id;
            }

            // find forms to import
            $system_form_items = [];
            foreach ($products as $product) {
                if (in_array($product['form_name'], $system_form_items)) {
                    continue;
                }
                $type = SystemType::getByID($acl, $product['system_form_item_id']);
                $system_form_items[$product['form_name']] = [
                    'id' => $product['system_form_item_id'],
                    'category_name' => $product['product_category_name'],
                    'content' => $product['form_content'],
                    'setup_data' => $type->toSetupFormat()
                ];
            }

            foreach ($system_form_items as $item => $value) {

                $form_item = [
                    'name' => $item,
                    'form_item_id' => $value['setup_data']['form_item_id'],
                    'item' => $value['setup_data']['item'],
                    'source_form_id' => $value['id'],
                    'source_form_type' => CompanyFormItemResource::SOURCE_FORM_TYPE_SYSTEM,
                    'type' => CompanyFormItemResource::TYPE_BID,
                    'templates' => [],
                    'fields' => []
                ];
                $structure = $value['setup_data']['structure'];
                if ($structure['field_overrides']) {
                    foreach ($structure['field_overrides'] as $field) {
                        $source = $field['source'];
                        $new_field = [
                            'form_item_group_field_id' => $source['id'],
                            'type' => $source['type'],
                            'label' => $source['label'],
                            'display_label' => $source['display_label'],
                            'is_required' => $source['is_required'],
                            'config' => $source['config'],
                            'products' => [
                                [
                                    'action' => ProductResource::ACTION_ADD,
                                    'item_id' => $new_categories[$value['category_name']],
                                    'item_type' => ProductResource::TYPE_PRODUCT_CATEGORY
                                ]
                            ]
                        ];
                        array_push($form_item['fields'], $new_field);
                    }
                }

                if ($structure['template_overrides']) {
                    $template = $structure['template_overrides'][0]['source'];
                    $new_template = [
                        'content' => $value['content'],
                        'type' => $template['type'],
                        'form_item_group_template_id' => $template['id']
                    ];
                    array_push($form_item['templates'], $new_template);
                }
                $type = CompanyType::import($form_item);
                $type->setCompanyID($user->companyID);
                $type->save($acl);
            }

            $item_resource = ProductItemResource::make($acl);
            $companySetup = CompanySetup::where('companyID', $user->companyID)->first();

            foreach ($products as &$product) {
                $product_item = [
                    'owner_type' => ProductItemResource::OWNER_TYPE_COMPANY,
                    'owner_id' => $user->companyID,
                    'name' => $product['name'],
                    'description' => $product['description'],
                    'is_intangible' => false,
                    'pricing_type' => ProductItemResource::PRICING_TYPE_BASIC,
                    'unit_id' => $units[$product['unit']]['id'],
                    'prices' => [
                        [
                            'min_count' => '0',
                            'max_count' => null,
                            'price' => $product['price']
                        ]
                    ],
                    'categories' => [
                        $new_categories[$product['product_category_name']]
                    ]
                ];
                $item_resource->create(Entity::make($product_item))->run();

                $now = Carbon::now('UTC');
                CompanySetupProductItem::create([
                    'companySetupID' => $companySetup->companySetupID,
                    'intakeIndustryProductItemID' => Uuid::fromString($product['id'])->getBytes(),
                    'createdAt' => $now,
                    'createdByUserID' => $user->userID,
                    'updatedAt' => $now,
                    'updatedByUserID' => $user->userID
                ]);
                unset($product);
            }

            $now = Carbon::now('UTC');
            CompanySetup::where('companyID', $user->companyID)->update([
                'industryProductItemsImport' => true,
                'industryProductItemsImportCompletedAt' => $now,
                'updatedAt' => $now,
                'updatedByUserID' => $user->userID
            ]);

            return Response::noContent();
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Save intake step data
     *
     * @param RequestValidation $validation
     * @return NoContentResponse
     * @throws ApiException
     * @throws ForbiddenException
     */
    public function saveIntake(RequestValidation $validation): NoContentResponse
    {
        $user = Auth::user();
        if (!$user->primary) {
            throw new ForbiddenException(1009, 'Access denied');
        }
        try {
            $config = FieldConfig::fromArray([
                'user_count_id' => [
                    'label' => 'user count',
                    'rules' => 'required|type[int]|in_array[user_counts]'
                ],
                'industry_id' => [
                    'label' => 'industry',
                    'rules' => 'required|type[int]|in_array[industries]'
                ],
                'marketing_source_id' => [
                    'label' => 'marketing source',
                    'rules' => 'required|type[int]|in_array[marketing_sources]'
                ],
                'feature_ids.*' => [
                    'label' => 'features',
                    'rules' => 'optional|type[int]|in_array[features]',
                    'group_rules' => [
                        'type' => 'array'
                    ]
                ]
            ]);

            $intake_service = new IntakeService();
            $intake_options = $intake_service->getAllOptions();
            $config->store('user_counts', array_map(fn(array $option): int => $option['id'], $intake_options['user_counts']));
            $config->store('industries', array_map(fn(array $option): int => $option['id'], $intake_options['industries']));
            $config->store('marketing_sources', array_map(fn(array $option): int => $option['id'], $intake_options['marketing_sources']));
            $config->store('features', array_map(fn(array $option): int => $option['id'], $intake_options['features']));

            $validation->config($config);
            $validator = $validation->run();
            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }

            $company_intake = CompanyIntake::where('companyID', $user->companyID)->first();
            // create or update company intake record
            if ($company_intake === null) {
                $intake = new CompanyIntake([
                    'companyID' => $user->companyID,
                    'intakeUserCountID' => $validator->data('user_count_id'),
                    'intakeIndustryID' => $validator->data('industry_id'),
                    'intakeMarketingSourceID' => $validator->data('marketing_source_id'),
                ]);
                $intake->save();
                $intake->features()->sync($validator->data('feature_ids'));
            } else {
                $company_intake->intakeUserCountID = $validator->data('user_count_id');
                $company_intake->intakeIndustryID = $validator->data('industry_id');
                $company_intake->intakeMarketingSourceID = $validator->data('marketing_source_id');
                $company_intake->save();
                $company_intake->features()->sync($validator->data('feature_ids'));
            }

            $company_setup = CompanySetup::where('companyID', $user->companyID)->first();

            if (!$company_setup->industryProductItemsImport) {
                $this->saveIntakeProductsWithForms($user, $validator->data('industry_id'));
            }

            CompanyResource::make(Acl::make())->partialUpdate(Entity::make([
                'id' => $user->companyID,
                'signup_status' => CompanyResource::SIGNUP_STATUS_SUBSCRIPTION
            ]))->run();

            return Response::noContent();
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

    /**
     * Save payment profile, select subscription and move setup status to complete
     *
     * @param RequestValidation $validation
     * @return NoContentResponse
     * @throws ApiException
     * @throws ForbiddenException
     */
    public function saveSubscription(RequestValidation $validation): NoContentResponse
    {
        $user = Auth::user();
        if (!$user->primary) {
            throw new ForbiddenException(1009, 'Access denied');
        }
        try {
            $config = FieldConfig::fromArray([
                'subscription_id' => [
                    'label' => 'subscription id',
                    'rules' => 'required|type[int]'
                ],
                'subscription_option_id' => [
                    'label' => 'subscription option',
                    'rules' => 'required|type[int]'
                ],
                'credit_card' => [
                    'label' => 'number',
                    'rules' => 'trim|required'
                ],
                'expiration_date' => [
                    'label' => 'expiration date',
                    'rules' => 'trim|required'
                ],
                'card_code' => [
                    'label' => 'card code',
                    'rules' => 'trim|required|max_length[4]'
                ],
                'address' => [
                    'label' => 'address',
                    'rules' => 'trim|required|max_length[100]'
                ],
                'address_2' => [
                    'label' => 'address 2',
                    'rules' => 'trim|optional|max_length[50]'
                ],
                'city' => [
                    'label' => 'city',
                    'rules' => 'trim|required|max_length[40]'
                ],
                'state' => [
                    'label' => 'state',
                    'rules' => 'trim|required|in_array[states]'
                ],
                'postal_code' => [
                    'label' => 'postal code',
                    'rules' => 'trim|required|max_length[12]'
                ]
            ]);

            $lists = Config::get('lists');
            $config->store('states', array_merge(array_keys($lists['states']), array_keys($lists['us_territories']), array_keys($lists['provinces'])));

            $validation->config($config);
            $validator = $validation->run();
            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }

            $batch_request = BatchRequest::make()->sequential();

            $credit_card_data = Entity::make([
                'is_default' => true,
                'name' => 'Primary Card',
                'item_type' => PaymentMethodResource::TYPE_CREDIT_CARD,
                'item' => [
                    'number' => $validator->data('credit_card'),
                    'expiration_date' => $validator->data('expiration_date'),
                    'code' => $validator->data('card_code'),
                    'address' => $validator->data('address'),
                    'address_2' => $validator->data('address_2'),
                    'city' => $validator->data('city'),
                    'state' => $validator->data('state'),
                    'zip' => $validator->data('postal_code')
                ]
            ]);

            $acl = Auth::acl();
            $payment_method_request = PaymentMethodResource::make($acl)->polyCreate($credit_card_data);
            $batch_request->add($payment_method_request);

            // get subscription data to determine expiration date
            $subscription_resource = SubscriptionResource::make($acl);
            $subscription_option = $subscription_resource
                ->entity($validator->data('subscription_id'))
                ->run();
            $trial_expiration = Carbon::now('UTC');
            switch ($subscription_option->delay_interval_unit) {
                case SubscriptionResource::INTERVAL_UNIT_DAY:
                    $trial_expiration = $trial_expiration->addDays($subscription_option->delay_interval_length);
                    break;
                case SubscriptionResource::INTERVAL_UNIT_MONTH:
                    $trial_expiration = $trial_expiration->addMonths($subscription_option->delay_interval_length);
                    break;
                case SubscriptionResource::INTERVAL_UNIT_YEAR:
                    $trial_expiration = $trial_expiration->addYears($subscription_option->delay_interval_length);
                    break;
            }
            $trial_expiration = $trial_expiration->format('Y-m-d');

            $subscription_request = CompanySubscriptionResource::make($acl)
                ->create(Entity::make([
                    'company_id' => $user->companyID,
                    'subscription_option_id' => $validator->data('subscription_option_id')
                ]))->store('next_billing', $trial_expiration)->store('run_billing', false);

            $batch_request->add($subscription_request);

            $company_request = CompanyResource::make($acl)
                ->setAccessLevel(Field::ACCESS_LEVEL_PRIVATE)
                ->partialUpdate(Entity::make([
                    'id' => $user->companyID,
                    'signup_status' => CompanyResource::SIGNUP_STATUS_COMPLETE
                ]))
                ->scope(Scope::make()->fields(['name', 'created_at']))
                ->attach('batch.last_request', function (CreateRequest $subscription_request, UpdateRequest $company_request) {
                    $company_request->store('subscription_option_id', $subscription_request->response());
                });
            $batch_request->add($company_request);
            $batch_request->run();
            $company = $company_request->response();

            Log::company('setup')->info('Company started trial', [
                'company_id' => $user->companyID
            ]);

            return Response::noContent();
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }

//    /**
//     * Allow user to start trial by skipping subscription selection, move setup to complete status
//     *
//     * @return NoContentResponse
//     * @throws ApiException
//     * @throws ForbiddenException
//     */
//    public function skipSubscription(): NoContentResponse
//    {
//        $user = Auth::user();
//        if (!$user->primary) {
//            throw new ForbiddenException(1009, 'Access denied');
//        }
//        try {
//            $company_resource = CompanyResource::make(Acl::make());
//            $company_scope = Scope::make()->noFields()->with([
//                'subscription' => [
//                    'fields' => ['is_trial']
//                ]
//            ]);
//            $company = $company_resource->entity($user->companyID)->scope($company_scope)->run();
//
//            if (!$company->subscription->is_trial) {
//                throw new ForbiddenException(1009, 'Skip is only available for subscriptions with a trial');
//            }
//
//            $data = Entity::make([
//                'id' => $user->companyID,
//                'signup_status' => CompanyResource::SIGNUP_STATUS_COMPLETE
//            ]);
//            $company_resource->partialUpdate($data)->run();
//
//            Log::company('setup')->info('Company started trial from setup', [
//                'company_id' => $user->companyID
//            ]);
//
//            return Response::noContent();
//        } catch (Throwable $e) {
//            throw ApiException::fromException($e);
//        }
//    }
}
