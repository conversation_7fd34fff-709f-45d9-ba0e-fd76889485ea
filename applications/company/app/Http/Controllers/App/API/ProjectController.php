<?php

namespace App\Http\Controllers\App\API;

use App\Classes\Func;
use App\Classes\Log;
use App\Resources\Project\EventResource;
use App\Resources\Project\NoteResource;
use App\Resources\ProjectResource;
use App\Resources\ProjectTypeResource;
use App\Resources\UserResource;
use App\Services\CompanySettingService;
use Carbon\Carbon;
use Common\Models\MarketingType;
use Common\Models\ProjectType;
use Common\Models\ProjectTypeLegacy;
use Common\Models\ResultType;
use Ramsey\Uuid\Uuid;
use Common\Models\User;
use Core\Classes\Arr;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Scope;
use Exception;

/**
 * Class ProjectController
 *
 * @package App\Http\Controllers\App\API
 */
class ProjectController
{
    /**
     * Handle adding project
     *
     * This is a port of the submitNewProject.php legacy ajax page, modified with some improvements. Still needs work,
     * but is the first phase to fully using the api.
     *
     * Note: a lot of this code is copied from the CustomerController::handleAdd method
     *
     * @param User $user
     * @param RequestInterface $request
     * @return int
     * @throws Exception
     */
    protected function handleAdd(User $user, RequestInterface $request)
    {
        // handle project data
        $project = Entity::make([
            'property_id' => (int) $request->get('propertyID', 0),
            'customer_id' => (int) $request->get('customerID', 0),
            'description' => $request->get('projectDescription'),
            'salesperson_user_id' => Func::emptyToNull($request->get('projectSalesperson')),
            'referral_marketing_type_id' => Func::emptyToNull($request->get('referralMarketingTypeID')),
            'secondary_marketing_type_id' => Func::emptyToNull($request->get('projectSecondaryMarketingTypeID')),
            'summary' => Func::emptyToNull($request->get('projectSummary')),
            'priority' => Func::emptyToNull($request->get('projectPriority')),
            'type' => Func::emptyToNull($request->get('projectType')),
        ]);

        if (($alt_description = $request->get('projectDescriptionArray', '')) !== '') {
            $project->description = str_replace(',', ', ', $alt_description);
        }
        // if a project salesperson is defined, then we cast it's value to an integer since form data is always a string
        if ($project->salesperson_user_id !== null) {
            $project->salesperson_user_id = (int) $project->salesperson_user_id;
        }

        if ($project->priority !== null) {
            $project->priority = (int) $project->priority;
        }

        // We deal with the same thing for primary and secondary marketing type
        if ($project->referral_marketing_type_id !== null) {
            $project->referral_marketing_type_id = (int) $project->referral_marketing_type_id;
        }
        if ($project->secondary_marketing_type_id !== null) {
            $project->secondary_marketing_type_id = (int) $project->secondary_marketing_type_id;
        }

        // handle project contact data
        $project_contacts = [];
        $contact_data = json_decode($request->get('emailArray', ''), true);
        if (is_array($contact_data)) {
            foreach ($contact_data as $contact) {
                $project_contacts[] = [
                    'name' => Arr::get($contact, 'name'),
                    'phone_number' => Arr::get($contact, 'phone'),
                    'email' => Arr::get($contact, 'email')
                ];
            }
        }
        if (count($project_contacts) > 0) {
            $project->contacts = $project_contacts;
        }

        // handle project note
        $project_note = null;
        if (($note_text = trim($request->get('projectNote', ''))) !== '') {
            $project_note = Entity::make([
                'note' => $note_text,
                'tag' => 'PC',
                'is_pinned' => $request->get('projectNotePin') === '1'
            ]);
        }

        try {
            $start_at = Carbon::parse(
                $request->get('scheduledStartDate') . ' ' . $request->get('scheduledStartTimeFormatted'),
                $user->systemTimezone
            );
            $end_at = Carbon::parse(
                $request->get('scheduledEndDate') . ' ' . $request->get('scheduledEndTimeFormatted'),
                $user->systemTimezone
            );
        } catch (Exception $e) {
            throw new Exception('Unable to parse start or end date', 0, $e);
        }

        // handle appointment event
        $event = null;
        if (is_numeric($scheduled_user_id = $request->get('salesperson', ''))) {
            $event = Entity::make([
                'source' => EventResource::SOURCE_PROJECT_ADD,
                'type' => EventResource::TYPE_EVALUATION,
                'user_id' => (int) $scheduled_user_id,
                'start_at' => $start_at->toIso8601String(),
                'end_at' => $end_at->toIso8601String(),
                'is_all_day' => false,
                'description' => $request->get('scheduleDescription'),
                'is_pending' => false,
                'send_notifications' => $request->get('notifyCustomerAppointment') === '1'
            ]);
        }

        // store entities
        $acl = Auth::acl();

        // create project
        $project_id = ProjectResource::make($acl)->create($project)->run();

        // create project note if defined
        if ($project_note !== null) {
            $project_note->project_id = $project_id;
            NoteResource::make($acl)->create($project_note)->run();
        }

        if ($event !== null) {
            $event['project_id'] = $project_id;
            EventResource::make($acl)->create($event)->run();
        }

        return $project_id;
    }

    /**
     * Add project
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function add(RequestInterface $request)
    {
        $user = Auth::user();

        if (!$user->primary && !$user->projectManagement && !$user->sales) {
            return Response::json(['error' => 'Access denied'], 403);
        }

        try {
            $project_id = null;
            DB::transaction(function () use ($user, $request, &$project_id) {
                $project_id = $this->handleAdd($user, $request);
            });

            return Response::json(['projectID' => $project_id, 'result' => 'true']);
        } catch (Exception $e) {
            Log::create('property_add')->error('Unable to add project', [
                'exception' => $e
            ]);
            return Response::json(['error' => 'Unable to add project'], 500);
        }
    }

    /**
     * Get Project Info
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function info(RequestInterface $request)
    {
        $user = Auth::user();

        if (!$user->primary && !$user->projectManagement && !$user->sales) {
            return Response::json(['error' => 'Access denied'], 403);
        }

        try {
            $project_scope = Scope::make()->fields([
                'description', 'referral_marketing_type_id', 'salesperson_user_id', 'secondary_marketing_type_id',
                'summary', 'priority', 'type', 'result_type_id', 'status', 'is_financing_enabled',
                ])
            ->with([
                'salesperson_user' => [
                    'fields' => ['id', 'first_name', 'last_name', 'is_active']
                ],
                'project_result_id' => [
                    'fields' => ['id', 'name']
                ],
            ]);

            $project_id = (int) $request->get('project_id', 0);

            $acl = Auth::acl();

            /** @var Entity $project */
            $project = ProjectResource::make($acl)->entity($project_id)->scope($project_scope)->run();

            $user_scope = Scope::make()->fields(['id', 'first_name', 'last_name', 'is_active'])
                ->filter('role_sales', 'eq', true)
                ->filter('is_active', 'eq', true)
                ->filter('is_user_invited', 'eq', false);
            $users = UserResource::make($acl)->collection()->scope($user_scope)->run();

            $project_types = ProjectType::where('companyID', $user->companyID)
                ->where('status', '=', ProjectTypeResource::STATUS_ACTIVE)
                ->ordered()
                ->get();

            $types = [];
            foreach ($project_types as $type) {
                $id = strtoupper(bin2hex($type->projectTypeID));
                $types[$id] = $type->name;
            }

            $project_types_legacy = ProjectTypeLegacy::where('companyID', $user->companyID)
                ->ordered()
                ->get();

            $marketing_types = MarketingType::where('companyID', $user->companyID)->whereNull('isDeleted')
                ->orderBy('parentMarketingTypeID', 'asc')
                ->orderBy('marketingTypeName', 'asc')
                ->get();

            $marketing = [];
            foreach ($marketing_types as $type) {
                if ($type->parentMarketingTypeID !== null && isset($marketing['id_'.$type->parentMarketingTypeID])) {
                    array_push($marketing['id_'.$type->parentMarketingTypeID]['children'], $type);
                } else {
                    $marketing['id_'.$type->marketingTypeID] = [
                        'parent' => $type,
                        'children' => []
                    ];
                }
            }

            $priorities = ProjectResource::getPriorityNames();
            $project_priorities = [];

            foreach ($priorities as $key => $val) {
                $project_priorities[] = [
                    'id' => $key,
                    'priority' => $val,
                ];
            }

            $project_results = [];
            $results = ResultType::where('companyID', $user->companyID)
                ->where('type', '=', ResultType::TYPE_PROJECT)
                ->where('status', '=', ResultType::STATUS_ACTIVE)
                ->whereNull('deletedAt')
                ->orderBy('name', 'asc')
                ->get();
            foreach ($results as $result) {
                $project_results[] = [
                    'id' => strtoupper(bin2hex($result->resultTypeID)),
                    'name' => $result->name
                ];
            }

            $settingService = new CompanySettingService($user->companyID);
            $marketing_source_required = $settingService->get('marketing_source_required', false);
            $result_required = $settingService->get('result_required', false);
            $project_type_required = $settingService->get('project_type_required', false);

            return Response::json([
                'settings' => [
                    'marketing_source_required' => $marketing_source_required,
                    'result_required' => $result_required,
                    'project_type_required' => $project_type_required,
                ],
                'project_info' => $project,
                'project_types' => $types,
                'project_types_legacy' => $project_types_legacy,
                'priorities' => $project_priorities,
                'results' => $project_results,
                'salespeople' => $users,
                'marketing_types' => $marketing
            ]);
        } catch (Exception $e) {
            Log::create('project_info')->error('Unable to get project info', [
                'exception' => $e
            ]);
            return Response::json(['error' => 'Unable to get project info'], 500);
        }
    }
}
