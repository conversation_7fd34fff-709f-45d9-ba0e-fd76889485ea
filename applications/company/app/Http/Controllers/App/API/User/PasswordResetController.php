<?php

declare(strict_types=1);

namespace App\Http\Controllers\App\API\User;

use App\Classes\{Func, Security};
use App\Exceptions\{ApiException, Api\ForbiddenException, Api\NotFoundException, Api\UnprocessableEntityException};
use Carbon\Carbon;
use Common\Models\UserPasswordReset;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\RequestValidation\Classes\Validation;
use Throwable;

/**
 * Class PasswordResetController
 *
 * @package App\Http\Controllers\App\API\User
 */
class PasswordResetController
{
    /**
     * Get user password reset model by id
     *
     * @param string $id
     * @return UserPasswordReset|null
     * @throws NotFoundException
     */
    protected function getModel(string $id): ?UserPasswordReset
    {
        if (
            ($id = Func::uuidFromString($id)) === false ||
            ($password_reset = UserPasswordReset::find($id->getBytes())) === null ||
            $password_reset->createdAt->lt(Carbon::now('UTC')->subMinutes(30))
        ) {
            throw new NotFoundException(1004, 'Unable to find user password reset');
        }
        return $password_reset;
    }

    /**
     * Retrieve data by id
     *
     * @param string $id
     * @return JSONResponse
     * @throws NotFoundException
     */
    public function retrieve($id): JSONResponse
    {
        $password_reset = $this->getModel($id);
        return Response::json([
            'id' => $password_reset->getUuidKey()->toString(),
            'completed_at' => $password_reset->completedAt?->format('c'),
            'created_at' => $password_reset->createdAt->format('c')
        ]);
    }

    /**
     * Complete password reset request
     *
     * @param string $id
     * @param Validation $validation
     * @return \Core\Components\Http\Classes\Response
     * @throws ApiException
     */
    public function complete($id, Validation $validation): \Core\Components\Http\Classes\Response
    {
        try {
            $password_reset = $this->getModel($id);

            if ($password_reset->completedAt !== null) {
                throw new ForbiddenException(1009, 'Password reset already completed');
            }

            $validation->config([
                'password' => [
                    'label' => 'New Password',
                    'rules' => [
                        'trim' => true,
                        'required' => true,
                        'password' => 3
                    ]
                ]
            ]);
            $validator = $validation->run();
            if (!$validator->valid()) {
                throw UnprocessableEntityException::fromValidator($validator);
            }

            $password_reset->user->userPassword = Security::hashPassword($validator->data('password'));
            $password_reset->user->isPasswordValid = true;
            $password_reset->user->save();

            $password_reset->completedAt = Carbon::now('UTC');
            $password_reset->save();

            return Response::create('', 204);
        } catch (Throwable $e) {
            throw ApiException::fromException($e);
        }
    }
}
