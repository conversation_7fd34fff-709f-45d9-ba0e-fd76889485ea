<?php

declare(strict_types=1);

namespace App\Console\Commands\Seeders;

use Common\Seeders\CustomerSeeder;
use Core\Components\Console\Commands\BaseCommand;

/**
 * Class CustomerSeederCommand
 *
 * @package App\Console\Commands\Seeders
 */
class CustomerSeederCommand extends BaseCommand
{
    public function handle(): void
    {
        $company_id = (int) $this->console->get('company-id', 'Company ID');
        $count = (int) $this->args->get('count', 1);

        for ($i = 1; $i <= $count; $i++) {
            $customer = new CustomerSeeder();
            $customer->companyID($company_id);
            $customer->run();
            $this->console->line('Customer %d added', $customer->getPrimaryKey());
        }

        $this->console->line();
        $this->console->info('%d customer(s) added', $count);
    }
}
