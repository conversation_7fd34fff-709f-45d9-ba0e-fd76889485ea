<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Services\BrandService;
use App\Services\DomainService;
use App\Services\IntakeService;
use App\Services\StorageService;
use Core\Components\Console\Commands\BaseCommand;

/**
 * Class CacheCommand
 *
 * @package App\Console\Commands
 */
class CacheCommand extends BaseCommand
{
    /**
     * Clear all caches
     *
     * @throws \Core\Exceptions\AppException
     */
    public function clearAll(): void
    {
        (new StorageService())->clearCaches();
        $this->console->info('All caches cleared');
    }

    /**
     * Clear route cache file
     */
    public function clearRoutes(): void
    {
        (new StorageService())->clearRouteCache();
        $this->console->info('Route cache cleared');
    }

    /**
     * Clear brand cache files
     *
     * @throws \Core\Exceptions\AppException
     */
    public function clearBrands(): void
    {
        BrandService::clearCache();
        $this->console->info('Brand cache cleared');
    }

    /**
     * Clear domain cache files
     *
     * @throws \Core\Exceptions\AppException
     */
    public function clearDomains(): void
    {
        DomainService::clearCache();
        $this->console->info('Domain cache cleared');
    }

    /**
     * Clear all handlebars template cache files
     *
     * @throws \Core\Exceptions\AppException
     */
    public function clearTemplates(): void
    {
        (new StorageService())->clearHandlebarsTemplateCache();
        $this->console->info('Template caches cleared');
    }

    public function clearIntakeOptions(): void
    {
        (new IntakeService())->clearOptionsCache();
        $this->console->info('Intake options cache cleared');
    }
}
