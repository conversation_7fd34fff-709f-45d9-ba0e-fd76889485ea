<?php

$this->extend('layout.app');

$asset = $this->asset;

// jquery ui
$asset->style('//ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/themes/smoothness/jquery-ui.css');
$asset->script('//ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/jquery-ui.min.js');

$asset->script('moment.min');

// custom
$asset->style('marketing');
$asset->script('marketing');

?>
<div id="loading-image" class="loadingImage">
    <img src="<?=$this->asset->uri('image', 'ajax-loader.gif')?>" />
</div>

<div class="row inputs">
    <div class="small-10 columns">
        <h4 style="margin-top: .5rem;"> Marketing Spend for</h4>
    </div>
    <div class="medium-7 columns text-left">
        <input id="dateFilterFrom" style="margin-bottom: .2rem;" class="evaluationData date-picker date" type="text" name="dateFilterFrom" placeholder="Start Date"/>
        <input id="dateFilterTo" style="margin-bottom: .2rem;" class="evaluationData date-picker date" type="text" name="dateFilterTo" placeholder="End Date" />
    </div>
    <div class="medium-5 columns text-right">
        <p>
            <button id="addSource" class="button">Add Category</button>
        </p>
    </div>
    <div class="row">
        <div class="small-12 columns">
            <div class="small-12 columns">
                    <span id="date-range-noData" class="form-error">
                        <small>There is no data available. Add a category to view marketing data.</small>
                    </span>
            </div>
        </div>
    </div>
</div>
<div class="row" style="display: ; padding-bottom: 5rem;">
    <div id="marketingMetricsDiv" class="small-12 columns" style="margin: 0 0 .5rem 0;">
        <!-- all marketingMetric divs will appear here -->

    </div>
</div>
<!-- add source modal -->
<div id="addSourceModal" class="reveal medium" data-reveal>
    <h2 id="addSourceModalTitle" style="text-align:center;margin-bottom:0rem;">Add Category</h2>
    <div class="row align-center" style="margin-bottom: 1rem;"></div>
    <div class="row">
        <div class="medium-12 columns">
            <label>Category Name
                <input type="Text" id="addSourceName"/>
                <small class="form-error">Category name is Required</small>
            </label>
            <p class="form-error" id="sourceNameError">Category name cannot be blank</p>
            <div class="medium-12 columns text-center">
                <button class="button" id="saveAddSource">Save</button>
                <button class="button secondary" id="cancelAddSource">Cancel</button>
            </div>
        </div>
        <div class="medium-6 columns">&nbsp;</div>
    </div>
</div>
<!-- edit source modal -->
<div id="editSourceModal" class="reveal medium" data-reveal>
    <h2 id="editSourceModalTitle" style="text-align:center;margin-bottom:0rem;">Edit Category</h2>
    <div class="row align-center" style="margin-bottom: 1rem;"></div>
    <div class="row">
        <div class="medium-12 columns">
            <label>Category Name
                <input type="Text" id="editSourceName"/>
                <small class="form-error">Category name is Required</small>
            </label>
            <input id="sourceID" type="hidden" value="" />
            <p class="form-error" id="sourceNameError">Category name cannot be blank</p>
            <div class="medium-12 columns text-center">
                <button class="button" id="saveEditSource">Save</button>
                <button class="button secondary" data-close>Cancel</button>
            </div>
        </div>
        <div class="medium-6 columns">&nbsp;</div>
    </div>
</div>
<!-- add subsource modal -->
<div id="addSubSourceModal" class="reveal medium" data-reveal>
    <h2 id="subSourceModalTitle" style="text-align:center;margin-bottom:0rem;">Add Source</h2>
    <div class="row align-center" style="margin-bottom: 1rem;"></div>
    <div class="row">
        <div class="medium-12 columns">
            <label>Parent Category
                <select id="addParentTypeSelection" name="sourceNames">
                    <option value="0">Select</option>
                </select>
                <small class="form-error">Parent category is Required</small>
            </label>
            <label>Source Name
                <input type="Text" id="addSubSourceName" name="subSourceName"/>
                <small class="form-error">Source name is Required</small>
            </label>
            <p class="form-error" id="sourceNameError">Source Name Cannot be blank</p>
            <div class="medium-12 columns text-center">
                <button class="button" id="saveAddSubSource">Save</button>
                <button class="button secondary" id="cancelAddSubSource">Cancel</button>
            </div>
        </div>
        <div class="medium-6 columns">&nbsp;</div>
    </div>
</div>
<!-- edit subsource modal -->
<div id="setupSubSourceModal" class="reveal medium" data-reveal>

    <h2 id="subSourceModalTitle" style="text-align:center;margin-bottom:0rem;">Edit Source</h2>
    <div class="row">
        <div class="medium-12 columns">
            <form name="editSubSource" action="">
                <label>Parent Category
                    <select id="parentTypeSelection" name="sourceNames">
                        <option>Select</option>
                    </select>
                </label>
                <label id="sourceNameLabel">Source Name
                    <input class="evaluation-data" type="Text" id="subSourceName" name="subSourceName"   />
                </label>
                <p class="form-error" id="sourceNameError">Source name cannot be blank</p>
                <input class="evaluation-data" type="hidden" id="subsourceID" name="subsourceID" disabled />
                <input class="evaluation-data" type="hidden" id="parentTypeID" name="parentTypeID " disabled />
            </form>
            <div class="medium-12 columns text-center">
                <button class="button" id="saveSubSource">Save</button>
                <button class="button secondary" id="closeSubSource">Cancel</button>
            </div>
        </div>
        <div class="medium-6 columns">&nbsp;</div>
    </div>
</div>

<!-- Add Spend modal -->
<div id="addSpendModal" class="reveal medium" data-reveal data-close-on-esc="false" data-close-on-click="false">

    <h2 id="addSpendSourceModalTitle" style="text-align:center;margin-bottom:0rem;">Add Marketing Spend</h2>
    <div class="row align-center" style="margin-bottom: 1rem;">
        <small>Add any marketing &amp; advertising expenses here.</span></small>
    </div>
    <div class="row">
        <div class="medium-12 columns">

            <label>Category
                <select id="adParentTypeSelection" name="adSourceNames">
                    <option>Select</option>
                </select>
            </label>
            <label>Source
                <select id="adSubSourceSelection" name="adSubSourceNames">
                    <option>Select</option>
                </select>
            </label>
            <label id="lblAdPaidDate">Marketing Paid Date
                <input id="adPaidDate"  class="evaluationData date-picker date" type="text" name="adPaidDate" />
            </label>

            <label id="lblAdStartDate">Marketing Start Date <small> Required </small>
                <input id="adStartDate"  class="evaluationData date-picker date" type="text" name="adStartDate"  />
            </label>
            <p id= "adStartDateError" class="form-error">You must choose a Marketing Start Date</p>

            <label id="lblAdEndDate">Marketing End Date<small> Required</small>
                <input id="adEndDate"  class="evaluationData date-picker date" type="text" name="adEndDate"  />
            </label>
            <p id= "adEndDateError" class="form-error">You must choose a Marketing End Date</p>

            <label id="lblSpendAmount">Total Marketing Spend Amount<small> Required</small>
                <input id="adSpendAmount"  class="evaluationData" type="text" name="adSpendAmount" />
            </label>
            <p id= "adSpendAmountError" class="form-error">You must enter a Marketing Spend Amount</p>


            <input class="evaluation-data" type="hidden" id="adSubsourceID" name="subsourceID" disabled />
            <input class="evaluation-data" type="hidden" id="adParentTypeID" name="parentTypeID " disabled />
            <!-- <div class="medium-2 columns">&nbsp;</div> -->

            <div class="row">
                <div class="medium-12 columns">
                    <p class="text-center no-margin">
                        <button class="button" id="saveAdSpend">Save</button>
                        <button class="button secondary" id="closeAdSpend">Cancel</button>
                    </p>
                </div>
            </div>
        </div>
        <div class="medium-6 columns">&nbsp;</div>
    </div>
</div>


<!-- Edit Spend modal -->
<div id="editSpendModal" class="reveal medium" data-reveal data-close-on-esc="false" data-close-on-click="false">

    <h2 id="addSpendSourceModalTitle" style="text-align:center;margin-bottom:0rem;">Edit Marketing Spend</h2>
    <div class="row align-center" style="margin-bottom: 1rem;">
        <small>Make changes to the selected marketing &amp; advertising expenses here.</span></small>
    </div>
    <div class="row">
        <div class="medium-12 columns">

            <label>Category
                <select id="adEditParentTypeSelection" name="adEditSourceNames">
                    <option>Select</option>
                </select>
            </label>
            <label>Source
                <select id="adEditSubSourceSelection" name="adEditSubSourceNames">
                    <option>Select</option>
                </select>
            </label>

            <label id="lblAdEditPaidDate">Marketing Paid Date
                <input id="adEditPaidDate"  class="evaluationData date-picker date" type="text" name="adEditPaidDate" />
            </label>

            <label id="lblAdEditStartDate">Marketing Start Date<small> Required </small>
                <input id="adEditStartDate"  class="evaluationData date-picker date" type="text" name="adEditStartDate"  />
            </label>
            <p id= "adEditStartDateError" class="form-error">You must choose a Marketing Start Date</p>

            <label id="lblAdEditEndDate">Marketing End Date<small> Required</small>
                <input id="adEditEndDate"  class="evaluationData date-picker date" type="text" name="adEditEndDate"  />
            </label>
            <p id= "adEditEndDateError" class="form-error">You must choose a Marketing End Date</p>

            <label id="lblEditSpendAmount">Total Marketing Spend Amount<small> Required</small>
                <input id="adEditSpendAmount"  class="evaluationData" type="text" name="adEditSpendAmount" />
            </label>
            <p id= "adEditSpendAmountError" class="form-error">You must enter a Marketing Spend Amount</p>


            <input class="evaluation-data" type="hidden" id="adEditSubsourceID" name="adEditSubsourceID" disabled />
            <input class="evaluation-data" type="hidden" id="adEditParentTypeID" name="editParentTypeID " disabled />
            <input class="evaluation-data" type="hidden" id="adEditSpendRowID" name="editSpendRowID " disabled />

            <!-- <div class="medium-2 columns">&nbsp;</div> -->

            <div class="row">
                <div class="medium-12 columns">
                    <p class="text-center no-margin">
                        <button class="button" id="editAdSpend">Save</button>
                        <button class="button secondary" id="closeAdEditSpend">Cancel</button>
                    </p>
                </div>
            </div>
        </div>
        <div class="medium-6 columns">&nbsp;</div>
    </div>
</div>
<!-- update success modal -->
<div class="tiny reveal" id="updateMessage" data-reveal>
    <div class="row">
        <div class="medium-12 columns">
            <p class="text-center no-margin" id="updateMessageText">
                Update was successful.
                <br/>
            </p>
            <p class="text-center no-margin">
                <br/>
                <button class="button" data-close>OK</button>
            </p>
        </div>
    </div>
</div>
<!-- delete item modal -->
<div class="tiny reveal" id="deleteItemModal" data-reveal data-close-on-esc="false" data-close-on-click="false">
    <h3 class="text-center">Delete</h3>
    <div class="row">
        <div class="medium-12 columns text-center">
            <p class="text-center no-margin">
            </p>
            <input id="itemType" type="hidden" value="" />
            <input id="itemID" type="hidden" value="" />
            <br />
            <button class="button" id="yesDeleteItem">Yes</button>
            <button class="button secondary" id="cancelDeleteItem">Cancel</button>
        </div>
    </div>
</div>
