<?php

use Core\StaticAccessors\Config;

$this->extend('layout.app');

$asset = $this->asset;

$asset->style('index');

// select2
$asset->style('select2/select2');
$asset->script('select2/select2.full');
$asset->script('select2/select2.multi-checkboxes');

// jquery ui
$asset->style('//ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/themes/smoothness/jquery-ui.css');
$asset->script('//ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/jquery-ui.min.js');

// jquery Touch Punch
$asset->script('jquery.ui.touch-punch.min');

// moment
$asset->script('moment.min');

// fullcalendar
$asset->style('fullcalendar/fullcalendar.min');
$asset->style('fullcalendar/scheduler.min');
$asset->script('fullcalendar/fullcalendar.min');
$asset->script('fullcalendar/scheduler.min');

// wicked picker
$asset->style('wickedpicker');
$asset->script('wickedpicker');

// custom styles
$asset->style('custom');
$asset->style('custom-calendar');
$asset->style('resource-select');
$asset->style('modal');

$asset->script('map');
$asset->script('cookie');
$asset->script('home');
$asset->script('calendar-filter');
$asset->script('project-status');
$asset->scriptData('company_info', $this->getRaw('company'));

$asset->scriptData('icons', [
    'AVATAR' => $this->asset->uri('image', 'icons/avatar.svg')
]);

if (!App::debugEnabled()) {
    $google_script = <<<JS
(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
})(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

ga('create', 'UA-86701998-2', 'auto');
JS;
    $this->asset->rawScript($google_script);
}
// google maps
$google_api = 'https://maps.googleapis.com/maps/api/js?key='.Config::get('google.map.api_key').'&callback=initMap';
$asset->script($google_api);

$asset->scriptData('home_info', [
    'debug' => App::debugEnabled(),
    'business_hours' => $business_hours
]);

$icons = [
    'info' => $this->asset->uri('image', 'icons/info.png')
];

?>
<div id="loading-image" class="loadingImage" style="display: block"></div>
<div class="contentContainer">
    <div id="dashboard" class="dashboard shared">
        <div class="home-header">
            <h1 class="dashboard-title "><?=$this->get('user.first_name')?>'s Dashboard</h1>
            <div class="home-map-switch">
                <div class="mapSwitchContainer" style="display:none;"></div>
                <div id="mapSwitchContainer" class="mapSwitchContainer">
                    <label class="mapLabel">Map</label>
                    <div class="switch small">
                        <input checked class="switch-input" id="mapSwitch" type="checkbox" name="mapSwitch">
                        <label class="switch-paddle" for="mapSwitch">
                            <span class="switch-active" aria-hidden="true">On</span>
                            <span class="switch-inactive" aria-hidden="true">Off</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="schedule">
            <div id="calendarMap"></div>
            <div class="calendarWrapper">
                <div id="filterGroup" class="left">
                    <div class="header">
                        <span class="title">Filter
                            <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="8" title="Filter: Select the users that you want displayed on the calendar. Order: Drag the users to set their display order.">
                                <img src="<?=$icons['info']?>" />
                            </span>
                        </span>
                        <div class="group">
                            <button id="filter-clear" class="btn icon-cross"></button>
                            <button id="filter-apply" class="btn icon-checkmark"></button>
                        </div>
                    </div>
                    <div class="list"></div>
                </div>
                <div id="calendarGroup" class="left">
                    <div class="dashboard-filter-bar show-for-small-only">
                            <div class="filter">
                                <button class="btn icon-filter filter-show"></button>
                            </div>
                            <div class="todayButton">
                                <a class="button today list">Today</a>
                            </div>
                            <div class="button-group list-types">
                                <a id="listDay" class="button view view-left active">List Day</a>
                                <a id="listWeek" class="button view view-center">List Week</a>
                                <a id="listMonth" class="button view view-right">List Month</a>
                            </div>
                    </div>
                    <div class="dashboard-filter-bar table">
                        <div>
                            <div class="filter hide-for-small-only">
                                <button class="btn icon-filter filter-show"></button>
                            </div>
                            <div class="todayButton hide-for-small-only">
                                <a class="button today">Today</a>
                            </div>
                            <div class="daySelector">
                                <div class="calendar-previous">
                                    <div id="previous" class="arrow-left"></div>
                                </div>
                                <div class="calendar-title">
                                    <span id="calendarTitle" class="calendarTitle"></span>
                                </div>
                                <div class="calendar-next">
                                    <div id="next" class="arrow-right"></div>
                                </div>
                            </div>
                            <div class="button-group viewSelector hide-for-small-only">
                                <a id="dailyView" class="button view view-left active">Daily</a>
                                <a id="resourceWeekView" class="button view view-center">Weekly</a>
                                <a id="monthlyView" class="button view view-right">Monthly</a>
                            </div>
                        </div>
                    </div>
                    <div id="calendar"></div>
                </div>
            </div>
        </div>
        <div id="editEvent" class="reveal small" data-reveal data-close-on-esc="false" data-close-on-click="false">
            <h2 id="modalTitle"></h2>
            <p class="lead name"></p>
            <p class="original"></p>
            <p class="proposed no-margin"><strong></strong></p>
            <div class="row">
                <div class="medium-12 columns">
                    <label class="scheduledTitle"></label>
                </div>
                <div class="medium-5 columns" id="scheduleModalDD">
                    <select name="salesperson" class="dragAppointment">
                        <option value="">--</option>
                    </select>
                </div>
                <div class="medium-7 columns"></div>
            </div>
            <div class="row">
                <div class="medium-12 columns">
                    <label>Start Time</label>
                </div>
                <div class="medium-4 columns">
                    <input id="scheduledStartDate" style="margin-bottom: .2rem;" class="dragAppointment datepickerFrom date" type="text" name="scheduledStartDate" />
                    <small class="form-error" id="startDateErr">Please select a scheduled start date</small>
                </div>
                <div class="medium-4 columns">
                    <input id="scheduledStartTime" style="margin-bottom: .2rem;" class="dragAppointment timepicker" type="text" name="scheduledStartTime" />
                    <small class="form-error" id="startTimeErr">Please enter a scheduled start time</small>
                    <div id="scheduledStartTimeHidden" class="is-hidden"></div>
                </div>
                <div class="medium-3 columns">
                    <input type="checkbox" name="scheduledStartTimeAllDay" value="1" class="dragAppointment" /> All Day
                </div>
            </div>
            <div class="row">
                <div class="medium-12 columns">
                    <label>End Time</label>
                </div>
                <div class="medium-4 columns">
                    <input id="scheduledEndDate" class="datepickerTo date dragAppointment" type="text" name="scheduledEndDate" />
                    <small style="margin-bottom: .3rem;"class="form-error" id="endDateErr">Please select an end date</small>
                </div>
                <div class="medium-4 columns">
                    <input id="scheduledEndTime" class="timepicker dragAppointment" type="text" name="scheduledEndTime" />
                    <small style="margin-bottom: .3rem;" id="endTimeErr" class="form-error">Please enter an end time</small>

                    <div id="scheduledEndTimeHidden" class="is-hidden"></div>
                </div>
            </div>
            <div class="row">
                <div class="medium-12 columns">
                    <label>Description <small><i>(Internal)</i></small></label>
                </div>
                <div class="medium-8 columns">
                    <textarea class="internalNote" rows="3" name="scheduleDescription"></textarea>
                </div>
            </div>
            <div class="row">
                <div class="medium-12 columns">
                    <label>
                        <input type="checkbox" name="notifyCustomer" value="1" />
                        Notify Customer of Appointment Change <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="1" title="Customer will receive appointment change email after 15 minute delay (See Company Profile > Email Section to customize email content)."><img src="<?=$this->asset->uri('image', 'icons/info.png')?>" /></span>
                    </label>
                </div>
            </div>
            <div class="row">
                <div class="medium-12 columns">
                    <input type="hidden" name="tempID" value="" />
                    <input type="hidden" name="projectScheduleID" value="" />
                    <p><a style="margin-top:1rem;" id="confirmAppointmentYes" class="button">Save</a> <a  style="margin-top:1rem;" id="confirmAppointmentNo" class="button secondary">Cancel</a></p>
                </div>
            </div>
        </div>
    </div>
    <div id="projectStatus" class="project-status">
        <div id="loading-image" class="loadingImage contained projectStatus"></div>
        <div class="projectStatusTab">
            <span>Project Status Pipeline</span>
        </div>
        <div class="projectStatusContainer">
            <div class="menu-bar">
                <div class="sort">
                    <select>
                        <option value="asc">Oldest</option>
                        <option value="desc">Newest</option>
                    </select>
                </div>
                <div class="group">
                    <label>
                        <input type="checkbox"> Group By User
                    </label>
                </div>
                <div class="refresh">
                    <span></span>
                </div>
            </div>
            <div class="projectStatusTitle" status="0">
                <span class="arrow closed"></span>
                <span class="title">Lead Created</span> <span class="titleCount"></span><br />
                <span class="subTitle">Lead needs converted</span>
            </div>
            <div class="projectStatusStep" statusList="0">
            </div>
            <div class="projectStatusTitle" status="1">
                <span class="arrow closed"></span>
                <span class="title">Project Created</span> <span class="titleCount"></span><br />
                <span class="subTitle">Appointment needs scheduled</span>
            </div>
            <div class="projectStatusStep" statusList="1">
            </div>

            <div class="projectStatusTitle" status="2">
                <span class="arrow closed"></span>
                <span class="title">Sales Appointment Scheduled</span> <span class="titleCount"></span><br />
                <span class="subTitle">No action necessary</span>
            </div>
            <div class="projectStatusStep" statusList="2">
            </div>

            <div class="projectStatusTitle" status="3">
                <span class="arrow closed"></span>
                <span class="title">Sales Appointment Completed</span> <span class="titleCount"></span><br />
                <span class="subTitle">Estimate needed</span>
            </div>
            <div class="projectStatusStep" statusList="3">
            </div>

            <div class="projectStatusTitle" status="4">
                <span class="arrow closed"></span>
                <span class="title">Bid Started</span> <span class="titleCount"></span><br />
                <span class="subTitle">Bid needs finalized</span>
            </div>
            <div class="projectStatusStep" statusList="4">
            </div>

            <div class="projectStatusTitle" status="5">
                <span class="arrow closed"></span>
                <span class="title">Bid Pending</span> <span class="titleCount"></span><br />
                <span class="subTitle">Bid needs to be sent</span>
            </div>
            <div class="projectStatusStep" statusList="5">
            </div>

            <div class="projectStatusTitle" status="6">
                <span class="arrow closed"></span>
                <span class="title">Bid Sent</span> <span class="titleCount"></span><br />
                <span class="subTitle">Waiting for customer approval</span>
            </div>
            <div class="projectStatusStep" statusList="6">
            </div>

            <div class="projectStatusTitle" status="7">
                <span class="arrow closed"></span>
                <span class="title">Bid Accepted</span> <span class="titleCount"></span><br />
                <span class="subTitle">Installation needs to be scheduled</span>
            </div>
            <div class="projectStatusStep" statusList="7">
            </div>

            <div class="projectStatusTitle" status="8">
                <span class="arrow closed"></span>
                <span class="title">Bid Rejected</span> <span class="titleCount"></span><br />
                <span class="subTitle">Project needs to be cancelled or rebid</span>
            </div>
            <div class="projectStatusStep" statusList="8">
            </div>

            <div class="projectStatusTitle" status="9">
                <span class="arrow closed"></span>
                <span class="title">Installation Scheduled</span> <span class="titleCount"></span><br />
                <span class="subTitle">Mark completed after install</span>
            </div>
            <div class="projectStatusStep" statusList="9">
            </div>

            <div class="projectStatusTitle" status="10">
                <span class="arrow closed"></span>
                <span class="title">Installation Complete</span> <span class="titleCount"></span><br />
                <span class="subTitle">Warranties needed</span>
            </div>
            <div class="projectStatusStep" statusList="10">
            </div>

            <div class="projectStatusTitle" status="11">
                <span class="arrow closed"></span>
                <span class="title">Warranties Sent</span> <span class="titleCount"></span><br />
                <span class="subTitle">Project needs to be marked Complete</span>
            </div>
            <div class="projectStatusStep" statusList="11">
            </div>

            <div class="projectStatusTitle" status="12">
                <span class="arrow closed"></span>
                <span class="title">Recently Closed Project</span> <span class="titleCount"></span><br />
                <span class="subTitle">No action necessary</span>
            </div>
            <div class="projectStatusStep" statusList="12">
            </div>
        </div>
    </div>
</div>
<div id="viewEvent" class="reveal" data-reveal></div>
<div class="tiny reveal" id="noResourcesModal" data-reveal data-close-on-esc="true" data-close-on-click="true">
    <div class="row">
        <h3>No Salesmen or Installers</h3>
    </div>
    <p>Add salesmen or installers to see information on this page.</p>
    <button class="button" data-close>OK</button>
</div>
<input id="currentDate" type="hidden" name="currentDate" value="<?=$date_today?>" />
<input id="fullUserColor" type="hidden" name="fullUserColor" value="0" />
<input id="resourceClicked" type="hidden" name="resourceClicked" value="" />
<input id="calendarEditable" type="hidden" name="calendarEditable" value="<?=$calendar_editable?>" />
