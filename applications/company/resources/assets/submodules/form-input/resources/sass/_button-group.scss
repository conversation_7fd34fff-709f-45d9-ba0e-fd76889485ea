@use '~@cac-sass/base';
@use './config';

.f-field {
    .f-f-button-group {
        display: flex;
        overflow: hidden;
        background-color: base.$color-white-default;
        padding: base.unit-rem-calc(2px);
        border-radius: base.unit-rem-calc(32px);
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        &.t-disabled {}
    }
        .f-fbg-button {
            display: flex;
            flex: 1;
            justify-content: center;
            align-items: center;
            height: base.unit-rem-calc(26px);
            color: config.$button-group-text-color-default;
            font-size: base.unit-rem-calc(14px);
            font-weight: 600;
            text-align: center;
            font-family: 'Barlow', Arial, sans-serif;
            line-height: base.unit-rem-calc(24px);
            cursor: pointer;
            z-index: 1;
            padding: 0 base.unit-rem-calc(8px);
            border-radius: base.unit-rem-calc(32px);
            background-color: transparent;
            transition: all 0.25s ease-in-out;
            &.t-selected {
                color: config.$button-group-text-color-selected;
                background-color: config.$button-group-bg-color-selected;
                box-shadow: 0 0 base.unit-rem-calc(2px) rgba(0, 0, 0, 0.25);
                z-index: 2;
                &[disabled] {
                    background-color: base.$color-grey-light-3;
                    color: base.$color-white-default;
                }
            }
            &[disabled] {
                cursor: not-allowed;
                color: base.$color-grey-light-3;
            }
        }
}
