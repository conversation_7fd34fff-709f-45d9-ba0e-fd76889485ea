@use '~@cac-sass/base';
@use '~select2/src/scss/core';
@use '~select2/src/scss/theme/default/layout';

.select2-container {
    // has to be applied outside mixin since the dropdown this is contained in is bound to the body and not within
    // the field
    .select2-search__field::-webkit-input-placeholder,
    .select2-search__field::placeholder {
        color: base.$color-grey-light-1 !important;
    }
    .select2-search--dropdown {
        padding: 0;
    }
    //overrides for search field
    .select2-search__field {
        outline: none;
        height: base.unit-rem-calc(40px);
        border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-3 solid !important;
        border-top: none !important;
        border-right: none !important;
        border-left: none !important;
        padding: base.unit-rem-calc(8px) base.unit-rem-calc(12px);
        background-color: base.$color-background-form;
        color: base.$color-grey-dark-4;
        border-radius: 0;
        box-shadow: none;
        margin: 0;
        font-size: base.unit-rem-calc(14px);
        @include base.respond-to('<=small') {
            font-size: base.unit-rem-calc(16px);
        }
    }
}

%select2-dropdown {
    .select2-container {
        display: block;
        border-radius: base.$prop-border-radius;
        &.select2-container--focus {
            outline: none;
            color: base.$form-input-active-text-color;
            background-color: base.$form-input-active-bg-color;
            .select2-selection--multiple {
                border: 1px solid base.$form-input-active-border-color;
                box-shadow: 0 0 0 base.unit-rem-calc(4px) base.$form-input-active-drop-shadow-color;
            }
            &.select2-container--disabled {
                box-shadow: none;
            }
        }
        &.select2-container--disabled {
            .select2-selection--single {
                background-color: base.$color-white-default;
                border-radius: 0;
                border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                border-top: none;
                border-right: none;
                border-left: none;
                &:active {
                    border-color: base.$color-grey-light-2;
                }
                .select2-selection__rendered{
                    color: base.$color-grey-dark-1;
                    &:before {
                        display: none;
                    }
                }
            }
        }
        .select2-selection {
            outline: none;
            border-radius: base.$prop-border-radius;
            line-height: 1;
        }
        .select2-selection--single {
            height: base.$form-input-height;
            border-color: base.$color-grey-light-3;
            &:active {
                border-color: base.$color-primary-light-1 !important;
            }
            .select2-selection__rendered {
                position: relative;
                height: base.$form-input-height - base.unit-rem-calc(2px);
                padding: 0 base.unit-rem-calc(32px) 0 base.unit-rem-calc(8px);
                color: base.$color-grey-dark-4;
                font-size: base.$form-input-font-size;
                line-height: base.$form-input-height - base.unit-rem-calc(2px);
                @include base.respond-to('<small') {
                    @media (pointer:none), (pointer:coarse) {
                        font-size: base.unit-rem-calc(16px);
                    }
                }
                &:before {
                    content: '';
                    position: absolute;
                    right: 0;
                    height: base.$form-input-height - base.unit-rem-calc(2px);
                    width: base.unit-rem-calc(28px);
                    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='18' height='18'%3e%3cpath fill='none' d='M0 0h24v24H0z'/%3e%3cpath d='M12 13.172l4.95-4.95 1.414 1.414L12 16 5.636 9.636 7.05 8.222z' fill='rgba(153,174,199,1)'/%3e%3c/svg%3e");
                    background-size: base.unit-rem-calc(18px) base.unit-rem-calc(18px);
                    background-repeat: no-repeat;
                    background-position: center;
                }
            }
            .select2-selection__clear {
                display: block;
                position: absolute;
                top: 0;
                right: base.unit-rem-calc(24px);
                width: base.unit-rem-calc(24px);
                height: base.$form-input-height - base.unit-rem-calc(2px);
                text-align: center;
                line-height: base.$form-input-height - base.unit-rem-calc(2px);
                background-image: url("data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23clip0_661_2)'%3E%3Cpath d='M9 16.5C4.85775 16.5 1.5 13.1422 1.5 9C1.5 4.85775 4.85775 1.5 9 1.5C13.1422 1.5 16.5 4.85775 16.5 9C16.5 13.1422 13.1422 16.5 9 16.5ZM9 15C10.5913 15 12.1174 14.3679 13.2426 13.2426C14.3679 12.1174 15 10.5913 15 9C15 7.4087 14.3679 5.88258 13.2426 4.75736C12.1174 3.63214 10.5913 3 9 3C7.4087 3 5.88258 3.63214 4.75736 4.75736C3.63214 5.88258 3 7.4087 3 9C3 10.5913 3.63214 12.1174 4.75736 13.2426C5.88258 14.3679 7.4087 15 9 15V15ZM9 7.9395L11.121 5.81775L12.1823 6.879L10.0605 9L12.1823 11.121L11.121 12.1823L9 10.0605L6.879 12.1823L5.81775 11.121L7.9395 9L5.81775 6.879L6.879 5.81775L9 7.9395Z' fill='%238A92A9'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_661_2'%3E%3Crect width='18' height='18' fill='white'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A");
                background-position: center;
                background-size: 18px 18px;
                background-repeat: no-repeat;
                font-size: 0;
                color: transparent;
            }
            .select2-selection__placeholder {
                color: base.$form-input-default-placeholder-color;
            }
            .select2-selection__arrow {
                display: none;
            }
        }
        .select2-selection--multiple {
            display: flex;
            height: base.unit-rem-calc(32px);
            border: base.unit-rem-calc(1px) solid base.$form-input-default-border-color;
            overflow: hidden;
            &:after {
                content: '';
                width: base.unit-rem-calc(28px);
                background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='18' height='18'%3e%3cpath fill='none' d='M0 0h24v24H0z'/%3e%3cpath d='M12 13.172l4.95-4.95 1.414 1.414L12 16 5.636 9.636 7.05 8.222z' fill='rgba(153,174,199,1)'/%3e%3c/svg%3e");
                background-size: base.unit-rem-calc(18px) base.unit-rem-calc(18px);
                background-repeat: no-repeat;
                background-position: center;
            }
            .select2-selection__rendered {
                -webkit-appearance: none;
                display: block;
                min-height: base.$form-input-height;
                padding: 0;
                line-height: 1;
            }
            .select2-search--inline {
                -webkit-appearance: none;
                .select2-search__field {
                    -webkit-appearance: none;
                    height: base.$form-input-height;
                    margin: 0;
                    color: base.$color-grey-dark-4;
                    font-size: base.$form-input-font-size;
                    line-height: base.unit-rem-calc(20px);
                    padding: base.unit-rem-calc(4px) base.unit-rem-calc(8px) base.unit-rem-calc(8px);
                    @include base.respond-to('<=small') {
                        font-size: base.unit-rem-calc(16px);
                    }
                }
            }
            .select2-selection__choice {
                display: none;
                border-radius: base.$prop-border-radius;
                height: base.unit-rem-calc(22px);
                margin: base.unit-rem-calc(4px) base.unit-rem-calc(4px) 0 0;
                font-size: base.unit-rem-calc(11px);
                line-height: base.unit-rem-calc(22px);
            }
            .select2-selection__choice__remove {
                font-size: base.unit-rem-calc(12px);
                font-style: normal;
                line-height: 1;
            }
        }
    }
    .select2-tags-container {
        .tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: base.unit-rem-calc(8px);
            list-style: none;
            padding: 0;
            margin: base.unit-rem-calc(8px) 0 0;
        }
            .tag-selected {
                display: flex;
                align-items: center;
                list-style: none;
                float: left;
                padding: base.unit-rem-calc(2px) base.unit-rem-calc(8px) base.unit-rem-calc(2px) base.unit-rem-calc(12px);
                border-radius: base.unit-rem-calc(20px);
                cursor: default;
                background-color: base.$color-primary-light-1;
                color: base.$color-white-default;
                .label {
                    flex: 1;
                    line-height: 20px;
                    font-size: base.unit-rem-calc(14px);
                    padding: 0;
                    font-style: normal;
                    background-color: transparent;
                }
                .destroy {
                    flex: 0 0 auto;
                    height: base.unit-rem-calc(18px);
                    margin-left: base.unit-rem-calc(8px);
                    //color: base.$color-white-default;
                    //cursor: pointer;
                    //display: inline-block;
                    //font-weight: bold;
                    //margin-right: 2px;
                    .icon {
                        @include base.svg-icon('default-18');
                        color: base.$color-white-default;
                    }
                    &:hover {
                        text-decoration: none;
                    }
                }
            }

    }
    .select2-container--open {
        .select2-selection {
            border-radius: base.$prop-border-radius !important;
        }
        .select2-selection--single {
            border-color: base.$color-primary-light-1;
            box-shadow: 0 0 0 base.unit-rem-calc(4px) base.$color-primary-light-4;
            .select2-selection__rendered {
                color: base.$color-grey-dark-4;
            }
        }
    }
}

@mixin select2 {
    @extend %select2-dropdown;
}

// add to global form field
.f-field {
    @extend %select2-dropdown;
}
