@use '~@cac-sass/base';
@use 'date';

.flatpickr-calendar {
    .flatpickr-time {
        border-radius: 0 0 2px 2px;
        input {
            color: date.$color-dark-gray;
        }
        .numInputWrapper {
            &:hover {
                background: transparent;
            }
            .flatpickr-hour {
                &:hover {
                    background: transparent;
                    cursor: pointer;
                }
            }
            .flatpickr-minute {
                &:hover {
                    background: transparent;
                    cursor: pointer;
                }
            }
            .arrowUp {
                @include date.arrow-up;
                top: 6px;
            }
            .arrowDown {
                @include date.arrow-down;
                top: 35%;
            }
        }
        .flatpickr-am-pm {
            color: date.$color-dark-gray;
            &:hover {
                background: transparent;
            }
        }
    }
}
