'use strict';

const FormInput = require('./index');

const tinymce = require('tinymce/tinymce');
require('tinymce/themes/silver/theme');
require('tinymce/models/dom/model');
require('tinymce/icons/default/icons');
require('@cac-js/utils/wysiwyg_icons');
require('tinymce/plugins/code');
require('tinymce/plugins/image');
require('tinymce/plugins/lists');
require('tinymce/plugins/link');
const {boolean} = require("mathjs/lib/utils");

// set base url for tinyMCE editor
window.tinyMCE.baseURL = window.fx_url.assets.VENDOR + 'tinymce';

const Presets = {
    simple: {
        anchor_top: false,
        anchor_bottom: false,
        block_formats: 'Paragraph=p;Header 1=h3;Header 2=h4;Header 3=h5',
        font_size_formats: '8pt 9pt 10pt 11pt 12pt 14pt 18pt 24pt 36pt',
        paste_remove_spans: true,
        color_default_foreground: 'black',
        plugins: [
            'code', 'lists', 'link', 'image'
        ],
        image_description: false,
        paste_as_text: true,
        toolbar1: (input) => {
            let toolbar = 'bold italic underline';

            if (input.hasTextColor()) {
                toolbar = `${toolbar} | forecolor`;
            }
            if (input.hasAlignment()) {
                toolbar = `${toolbar} | alignleft aligncenter alignright alignjustify`;
            }
            toolbar = `${toolbar} | bullist numlist | image | link | code`;

            if (input.hasBlocks()) {
                toolbar = `blocks | ${toolbar}`;
            }
            if (input.hasFontSize()) {
                toolbar = `fontsize | ${toolbar}`;
            }
            if (input.hasTags()) {
                toolbar = `tags | ${toolbar}`;
            }
            if (input.hasPreview()) {
                toolbar = `${toolbar} | preview`;
            }
            if (input.hasNoImage()) {
                toolbar = toolbar.replace("image | ", "");
            }
            return toolbar;
        },
        menubar: false,
        toolbar_items_size: 'small',
        elementpath: false
    }
};

/**
 * @memberof module:FormInput
 */
class Wysiwyg extends FormInput {
    /**
     * Constructor
     *
     * @param {jQuery} element
     * @param {object} [config={}]
     */
    constructor(element, config = {}) {
        super(element);
        if (!element.is('textarea')) {
            throw new Error('Element must be a textarea');
        }
        Object.assign(this.state, {
            content: element.val() || '',
            preset: config.preset || null,
            height: config.height || 384,
            remove_empty_paragraphs: !!config.remove_empty_paragraphs,
            tags: Array.isArray(config.tags) ? config.tags : null,
            blocks: typeof config.blocks === 'boolean' ? config.blocks : false,
            font_size: typeof config.font_size === 'boolean' ? config.font_size : false,
            alignment: typeof config.alignment === 'boolean' ? config.alignment : false,
            text_color: typeof config.text_color === 'boolean' ? config.text_color : false,
            preview: typeof config.preview === 'boolean' ? config.preview : false,
            no_image: typeof config.no_image === 'boolean' ? config.no_image : false,
            tinymce_config: config.tinymce_config,
            callback_props: ['toolbar1', 'plugins'],
            editor: null,
            preview_config: typeof config.preview_config === 'object' ? config.preview_config : false,
            events: {}
        });
        element.on('change.fx', () => {
            this.setContent(element.val(), true, false);
        });
        this.state.promise = this.getEditor();
    };

    /**
     * Get name of type
     *
     * @readonly
     *
     * @returns {string}
     */
    static get name() {
        return 'wysiwyg';
    };

    /**
     * Get promise to know when input is ready to be used
     *
     * @returns {module:FormInput.Wysiwyg}
     */
    get promise() {
        return this.state.promise;
    };

    /**
     * Determines if input has tags assigned
     *
     * @returns {boolean}
     */
    hasTags() {
        return this.state.tags !== null;
    };

    /**
     * Determines if input has blocks assigned
     *
     * @returns {boolean}
     */
    hasBlocks() {
        return this.state.blocks;
    };

    /**
     * Determines if input has font size assigned
     *
     * @returns {boolean}
     */
    hasFontSize() {
        return this.state.font_size;
    };

    /**
     * Determines if input has alignment assigned
     *
     * @returns {boolean}
     */
    hasAlignment() {
        return this.state.alignment;
    };

    /**
     * Determines if input has text color assigned
     *
     * @returns {boolean}
     */
    hasTextColor() {
        return this.state.text_color;
    };

    /**
     * Determines if input has preview enabled
     *
     * @returns {boolean}
     */
    hasPreview() {
        return this.state.preview;
    };

    /**
     * Determines if input doesn't have image enabled
     *
     * @returns {boolean}
     */
    hasNoImage() {
        return this.state.no_image;
    };

    /**
     * Get configuration for underlying TinyMCE instance
     *
     * @returns {object}
     */
    getTinyMCEConfig() {
        let config = {
            target: this.elem.main[0],
            height: this.state.height,
            branding: false,
            content_css: [
                window.fx_url.assets.VENDOR + 'tinymce/skins/codepen.min.css'
            ],
            relative_urls: false,
            link_assume_external_targets: 'http',
            link_default_target: '_blank',
            link_target_list: false,
            remove_script_host: false
        };
        if (Presets[this.state.preset] !== undefined) {
            config = Object.assign({}, Presets[this.state.preset], config);
        }
        return config;
    };

    /**
     * Get and cache editor
     *
     * @returns {Promise<object>}
     */
    getEditor() {
        if (this.state.editor === null) {
            this.state.editor = new Promise((resolve) => {
                let config = this.getTinyMCEConfig();
                if (typeof this.state.tinymce_config === 'function') {
                    config = this.state.tinymce_config(config);
                }
                for (let prop of this.state.callback_props) {
                    if (typeof config[prop] !== 'function') {
                        continue;
                    }
                    config[prop] = config[prop](this);
                }
                let setup = config.setup;
                Object.assign(config, {
                    content_css: window.fx_url.assets.STYLE+'tinymce-content.css',
                    icons: 'remix-icons',
                    skin: false,
                    browser_spellcheck: true,
                    contextmenu: false,
                    min_height: 200,
                    color_map: [
                        '2DC26B', 'Green',
                        'F1C40F', 'Yellow',
                        'E03E2D', 'Red',
                        'B96AD9', 'Purple',
                        '3598DB', 'Blue',
                        '169179', 'Dark Turquoise',
                        'E67E23', 'Orange',
                        'BA372A', 'Dark Red',
                        '843FA1', 'Dark Purple',
                        '236FA1', 'Dark Blue',
                        'ECF0F1', 'Light Gray',
                        'CED4D9', 'Medium Gray',
                        '95A5A6', 'Gray',
                        '7E8C8D', 'Dark Gray',
                        '34495E', 'Navy Blue'
                    ],
                    setup: (editor) => {
                        editor.on('init', () => {
                            resolve(editor);
                        });
                        editor.on('change keyup', () => {
                            this.setContent(editor.getContent(), false, true);
                            this.emit('editor_changed');
                        });
                        if (typeof setup === 'function') {
                            setup(editor);
                        }
                        if (this.state.tags !== null) {
                            editor.ui.registry.addMenuButton('tags', {
                                text: 'Tags',
                                fetch: (callback) => {
                                    let menu_items = this.state.tags.map(item => {
                                        return {
                                            type: 'menuitem',
                                            text: item.label,
                                            onAction: () => editor.insertContent(`{${item.content}}`)
                                        };
                                    });
                                    callback(menu_items);
                                }
                            });
                        }
                        if (this.state.preview) {
                            editor.ui.registry.addButton('preview', {
                                icon: 'preview',
                                onAction: () => editor.windowManager.open({
                                    title: this.state.preview_config.title ? this.state.preview_config.title : 'Preview',
                                    size: 'large',
                                    body: {
                                        type: 'panel',
                                        items: [
                                            {
                                                type: 'htmlpanel',
                                                html: this.getPreviewContent()
                                            }
                                        ]
                                    },
                                    buttons: [
                                        {
                                            type: 'cancel',
                                            name: 'closeButton',
                                            text: '',
                                            icon: 'close'
                                        }
                                    ],
                                })
                            })
                        }
                    }
                });
                tinymce.init(config);
            });
        }
        return this.state.editor;
    };

    /**
     * Get preview content and replace with tag replacement content
     *
     * @returns {string}
     */
    getPreviewContent() {
        let content = this.state.content;

        if (this.state.preview_config.tag_replacements) {
            for (let item of this.state.preview_config.tag_replacements) {
                content = content.replaceAll(`{${item.content}}`, item.replacement);
            }
        }
        if (this.state.preview_config.prepend) {
            content = this.state.preview_config.prepend + content;
        }
        if (this.state.preview_config.append) {
            content = content + this.state.preview_config.append;
        }
        return content;
    };

    /**
     * Set content for editor
     *
     * @param {string} content
     */
    setEditorContent(content) {
        this.getEditor().then(editor => editor.setContent(content));
    };

    setPreviewTitle(title) {
        this.state.preview_config.title = title;
    };

    setPreviewSubject(subject) {
        this.state.preview_config.title = title;
    };

    /**
     * Set content for field
     *
     * @param {string} content
     * @param {boolean} [update_editor=true] - Determines if editor is updated with new content
     * @param {boolean} [update_elem=true] - Determines if textarea is updated with new content
     */
    setContent(content, update_editor = true, update_elem = true) {
        if (typeof content !== 'string') {
            content = '';
        }
        content = content.trim();
        if (this.state.remove_empty_paragraphs) {
            content = content.replace(/^(\s*<p>\s*(&nbsp;)*\s*<\/p>\s*)+/ig, '');
            content = content.replace(/(\s*<p>\s*(&nbsp;)*\s*<\/p>\s*)+$/ig, '');
        }
        this.state.content = content;
        if (update_editor) {
            this.setEditorContent(content);
        }
        if (update_elem) {
            this.elem.main.val(content).trigger('wysiwyg:change');
        }
    };

    /**
     * Get content
     *
     * @returns {string|null}
     */
    get content() {
        return this.state.content;
    };

    /**
     * Resize editor
     *
     * @param {number} size
     */
    resize(size) {
        this.getEditor().then(editor => $(editor.editorContainer).height(size));
    }

    /**
     * Destroy field
     */
    destroy() {
        if (this.state.pickr !== null) {
            this.getEditor().then(editor => editor.remove());
        }
        this.elem.main.off('change.fx');
        super.destroy();
    };

    on(event, closure) {
        if (typeof this.state.events[event] === 'undefined') {
            this.state.events[event] = [];
        }
        this.state.events[event].push(closure);
        return this;
    };

    emit(event, ...args) {
        if (typeof this.state.events[event] === 'undefined') {
            return;
        }
        this.state.events[event].forEach((closure) => {
            closure(...args);
        });
    };
}

module.exports = Wysiwyg;
