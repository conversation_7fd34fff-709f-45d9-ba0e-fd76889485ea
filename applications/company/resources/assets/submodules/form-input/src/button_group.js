'use strict';

const FormInput = require('./index');

const input_tpl = require('@cas-form-input-tpl/button_group.hbs');

/**
 * @typedef {object} OptionConfig
 * @property {string} label
 * @property {jQuery} src_elem - source select option element
 * @property {jQuery} elem - button element
 * @property {boolean} selected
 */

/**
 * @memberof module:FormInput
 */
class ButtonGroup extends FormInput {
    /**
     * Constructor
     *
     * @param {jQuery} element
     */
    constructor(element) {
        super(element);
        if (!element.is('select')) {
            throw new Error('Button group only works with select elements');
        }
        let options = [],
            selected_idx = null;
        element.children('option').each((i, option) => {
            let elem = $(option),
                selected = elem.is(':selected'),
                disabled = element.is(':disabled');

            options[i] = {
                label: elem.text(),
                src_elem: elem,
                selected,
                disabled
            };
            elem.data('fx_idx', i);
            if (selected) {
                selected_idx = i;
            }
        });
        Object.assign(this.state, {
            options,
            selected_idx
        });
        this.elem.root = $(input_tpl({
            disabled: element.is(':disabled'),
            options
        }));
        element.after(this.elem.root);

        const that = this;
        this.elem.root.fxClickWatcher('button', function (e) {
            e.preventDefault();
            that.setSelected(parseInt($(this).data('idx')));
            return false;
        });

        // listen for external changes to main element. user must emit this for us to update the selected value
        this.elem.main.fxEvent('button-group:change', () => {
            let selected = this.elem.main.find('option:selected');
            if (selected.length === 0) {
                return;
            }
            this.setSelected(selected.data('fx_idx'), false);
        });

        this.hideMainElem();
    };

    /**
     * Get name of type
     *
     * @readonly
     *
     * @returns {string}
     */
    static get name() {
        return 'button-group';
    };

    /**
     * Get and configure option by index
     *
     * @param {number} idx
     * @returns {OptionConfig}
     */
    getOption(idx) {
        let option = this.state.options[idx];
        if (option === undefined) {
            throw new Error(`Unable to find option with idx: ${idx}`);
        }
        if (option.elem === undefined) {
            option.elem = this.elem.root.fxFind('button', {idx});
        }
        return option;
    };

    /**
     * Set/remove selected option
     *
     * If null is passed, selection will be cleared.
     *
     * @param {?number} idx
     * @param {boolean} [update_input=true] - determines if update the main input element with new value
     */
    setSelected(idx, update_input = true) {
        if (idx === this.state.selected_idx) {
            return;
        }
        if (this.state.selected_idx !== null) {
            let selected_option = this.getOption(this.state.selected_idx);
            selected_option.selected = false;
            if (update_input) {
                selected_option.src_elem.prop('selected', false).removeAttr('selected');
            }
            selected_option.elem.removeClass('t-selected');
        }
        if (idx !== null) {
            let option = this.getOption(idx);
            option.selected = true;
            if (update_input) {
                option.src_elem.prop('selected', true).attr('selected', 'selected');
            }
            option.elem.addClass('t-selected');
        }
        this.state.selected_idx = idx;
        if (update_input) {
            this.elem.main.trigger('change');
        }
    };

    /**
     * Destroy instance, reset UI back to default and unbind events
     */
    destroy() {
        this.elem.root.remove();
        this.elem.main.fxEventDestroy('button-group:change');
        super.destroy();
    };
}

module.exports = ButtonGroup;
