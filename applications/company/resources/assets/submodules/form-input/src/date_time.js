'use strict';

const DateInput = require('./date');

/**
 * @memberof module:FormInput
 */
class DateTime extends DateInput {

    /**
     * Get name of type
     *
     * @readonly
     *
     * @returns {string}
     */
    static get name() {
        return 'datetime';
    };

    /**
     * Get current date and time to the nearest 5 minutes
     *
     * Needed for the minuteIncrement config that's set to 5 minutes by default
     *
     * @returns {Date}
     */
    getCurrentDate() {
        let milliseconds = 300000; // 5 minutes in milliseconds
        return new Date(Math.round(new Date().getTime() / milliseconds) * milliseconds);
    };

    /**
     * Get picker config
     *
     * @returns {object}
     */
    getPickrConfig() {
        return Object.assign(super.getPickrConfig(), {
            enableTime: true,
            altFormat: 'F j, Y h:i K',
            dateFormat: 'Z',
            defaultHour: '8',
            defaultMinute: '0'
        });
    };
}

module.exports = DateTime;
