/**
 * @module PanelStack/Panel
 */

'use strict';

const Controller = require('./controller');

/**
 * @alias module:PanelStack/Panel
 */
class Panel {
    /**
     * Constructor
     */
    constructor() {
        this.elem = {};
        this.state = {
            booted: false,
            controller: null,
            active: false
        };
    };

    /**
     * Get controller instance
     *
     * @readonly
     *
     * @returns {(null|Controller)}
     */
    get controller() {
        return this.state.controller;
    };

    /**
     * Set controller instance
     *
     * @param {Controller} controller
     */
    setController(controller) {
        this.state.controller = controller;
    };

    /**
     * Set if panel is active or not
     *
     * @param {boolean} bool
     */
    setActive(bool) {
        this.state.active = bool;
    };

    /**
     * Determine if panel is active
     *
     * @returns {boolean}
     */
    isActive() {
        return this.state.active;
    };

    /**
     * Load panel
     *
     * Prep panel for becoming active, called before the panel is put into view by the controller
     */
    load() {};

    /**
     * Unload panel
     *
     * Called after panel is remove from view, used to unload any necessary data or reset things to a default state
     */
    unload() {};

    /**
     * Determines if panel is booted
     *
     * @returns {boolean}
     */
    isBooted() {
        return this.state.booted;
    };

    /**
     * Boot panel
     *
     * Set it's root element
     *
     * @param {Object} root - jQuery object
     */
    boot(root) {
        this.state.booted = true;
        this.elem.root = root;
    };

    /**
     * Render panel
     */
    render() {};

    /**
     * Delete panel
     *
     * Called when controller is being destroyed
     */
    delete() {};
}

module.exports = Panel;
