'use strict';

import {Action} from '@ca-package/notification';

import icon_tpl from '@cas-notification-toast-tpl/action/icon.hbs';

/**
 * @memberof module:NotificationToast/Message/Action
 */
export class IconAction extends Action {
    /**
     * Constructor
     *
     * @param {string} icon
     * @param {function} handler
     */
    constructor(icon, handler) {
        super(handler);
        Object.assign(this.state, {
            icon
        });
    };

    /**
     * Render action
     *
     * @returns {string}
     */
    render() {
        return icon_tpl({
            id: this.id,
            icon: this.state.icon
        });
    };
}
