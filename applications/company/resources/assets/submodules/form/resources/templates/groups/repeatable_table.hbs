<table class="c-f--g-repeatable-table" data-js="repeatable-table">
    <thead>
        <tr class="c-f--grt--row">
            <th class="c-f--grt--r-content">
                <table class="c-f--grt--content-row">
                    <tr class="c-f--grt--cr-wrapper t-header">
                        {{#each headers}}
                        <th class="c-f--grt--crw-data t-size-{{size}}"><div class="f-field"><label class="f-f-label">{{header}}{{#if required}}<span class="f-fl-required t-show" data-js="required">*</span>{{/if}}{{#if tooltip}}<span data-tooltip data-type="info">{{tooltip}}</span>{{/if}}</label></div></th>
                        {{/each}}
                    </tr>
                </table>
            </th>
            <th class="c-f--grt--r-actions">&nbsp;</th>
        </tr>
    </thead>
    <tbody data-js="rows">
    {{#each rows}}
        {{{this}}}
    {{/each}}
    </tbody>
    <tfoot>
        <tr>
            <td class="c-f--grt--footer" colspan="{{column_count}}">
                <a class="c-f--grt--f-row-add" data-js="row-add">
                    <div data-text>Add Row</div>
                    <svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg>
                </a>
            </td>
        </tr>
    </tfoot>
</table>
