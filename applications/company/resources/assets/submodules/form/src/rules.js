/**
 * @module Form/Rules
 */

'use strict';

import {Types} from './group/rule/constants';
import {PresentationEngine} from './rules/presentation_engine';
import {ConditionParser} from './rules/condition_parser';
import {GroupContext} from './rules/group_context';

export function engineFactory(type) {
    switch (type) {
        case Types.PRESENTATION:
            return new PresentationEngine();
        default:
            throw new Error(`Invalid type: ${type}`);
    }
}

export function groupContextFactory(group, data = {}) {
    return new GroupContext(group, data);
}

export function conditionParserFactory() {
    return new ConditionParser();
}

/**
 * @memberof module:Form
 */
export class Rules {
    /**
     * Constructor
     */
    constructor(form) {
        this.state = {
            form,
            engine_factory: engineFactory,
            group_context_factory: groupContextFactory,
            condition_parser_factory: conditionParserFactory,
            engines: new Map,
            condition_parser: null
        };
    };

    /**
     * Set the factory function to get engine instance by type
     *
     * Allows for overriding of engine classes.
     *
     * @param {function} factory
     * @returns {module:Form.Rules}
     */
    setEngineFactory(factory) {
        this.state.engine_factory = factory;
        return this;
    };

    /**
     * Set the factory function to get group context instance by type
     *
     * Allows for overriding of context classes.
     *
     * @param {function} factory
     * @returns {module:Form.Rules}
     */
    setGroupContextFactory(factory) {
        this.state.group_context_factory = factory;
        return this;
    };

    /**
     * Set the factory function to get condition parser instance
     *
     * @param {function} factory
     * @returns {module:Form.Rules}
     */
    setConditionParserFactory(factory) {
        this.state.condition_parser_factory = factory;
        return this;
    };

    /**
     * Get condition parser instance
     *
     * @readonly
     *
     * @returns {module:Form/Rules.ConditionParser}
     */
    get condition_parser() {
        if (this.state.condition_parser === null) {
            this.state.condition_parser = this.state.condition_parser_factory();
        }
        return this.state.condition_parser;
    };

    /**
     * Get engine by type
     *
     * @param {number} type
     * @returns {module:RulesEngine.Engine}
     */
    engine(type) {
        let engine = this.state.engines.get(type);
        if (engine === undefined) {
            engine = this.state.engine_factory(type);
            this.state.engines.set(type, engine);
        }
        return engine;
    };

    /**
     * Get new group context instance
     *
     * @param {module:Form.Group} group
     * @param {object} [data={}]
     */
    newGroupContext(group, data = {}) {
        return this.state.group_context_factory(group);
    };
}
