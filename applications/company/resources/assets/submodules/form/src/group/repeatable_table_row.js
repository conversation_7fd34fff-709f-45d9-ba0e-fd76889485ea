'use strict';

import {Group} from './base';
import {Types as LayoutTypes} from './layout/constants';

import repeatable_table_row_tpl from '@cas-form-tpl/groups/repeatable-table/row.hbs';

/**
 * @memberof module:Form/Group
 */
export class RepeatableTableRow extends Group {
    /**
     * Constructor
     *
     * @param {module:Form/Group.RepeatableTable} controller
     * @param {module:Form.Controller} form
     * @param {Object} data
     * @param {Object} entry
     * @param {module:Form.Group|null} parent
     */
    constructor(controller, form, data, entry, parent) {
        super(form, data, entry, parent);
        this.state.controller = controller;
    };

    /**
     * Initialize row
     */
    init() {
        super.init();
        for (let field of this.state.fields) {
            field.init();
        }
    };

    /**
     * Boot repeatable table row
     */
    async boot() {
        if (!this.isRendered()) {
            throw new Error('Cannot boot that which has not been rendered');
        }
        this.elem.root = this.state.controller.elem.tbody.fxChildren('row', {index: this.state.index});
        for (let field of this.state.fields) {
            await field.boot();
        }

        this.state.booted = true;
    };

    /**
     * Render repeatable table row
     *
     * @returns {string}
     */
    render() {
        super.render();
        const layout = this.state.layouts.get(LayoutTypes.INPUT_TABLE_ROW);
        if (layout === undefined) {
            throw new Error('Layout not found');
        }
        return repeatable_table_row_tpl({
            index: this.state.index,
            content: layout.render()
        });
    };
}
