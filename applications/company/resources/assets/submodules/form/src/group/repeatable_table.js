'use strict';

import $ from 'jquery';
import cloneDeep from 'lodash/cloneDeep';

import {Group} from './base';
import {RepeatableTableRow} from './repeatable_table_row';
import {Types as LayoutTypes} from './layout/constants';

const Tooltip = require('@ca-submodule/tooltip');

import default_tpl from '@cas-form-tpl/groups/default.hbs';
import repeatable_table_tpl from '@cas-form-tpl/groups/repeatable_table.hbs';

/**
 * Controller for repeatable tables, manages individual rows (groups)
 *
 * @memberof module:Form/Group
 */
export class RepeatableTable extends Group {
    /**
     * Constructor
     *
     * @param {module:Form.Controller} form
     * @param {Object} data - Group data
     * @param {Array} entries - Entry data for each row
     * @param {module:Form.Group|null} [parent=null]
     */
    constructor(form, data, entries, parent = null) {
        super(form, data, {}, parent);
        if (!this.state.layouts.has(LayoutTypes.INPUT_TABLE_ROW)) {
            throw new Error('Repeatable table group must have a table row layout');
        }
        let headers = [];
        for (let field of this.state.fields) {
            let label = field.label;
            if (typeof label !== 'string') {
                continue;
            }
            headers.push(label);
        }
        Object.assign(this.state, {
            headers,
            row_index: 0,
            rows: new Map,
            entries
        });
    };

    /**
     * Get instances of this group
     *
     * Since this is a container instance, we return all the row groups we made.
     *
     * @returns {module:Form/Group.RepeatableTableRow[]}
     */
    get instances() {
        return Array.from(this.state.rows.values()).map(({row}) => row);
    };

    /**
     * Add row
     *
     * @param {Object} entry - Entry data
     * @param {?number} entry.index - Entry index
     */
    async addRow(entry = {}) {
        if (typeof entry.index !== 'number') {
            entry.index = this.state.row_index;
        }
        let row = new RepeatableTableRow(this, this.form, cloneDeep(this.state.data), entry, this.parent);
        let elem = $(row.render());
        this.state.rows.set(row.index, {
            row: row,
            elem: elem
        });
        this.state.row_index = row.index + 1;
        this.elem.tbody.append(elem);
        await row.boot();
    };

    /**
     * Remove row with specified index
     *
     * @param {number} index - Index of row
     */
    removeRow(index) {
        let row = this.state.rows.get(index);
        if (row === undefined) {
            return;
        }
        row.row.delete();
        this.state.rows.delete(index);
        this.form.runRules().catch(e => console.error(e));
        // if all rows are removed, we add back in a new one
        if (this.state.rows.size === 0) {
            this.addRow().catch(e => console.error(e));
        }
    };

    /**
     * Determines if any repeatable table components have incomplete uploads
     *
     * @returns {boolean}
     */
    hasIncompleteUploads() {
        for (let row of this.state.rows.values()) {
            if (!row.row.hasIncompleteUploads())  {
                continue;
            }
            return true;
        }
        return false;
    };

    /**
     * Determines if repeatable table components are valid
     *
     * @returns {boolean}
     */
    isValid() {
        let valid = true;
        for (let row of this.state.rows.values()) {
            if (row.row.isValid()) {
                continue;
            }
            valid = false;
        }
        return valid;
    };

    /**
     * Get entry data
     *
     * @returns {object}
     */
    getEntry() {
        let items = [];
        for (let {row} of this.state.rows.values()) {
            items.push(row.getEntryItem());
        }
        return {
            item_group_id: this.state.id,
            items
        };
    };

    /**
     * Run rules for all rows of this repeated group
     *
     * Does not call parent since this is just a container group and doesn't have rules to run.
     *
     * @returns {Promise<void>}
     */
    async runRules() {
        // if any rows are available, run their groups first before running the parent
        if (this.state.rows.size > 0) {
            for (let {row} of this.state.rows.values()) {
                await row.runRules();
            }
        }
    };

    /**
     * Initialize group
     */
    init() {
        for (let {row} of this.state.rows.values()) {
            if (!row.isBooted()) {
                continue;
            }
            row.init();
        }
    };

    /**
     * Boot repeatable table
     */
    async boot() {
        await super.boot();
        Tooltip.initAll(this.elem.root);
        this.elem.table = this.elem.root.fxChildren('repeatable-table');
        if (this.elem.table.length !== 1) {
            throw new Error('Unable to find table');
        }
        this.elem.tbody = this.elem.table.fxChildren('rows');
        if (this.elem.tbody.length !== 1) {
            throw new Error('Unable to find table body');
        }

        // add rows from entries
        if (this.state.entries.length > 0) {
            for (let entry of this.state.entries) {
                await this.addRow(entry);
            }
        } else {
            await this.addRow();
        }

        this.elem.row_add = this.elem.table.fxFind('row-add');
        if (this.elem.row_add.length !== 1) {
            throw new Error('Unable to find row add button');
        }
        this.elem.row_add.fxClick((e) => {
            e.preventDefault();
            this.addRow().catch(e => console.error(e));
            return false;
        });

        const that = this;
        this.elem.tbody.fxClickWatcher('action-delete', function (e) {
            e.preventDefault();
            let $this = $(this);
            let index = $this.fxParents('row').data('index');
            that.removeRow(index);
            return false;
        });

        this.state.booted = true;
    };

    /**
     * Render repeatable table
     *
     * @returns {string}
     */
    render() {
        super.render();
        const layout = this.state.layouts.get(LayoutTypes.INPUT_TABLE_ROW);
        let headers = layout.headers();
        return default_tpl({
            path: this.path(),
            name: this.state.name,
            content: repeatable_table_tpl({
                headers: headers,
                column_count: headers.length + 1,
                rows: []
            })
        });
    };

    /**
     * Clear all rows, reset index, and re-add initial row
     */
    clear() {
        for (let index of this.state.rows.keys()) {
            this.removeRow(index);
        }
        this.state.row_index = 0;
        // add back initial row
        this.addRow().catch(e => console.error(e));
    };

    /**
     * Destroy repeatable table, destroying all rows and remove it from the DOM
     */
    destroy() {
        this.state.rows.forEach((row) => {
            row.row.destroy();
        });
        this.elem.root.remove();
        this.state.events.emit('destroyed');
    };
}
