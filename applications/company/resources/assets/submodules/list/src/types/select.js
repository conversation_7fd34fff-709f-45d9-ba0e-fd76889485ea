'use strict';

const isString = require('lodash/isString');

const Base = require('./base');

const item_tpl = require('@cas-list-tpl/types/select.hbs');

/**
 * @memberof module:List/Types
 */
class Select extends Base {
    /**
     * Constructor
     *
     * @param {object} [config={}]
     */
    constructor(config = {}) {
        super(Object.assign({
            toggle_handler: null,
            select_handler: null,
            deselect_handler: null
        }, config));
    };

    /**
     * Hook to change configuration data of item
     *
     * @param {object} data
     * @param {object} item
     * @returns {object}
     */
    handleItemConfig(data, item) {
        item = super.handleItemConfig(data, item);
        item.selected = false;
        if (isString(data.icon)) {
            item.icon = data.icon;
        }
        return item;
    };

    /**
     * Render item
     *
     * @protected
     *
     * @param {ItemConfig} item
     * @returns {(undefined|string)}
     */
    renderItem(item) {
        let tpl_data = {
            id: item.id,
            icon: item.icon,
            selected: item.selected
        };
        switch (this.state.config.display_type) {
            case Base.DisplayType.DEFAULT:
                tpl_data.label = item.label;
                break;
            case Base.DisplayType.MEDIA:
                tpl_data.media = {
                    thumbnail_url: item.thumbnail_url,
                    name: item.name
                };
                break;
        }
        item.elem = {
            root: $(item_tpl(tpl_data))
        };
        item.elem.label = item.elem.root.fxFind('label');
        this.elem.items.append(item.elem.root);
    };

    /**
     * Toggle item status
     *
     * @param {number} id
     * @param {(boolean|null)} [selected=null]
     */
    toggleItem(id, selected = null) {
        let item = this.getItem(id);
        if (selected === null) {
            selected = !item.selected;
        } else if (selected === item.selected) {
            return;
        }
        if (this.isBooted()) {
            item.elem.root[selected ? 'addClass' : 'removeClass']('t-selected');
        }
        item.selected = selected;
        if (typeof this.state.config.toggle_handler === 'function') {
            this.state.config.toggle_handler(item);
        }
        let handler = this.state.config[selected ? 'select_handler' : 'deselect_handler'];
        if (typeof handler === 'function') {
            handler(item);
        }
    };

    /**
     * Select item
     *
     * @param {number} id
     */
    selectItem(id) {
        this.toggleItem(id, true);
    };

    /**
     * Deselect item
     *
     * @param {number} id
     */
    deselectItem(id) {
        this.toggleItem(id, false);
    };

    /**
     * Boot list
     *
     * @param {jQuery} container
     */
    boot(container) {
        super.boot(container);

        const that = this;
        this.elem.items.fxClickWatcher('item', function (e) {
            e.preventDefault();
            that.toggleItem(parseInt($(this).data('id')));
            return false;
        });
    };
}

module.exports = Select;
