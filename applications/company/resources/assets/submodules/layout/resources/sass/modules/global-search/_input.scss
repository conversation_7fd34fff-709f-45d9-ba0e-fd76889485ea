@use '~@cac-sass/base';

.m-layout-search {
    $search-height: base.unit-rem-calc(40px);
    height: $search-height;
    &.t-clear {
        display: flex;
        align-items: center;
        .c-ls-input-wrapper {
            width: unset;
            flex: 1;
        }
    }
    &.t-filled {
        .c-ls-clear {
            width: base.unit-rem-calc(64px);
        }
    }
    .c-ls-input-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
    }
        .c-lsiw-icon {
            @include base.svg-icon('default-18');
            position: absolute;
            top: base.unit-rem-calc(10px);
            left: base.unit-rem-calc(12px);
            color: base.$color-grey-light-1;
        }
        .c-lsiw-input {
            width: 100%;
            height: 100%;
            padding: 0 base.unit-rem-calc(8px) 0 base.unit-rem-calc(34px);
            border: base.unit-rem-calc(1px) base.$color-grey-light-3 solid;
            color: base.$color-grey-dark-4;
            font-size: base.unit-rem-calc(16px);
            line-height: $search-height;
            box-shadow: none;
            border-radius: base.unit-rem-calc(20px);
            background-color: base.$color-white-default;
            -webkit-appearance: none;
            outline-offset: 0;
            outline: transparent;
            &:focus {
                box-shadow: 0 0 0 base.unit-rem-calc(4px) base.$color-primary-light-4;
                border: base.unit-rem-calc(1px) base.$color-primary-light-1 solid;
            }
            &::placeholder {
                color: base.$form-input-default-placeholder-color;
            }
            &::-webkit-search-decoration,
            &::-webkit-search-cancel-button,
            &::-webkit-search-results-button,
            &::-webkit-search-results-decoration {
                -webkit-appearance: none;
            }
            &::-ms-clear,
            &::-ms-reveal {
                display: none;
            }
        }
    .c-ls-clear {
        flex: 0 0 auto;
        width: 0;
        overflow: hidden;
        transition: width 0.3s;
    }
        .c-lsc-text {
            display: block;
            padding: base.unit-rem-calc(10px);
            color: base.$color-red-default;
            font-size: base.unit-rem-calc(14px);
            line-height: 1;
            text-align: center;
            @include base.respond-to('hover'){
                &:hover {
                    color: base.$color-red-light-1;
                }
            }
        }
}
