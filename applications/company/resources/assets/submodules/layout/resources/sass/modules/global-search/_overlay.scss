@use '~@cac-sass/base';
@use 'input';

body.t-global-search-open {
    overflow: hidden;
}

.m-global-search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    display: none;
    width: 100%;
    height: 100%;
    padding: base.unit-rem-calc(72px) 0 0;
    background-color: base.$color-white-default;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.4s;
    &.t-show {
        display: block;
    }
    &.t-open {
        opacity: 1;
    }
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(56px) 0 0;
    }
    @include base.respond-to('height<500px') {
        padding: 0;
    }
    .c-gso-close {
        position: absolute;
        display: block;
        top: base.unit-rem-calc(40px);
        right: base.unit-rem-calc(40px);
        padding: base.unit-rem-calc(3px);
        outline: 0 transparent solid;
        border-radius: base.unit-rem-calc(32px);
        color: base.$color-grey-light-2;
        transition: all 0.3s cubic-bezier(0.45, 0.05, 0.55, 0.95);
        @include base.respond-to('hover') {
            &:hover {
                scale: 1.2;
                outline: base.unit-rem-calc(1.5px) base.$color-grey-light-4 solid;
            }
            &:active {
                color: base.$color-grey-dark-1;
            }
        }
        @include base.respond-to('height<500px', '>=small') {
            top: base.unit-rem-calc(20px);
            right: base.unit-rem-calc(24px);
        }
        @include base.respond-to('<small') {
            top: base.unit-rem-calc(24px);
            right: base.unit-rem-calc(24px);
        }
        @include base.respond-to('height<500px', '<small') {
            top: base.unit-rem-calc(20px);
            right: base.unit-rem-calc(24px);
        }
    }
        .c-gsoc-icon {
            display: block;
            @include base.svg-icon('default-24');
        }
    .c-gso-inner {
        display: flex;
        flex-direction: column;
        height: 100%;
        background: base.$color-background-form;
        @include base.respond-to('<large') {
            width: 100%;
        }
    }
        .c-gsoi-header {
            flex: 0 0 auto;
            border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
            background: base.$color-white-default;
        }
            .c-gsoih-input-wrapper {
                width: base.unit-rem-calc(640px);
                margin: 0 auto;
                padding: base.unit-rem-calc(40px);
                @include base.respond-to('<medium') {
                    width: 100%;
                }
                @include base.respond-to('<small') {
                    width: 100%;
                    padding: base.unit-rem-calc(24px) base.unit-rem-calc(16px);
                }
                @include base.respond-to('height<500px') {
                    padding: base.unit-rem-calc(16px);
                }
                //@include base.respond-to('height<500px', '<small') {
                //    padding: base.unit-rem-calc(16px);
                //}
            }
        .c-gsoi-message {
            flex: 1;
            display: none;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            padding: base.unit-rem-calc(20px);
            &.t-show {
                display: flex;
            }
            &.t-empty {
                .c-gsoim-image {
                    color: base.$color-primary-light-1;
                }
            }
            &.t-in-progress {
                .c-gsoim-image {
                    color: base.$color-yellow-light-1;
                }
            }
            &.t-no-results {
                .c-gsoim-image {
                    color: base.$color-red-light-1;
                }
            }
        }
            .c-gsoim-image {
                @include base.svg-icon-base($absolute: false);
                width: base.unit-rem-calc(269px);
                height: base.unit-rem-calc(100px);
                @include base.respond-to('height<500px') {
                    width: base.unit-rem-calc(161px);
                    height: base.unit-rem-calc(60px);
                }
            }
            .c-gsoim-text {
                width: base.unit-rem-calc(500px);
                margin-top: base.unit-rem-calc(60px);
                color: base.$color-grey-dark-1;
                font-size: base.unit-rem-calc(16px);
                text-align: center;
                @include base.respond-to('<small') {
                    width: 100%;
                    margin-top: base.unit-rem-calc(48px);
                }
                @include base.respond-to('height<500px') {
                    margin-top: base.unit-rem-calc(24px);
                }
            }
        .c-gsoi-results {
            display: none;
            flex: 1;
            overflow: auto;
            padding-top: base.unit-rem-calc(24px);
            &.t-show {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
        }
            .c-gsoir-collection {
                padding: base.unit-rem-calc(24px) base.unit-rem-calc(16px) base.unit-rem-calc(40px);
                @include base.respond-to('<medium') {
                    padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px) base.unit-rem-calc(40px);
                }
            }
    @import 'overlay/result_collection';
}
