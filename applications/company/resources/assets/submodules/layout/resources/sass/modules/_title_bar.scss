@use '~@cac-sass/base';
@use '../config';

.m-layout-title-bar {
    display: none;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: config.$title-bar-height;
    background-color: base.$color-white-default;
    border-bottom: 1px solid base.$color-background;
    &.t-show {
        display: flex;
    }
    @include base.respond-to('<=xlarge') {
        display: none;
    }
    .c-ltb-title {
        margin: 0;
        padding: 0;
        @include base.typo-nav($size: 14px, $line-height: 1);
        color: base.$color-grey-dark-2;
        text-transform: uppercase;
    }
}
