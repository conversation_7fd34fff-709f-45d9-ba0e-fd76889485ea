'use strict';

const Api = require('./api');

/**
 * Network handler for module
 *
 * @memberof module:Drawing
 * @mixes Event
 */
class Network {
    constructor() {
        this.state = {
            online: null,
            poll_interval: 30000, // 30 secs
            timer: null
        };
        this.setOnline(!!window.navigator.onLine, true, false);
        window.addEventListener('online', () => {
            this.setOnline(true, true);
        });
        window.addEventListener('offline', () => {
            this.setOnline(false,  true);
        });
    };

    /**
     * Get online status
     *
     * @readonly
     *
     * @returns {boolean}
     */
    get online() {
        return this.state.online;
    };

    /**
     * Set online status
     *
     * If external is true, then we stop any polling since this means network status of the computer changed which
     * triggers events we can tie into.
     *
     * @param {boolean} online
     * @param {boolean} [external=false] - determines if status came from external source like navigate.onLine or online/offline events
     * @param {boolean} [events=true]
     */
    setOnline(online, external = false, events = true) {
        if (online === this.state.online) {
            return;
        }
        this.state.online = online;
        if (online) {
            this.startPolling();
        } else if (external) {
            this.stopPolling();
        }
        if (events) {
            this.emit('status-changed', {
                online: online
            });
        }
    };

    /**
     * Check connectivity by pinging server
     *
     * @returns {Promise<boolean>}
     */
    async checkConnectivity() {
        return await Api.Request.ping();
    };

    /**
     * Start timer to poll connectivity
     */
    startPolling() {
        if (this.state.timer !== null) {
            return;
        }
        this.state.timer = setInterval(() => {
            this.checkConnectivity().then((online) => {
                this.setOnline(online);
            });
        }, this.state.poll_interval);
    };

    /**
     * Stop polling timer
     */
    stopPolling() {
        if (this.state.timer === null) {
            return;
        }
        clearTimeout(this.state.timer);
        this.state.timer = null;
    };
}

require('@cac-js/mixins/event')(Network);

module.exports = new Network;
