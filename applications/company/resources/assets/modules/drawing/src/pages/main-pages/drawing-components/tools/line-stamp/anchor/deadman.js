'use strict';

const Anchor = require('../anchor');
const Node = require('../../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineStamp/Anchor
 */
class Deadman extends Anchor {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Deadman.Type.DEADMAN);
        Object.assign(this.state, {
            label: 'Deadman Anchor',
            icon: 'module--drawing--tools--deadman',
            node_type: Node.Entity.Type.DEADMAN
        });
    };
}

module.exports = Deadman;
