'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class Downspout extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Downspout.Type.DOWNSPOUT);
        Object.assign(this.state, {
            label: 'Downspout',
            icon: 'module--drawing--tools--downspout',
            node_type: Node.Entity.Type.DOWNSPOUT
        });
    };
}

module.exports = Downspout;
