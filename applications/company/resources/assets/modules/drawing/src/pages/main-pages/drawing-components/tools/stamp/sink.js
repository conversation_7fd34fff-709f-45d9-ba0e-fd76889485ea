'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class Sink extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Sink.Type.SINK);
        Object.assign(this.state, {
            label: 'Sink',
            icon: 'module--drawing--tools--sink',
            node_type: Node.Entity.Type.SINK
        });
    };
}

module.exports = Sink;
