'use strict';

const Base = require('./base');

const rectangle_size_tpl = require('@cam-drawing-tpl/pages/main-pages/drawing-components/poppers/rectangle_size.hbs');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Poppers
 */
class RectangleSize extends Base {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.PopperManager} manager
     */
    constructor(manager) {
        super(manager);
        Object.assign(this.state, {
            type: Base.Type.RECTANGLE_SIZE,
            name: 'rectangle-size',
            popper_options: {
                placement: 'top'
            }
        });
    };

    /**
     * Update size value in popper
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Size} size
     */
    updateSize(size) {
        let paper = this.state.node.paper,
            width = paper.getUnitsFromPixels(size.width),
            height = paper.getUnitsFromPixels(size.height);
        this.elem.size.text(`${width} x ${height}`);
    };

    /**
     * Destroy popper
     */
    destroy() {
        this.state.node.off('update', this.state.node_update);
        super.destroy();
    };

    /**
     * Boot popper
     *
     * @param {jQuery} root
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Rectangle} node
     * @param {string} handle
     */
    boot(root, node, handle) {
        super.boot(root);
        this.elem.size = this.elem.root.fxFind('size');

        Object.assign(this.state, {
            node,
            handle,
            node_update: () => {
                let rect = node.getRectangle();
                this.setPosition(rect.getPoint(handle));
                this.updateSize(node.size);
            }
        });
        node.on('update', this.state.node_update);
        this.updateSize(node.size);
        let rect = node.getRectangle();
        this.initPopper(rect.getPoint(handle));
    };

    /**
     * Get content for popper
     */
    getContent() {
        return rectangle_size_tpl();
    };
}

module.exports = RectangleSize;
