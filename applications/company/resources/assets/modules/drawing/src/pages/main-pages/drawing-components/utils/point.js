'use strict';

const paper = require('paper');

const Number = require('@cac-js/utils/number');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Utils
 */
class Point {
    /**
     * Convert serialized point into paper.js point
     *
     * @param {(null|paper.Point|[number, number], {x: number, y: number})} data
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}
     */
    static of(data) {
        if (data === null || data instanceof paper.Point) {
            return data;
        }
        return new paper.Point(data);
    };

    /**
     * Serialize point to array
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @param {boolean|number} round - if rounding is enabled, can specify number of decimals or use default
     * @returns {[number, number]}
     */
    static toArray(point, round = false) {
        let value = [point.x, point.y];
        if (round) {
            value = value.map(point => Number.round(point, typeof round === 'number' ? round : 4))
        }
        return value;
    };

    /**
     * Convert point into string representation
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @returns {string}
     */
    static toString(point) {
        return `[${point.x.toString()},${point.y.toString()}]`;
    };
}

module.exports = Point;
