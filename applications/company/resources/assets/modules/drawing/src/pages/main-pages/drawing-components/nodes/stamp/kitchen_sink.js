'use strict';

const Paper = require('../../paper');
const Stamp = require('../stamp');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Stamp
 */
class KitchenSink extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: KitchenSink.Entity.Type.KITCHEN_SINK,
            tool_type: Tool.Type.KITCHEN_SINK,
            layer: Paper.Layer.BACKGROUND_STAMP,
            handles: Object.assign(this.properties.handles, {
                scale: true,
                rotate: true
            }),
            sizes: {
                template: paper.getSizeFromUnits({inches: 36}, {inches: 24}),
                min: paper.getSizeFromUnits({feet: 1})
            }
        });
    };

    /**
     * Get default state for node
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            size: this.paper.getSizeFromUnits({inches: 36}, {inches: 24})
        });
        return state;
    };

    /**
     * Create paper.js path template to clone from
     *
     * @returns {*}
     */
    getPathTemplate() {
        let y_pos = this.paper.getPixelsFromUnit({inches: 2});
        let group = new this.paper.ps.Group({
            position: this.state.point,
            parent: this.getLayer(this.selected ? 'selected' : 'default'),
            visible: this.valid,
            applyMatrix: false
        });
        new this.paper.ps.Path.Rectangle({
            point: [0, 0],
            size: this.properties.sizes.template,
            fillColor: '#fff',
            strokeWidth: 1,
            strokeColor: this.selected ? this.properties.selected_color : '#000',
            parent: group
        });
        let width_offset = 8,
            height_offset = 12,
            width = this.properties.sizes.template.width / 2 - width_offset + 2,
            height = this.properties.sizes.template.height - height_offset;
        new this.paper.ps.Path.Rectangle({
            point: [width_offset / 2, height_offset / 2],
            radius: 6,
            size: [width, height],
            fillColor: '#fff',
            strokeWidth: 1,
            strokeColor: this.selected ? this.properties.selected_color : '#000',
            parent: group
        });
        new this.paper.ps.Path.Rectangle({
            point: [width + width_offset + width_offset / 2 - 4, height_offset / 2],
            radius: 6,
            size: [width, height],
            fillColor: '#fff',
            strokeWidth: 1,
            strokeColor: this.selected ? this.properties.selected_color : '#000',
            parent: group
        });

        return new this.paper.ps.Group({
            children: [group]
        });
    };
}

module.exports = KitchenSink;
