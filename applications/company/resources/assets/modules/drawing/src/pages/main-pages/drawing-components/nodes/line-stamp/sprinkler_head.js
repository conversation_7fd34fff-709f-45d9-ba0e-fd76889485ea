'use strict';

const LineStamp = require('../line_stamp');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/LineStamp
 */
class SprinklerHead extends LineStamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: SprinklerHead.Entity.Type.SPRINKLER_HEAD,
            tool_type: Tool.Type.SPRINKLER_HEAD,
            fill_color: '#fff',
            text_color: '#000',
            tracked: false,
            side_snap: false,
            sizes: {
                template: paper.getSizeFromUnits({inches: 6})
            }
        });
        this.properties.dimension_stem_offset = this.properties.sizes.template.height / 2;
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            number: null
        });
        return state;
    };

    /**
     * Get number
     *
     * @readonly
     *
     * @returns {number}
     */
    get number() {
        return this.state.number;
    };

    /**
     * Get node list
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes/LineStamp.SprinklerHead}
     */
    get node_list() {
        let list = this.paper.storage.get('sprinkler_head');
        if (list === undefined) {
            list = [];
            this.paper.storage.set('sprinkler_head', list);
        }
        return list;
    };

    /**
     * Get next number
     *
     * @returns {number}
     */
    getNextNumber() {
        let last_number = this.paper.storage.get('last_sprinkler_head_number');
        if (last_number === undefined) {
            last_number = 0;
            for (let node of this.node_list) {
                if (node.number <= last_number) {
                    continue;
                }
                last_number = node.number;
            }
        }
        let next_number = last_number + 1;
        this.paper.storage.set('last_sprinkler_head_number', next_number);
        return next_number;
    };

    /**
     * Draw node and register
     */
    draw() {
        if (this.state.number === null) {
            this.state.number = this.getNextNumber();
        } else if (!this.properties.tracked) {
            let last_number = this.paper.storage.get('last_sprinkler_head_number');
            if (last_number === undefined || this.state.number > last_number) {
                this.paper.storage.set('last_sprinkler_head_number', this.state.number);
            }
        }

        if (!this.properties.tracked) {
            this.node_list.push(this);
            this.properties.tracked = true;
        }

        let width = this.properties.sizes.template.width,
            half_width = width / 2,
            circle = new this.paper.ps.Path.Circle({
                radius: half_width,
                strokeWidth: 2,
                strokeColor: this.selected ? this.properties.selected_color : '#000',
                fillColor: this.properties.fill_color,
                insert: false
            }),
            number = new this.paper.ps.PointText({
                position: [0, half_width * 0.3],
                content: `S${this.state.number.toString()}`,
                justification: 'center',
                fillColor: this.properties.text_color,
                fontFamily: this.properties.font_family,
                fontWeight: 'bold',
                fontSize: half_width,
                insert: false
            }),
            item = new this.paper.ps.Group({
                children: [circle, number],
                position: this.state.position,
                applyMatrix: false,
                parent: this.getLayer(this.selected ? 'selected' : 'default')
            });
        this.setPaperItem('main', item, true);
    };

    /**
     * Get data for storage in entity
     *
     * @returns {object}
     */
    entityData() {
        let data = super.entityData();
        data.number = this.number;
        return data;
    };

    /**
     * Delete node
     *
     * @param {boolean} [notify=true]
     */
    delete(notify = true) {
        super.delete(notify);
        let list = this.node_list,
            index = list.indexOf(this);
        if (index !== -1) {
            list.splice(index, 1);
        }
        this.paper.storage.delete('last_sprinkler_head_number');
    };
}

module.exports = SprinklerHead;
