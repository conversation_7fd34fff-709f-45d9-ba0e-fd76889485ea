/**
 * @module Drawing/Pages/MainPages/DrawingComponents/Nodes/Line
 */

'use strict';

const Base = require('./base');
const Point = require('../utils/point');
const Paper = require('../paper');

const Styles = {
    SOLID: 1,
    DASHED: 2
};

/**
 * Base class for lines
 *
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes
 * @abstract
 */
class Line extends Base {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            layer: Paper.Layer.LINE_SEGMENT,
            selected_endpoints: [],
            min_length: 6,
            transfer_data: true
        });
    };

    /**
     * Get default state for line node
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        Object.assign(state, {
            from: null,
            to: null,
            width: 2,
            half_width: null,
            color: '#000000',
            style: Styles.SOLID,
            vectors: {}
        });
        return state;
    };

    /**
     * Load data into state
     *
     * @param {object} state
     * @param {object} data
     * @returns {object}
     */
    loadData(state, data) {
        state = super.loadData(state, data);
        state.from = Point.of(state.from);
        state.to = Point.of(state.to);
        return state;
    };

    /**
     * Get available styles
     *
     * @readonly
     *
     * @returns {object}
     */
    static get Style() {
        return Styles;
    };

    /**
     * Get from point
     *
     * @readonly
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}
     */
    get from() {
        return this.state.from;
    };

    /**
     * Get to point
     *
     * @readonly
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}
     */
    get to() {
        return this.state.to;
    };

    /**
     * Get opposite endpoint name
     *
     * @param {string} endpoint
     * @returns {string}
     */
    oppositeEndpoint(endpoint) {
        return endpoint === 'from' ? 'to' : 'from';
    };

    /**
     * Get vector of line based on endpoint
     *
     * @param {string} start_endpoint
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Vector}
     */
    getVector(start_endpoint) {
        if (this.state.vectors[start_endpoint] === undefined) {
            let end_endpoint = this.oppositeEndpoint(start_endpoint);
            this.state.vectors[start_endpoint] = this.state[end_endpoint].subtract(this.state[start_endpoint]);
        }
        return this.state.vectors[start_endpoint];
    };

    /**
     * Get vector based on from and to points
     *
     * @readonly
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Vector}
     */
    get vector() {
        return this.getVector('from');
    };

    /**
     * Get width
     *
     * @readonly
     *
     * @returns {number}
     */
    get width() {
        return this.state.width;
    };

    /**
     * Get half of width and cache
     *
     * Used in many calculations so was better to just cache
     *
     * @returns {number}
     */
    get half_width() {
        if (this.state.half_width === null) {
            this.state.half_width = this.state.width / 2;
        }
        return this.state.half_width;
    };

    /**
     * Get color
     *
     * @readonly
     *
     * @returns {string}
     */
    get color() {
        return this.state.color;
    };

    /**
     * Get style
     *
     * @readonly
     *
     * @returns {number}
     */
    get style() {
        return this.state.style;
    };

    /**
     * Select specified endpoint
     *
     * @param {string} endpoint
     * @param {boolean} [render=true]
     */
    selectEndpoint(endpoint, render = true) {
        this.properties.selected_endpoints.push(endpoint);
        if (render) {
            this.drawEndpoints();
        }
    };

    /**
     * Deselect specified endpoint
     *
     * @param {string} endpoint
     * @param {boolean} [render=true]
     */
    deselectEndpoint(endpoint, render = true) {
        let index = this.properties.selected_endpoints.indexOf(endpoint);
        if (index === -1) {
            return;
        }
        this.properties.selected_endpoints.splice(index, 1);
        if (render) {
            this.drawEndpoints();
        }
    };

    /**
     * Handle a zoom change by rendering any endpoints again
     */
    onZoomChange() {
        this.drawEndpoints();
    };

    /**
     * Select node
     */
    select() {
        this.paperSubscribe('zoom-changed', 'onZoomChange');
        super.select();
    };

    /**
     * Deselect node
     */
    deselect() {
        if (this.deleted) {
            return;
        }
        this.paperUnsubscribe('zoom-changed');
        this.properties.selected_endpoints = [];
        super.deselect();
    };

    /**
     * Validate node
     *
     * @returns {boolean}
     */
    validate() {
        return this.state.to.subtract(this.state.from).length > this.properties.min_length;
    };

    /**
     * Draw any selected endpoints
     */
    drawEndpoints() {
        let endpoints = null;
        if (this.properties.selected_endpoints.length > 0) {
            let zoom_factor = 1 / this.paper.view.zoom,
                inner_radius = 3 * zoom_factor,
                outer_radius = 10 * zoom_factor,
                layer = this.getLayer('overlay');
            endpoints = [];
            for (let endpoint of this.properties.selected_endpoints) {
                let inner = new this.paper.ps.Path.Circle({
                        center: [0, 0],
                        radius: inner_radius,
                        fillColor: '#ff6000'
                    }),
                    outer = new this.paper.ps.Path.Circle({
                        center: [0, 0],
                        radius: outer_radius,
                        fillColor: 'rgba(0, 0, 0, 0.5)'
                    });
                endpoints.push(new this.paper.ps.Group({
                    children: [outer, inner],
                    position: this[endpoint],
                    parent: layer
                }));
            }
        }
        this.setPaperItem('endpoints', endpoints);
    };

    /**
     * Draw node
     */
    draw() {
        this.drawEndpoints();
    };

    /**
     * Normalize properties of node after update
     */
    postUpdate(data) {
        if (data.from || data.to) {
            this.state.vectors = {};
        }
        if (data.width) {
            this.state.half_width = null;
        }
        this.state.from = this.state.from.round();
        this.state.to = this.state.to.round();
    };
}

module.exports = Line;
