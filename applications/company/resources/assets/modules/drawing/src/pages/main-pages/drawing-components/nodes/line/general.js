'use strict';

const Line = require('../line');
const Tool = require('../../tools/base');
const Point = require('../../utils/point');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line
 */
class General extends Line {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: General.Entity.Type.LINE,
            tool_type: Tool.Type.LINE
        });
    };

    /**
     * Perform hit test of node only to determine if point exists within
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @returns {(null|object)}
     */
    hitTest(point) {
        let result = this.getPaperItem('main').hitTest(point, {
            tolerance: this.paper.hit_test_tolerance,
            fill: true,
            stroke: true,
            segments: true,
            ends: true
        });
        if (result === null) {
            return null;
        }
        switch (result.type) {
            case 'stroke':
                return {
                    type: 'line'
                };
            case 'segment':
                return {
                    type: 'endpoint',
                    endpoint: result.segment.index === 0 ? 'from' : 'to'
                };
        }
    };

    /**
     * Draw node and register
     */
    draw() {
        let item = new this.paper.ps.Path.Line({
            from: this.state.from,
            to: this.state.to,
            strokeWidth: this.state.width,
            strokeColor: this.selected ? this.properties.selected_color : this.state.color,
            parent: this.getLayer(this.selected ? 'selected' : 'default'),
            visible: this.valid
        });
        if (this.state.style === Line.Style.DASHED) {
            item.dashArray = [8, 2];
        }
        this.setPaperItem('main', item, true);

        super.draw();
    };

    /**
     * Get data for storage in entity
     *
     * @returns {object}
     */
    entityData() {
        return {
            from: Point.toArray(this.from),
            to: Point.toArray(this.to),
            width: this.width,
            color: this.color,
            style: this.style
        };
    };
}

module.exports = General;
