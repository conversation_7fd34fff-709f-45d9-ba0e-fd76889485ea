'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class FloorCrack extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, FloorCrack.Type.FLOOR_CRACK);
        Object.assign(this.state, {
            label: 'Floor Crack',
            icon: 'module--drawing--tools--floor-crack',
            node_type: Node.Entity.Type.FLOOR_CRACK
        });
    };
}

module.exports = FloorCrack;
