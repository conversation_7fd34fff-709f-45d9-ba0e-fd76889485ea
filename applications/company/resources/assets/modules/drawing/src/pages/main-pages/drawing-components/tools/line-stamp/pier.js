/**
 * @module Drawing/Pages/MainPages/DrawingComponents/Tools/LineStamp/Pier
 */

'use strict';

const LineStamp = require('../line_stamp');
const LineStampNode = require('../../nodes/line_stamp');
const ConfigPanel = require('../../config-panels/base');

/**
 * Base pier line stamp tool class
 *
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineStamp
 * @abstract
 */
class Pier extends LineStamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     * @param {number} type
     */
    constructor(controller, type) {
        super(controller, type);
        Object.assign(this.state, {
            config_panel_type: ConfigPanel.Type.PIER
        });
    };

    /**
     * Get side of line point resides
     *
     * @param {?module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Line} line
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @returns {number}
     */
    getSide(line, point) {
        return LineStampNode.Side.NONE;
    };
}

module.exports = Pier;
