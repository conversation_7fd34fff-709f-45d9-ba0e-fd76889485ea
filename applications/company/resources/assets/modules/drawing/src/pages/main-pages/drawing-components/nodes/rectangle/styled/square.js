'use strict';

const Styled = require('../styled');
const Tool = require('../../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Rectangle/Styled
 */
class Square extends Styled {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Square.Entity.Type.SQUARE,
            tool_type: Tool.Type.SQUARE,
            handles: Object.assign(this.properties.handles, {
                scale: true,
                rotate: true
            }),
            proportional_scale: true
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            color: 'rgba(55,55,55,0.1)',
            border_color: '#4f4f4f',
            border_width: 2
        });
        return state;
    };
}

module.exports = Square;
