'use strict';

const Base = require('./base');
const FormInput = require('@ca-submodule/form-input');
const NumberSpinner = require('@ca-submodule/form-input/src/number_spinner');
FormInput.use(NumberSpinner);

const layout_tpl = require('@cam-drawing-tpl/pages/main-pages/drawing-components/config-panels/elevation_reading.hbs');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/ConfigPanels
 */
class ElevationReading extends Base {
    constructor() {
        super('elevation_reading');
    };

    /**
     * Populate config panel
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.ElevationReading} node
     */
    populate(node) {
        super.populate(node);
        this.elem.elevation.val(node.elevation).trigger('change');

    };

    /**
     * Set elevation reading
     */
    setElevationReading(value) {
        let data = {
            elevation: parseFloat(value).toFixed(1),
            color: value >= 0 ? '#000000' : '#ff0000'
        };
        this.state.tool.modifyNode((node) => node.update(data));
    };

    /**
     * Boot config panel
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.elevation = this.elem.content.fxFind('elevation');

        FormInput.init(this.elem.elevation, {
            type: NumberSpinner.Type.INCHES,
            step_increment: .1,
            decimals: 1
        });

        const that = this;
        this.elem.elevation.on('change.fx', function () {
            let $this = $(this);
            that.setElevationReading($this.val());
        });
    };

    /**
     * Get config panel content
     */
    getContent() {
        return layout_tpl();
    };
}

module.exports = ElevationReading;
