/**
 * @module Drawing/Pages/MainPages/DrawingComponents/Tools/Rectangle
 */

'use strict';

const Event = require('../event');
const Base = require('./base');
const Node = require('../nodes/base');
const ConfigPanel = require('../config-panels/base');
const Angle = require('../utils/angle');

/**
 * Freeform line class
 *
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools
 * @abstract
 */
class FreeformLine extends Base {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Base.Type.FREEFORM_LINE);
        Object.assign(this.state, {
            accept_events: [Event.Type.DOWN, Event.Type.MOVE, Event.Type.UP, Event.Type.TAP],
            label: 'Freedraw',
            icon: 'module--drawing--tools--freeform-line',
            node_type: Node.Entity.Type.FREEFORM_LINE,
            config_panel_type: ConfigPanel.Type.LINE_STYLE_COLOR_WIDTH,
            pivot_point: null,
            last_vector: null,
            start_rotation: null,
            angle_change: 0,
            duplicate_offset: [50, 50]
        });
    };

    /**
     * Set mode
     *
     * Enable/disable down hit test on drawing interaction class
     *
     * @param {number} mode
     */
    setMode(mode) {
        switch (mode) {
            case Base.Mode.CREATE:
            case Base.Mode.UPDATE:
                this.controller.interaction.down_hit_test = false;
                break;
            case Base.Mode.IDLE:
                this.controller.interaction.down_hit_test = true;
                break;
        }
        super.setMode(mode);
    };

    /**
     * Deselect tool
     *
     * If in create mode and node is available and not committed, we will run commit for both node and paper.
     */
    deselect() {
        if (this.mode === Base.Mode.CREATE && this.node !== null && !this.node.committed) {
            this.node.commit();
            this.controller.paper.commit(); // commit paper as well since the main commit call has already happened
        }
        super.deselect();
    };

    /**
     * Create freeform line node
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Base}
     */
    createNode() {
        return this.controller.paper.createNode(this.state.node_type, {
            paths: [],
            position: null,
            rotation: 0
        });
    };

    /**
     * Handle tap event
     *
     * If user taps/clicks anywhere, we will clear out the current tool
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onInteractTap(event) {
        if (this.mode === Base.Mode.CREATE) {
            this.node.removeLastPath(); // clean up tap start
            this.node.commit();
        }
        this.clearTool();
        this.setAsPrevTool();
    };

    /**
     * Handle down event
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onInteractDown(event) {
        switch (this.mode) {
            case Base.Mode.CREATE:
                if (this.node === null) {
                    this.node = this.createNode();
                }
                this.node.newPath();
                break;
            case Base.Mode.UPDATE:
                let result = this.node.hitTest(event.point);
                if (result !== null && result.type === 'handle' && result.handle === 'rotate') {
                    this.state.action = Base.Action.ROTATE;
                    this.state.pivot_point = this.node.position;
                    this.state.last_vector = event.point.subtract(this.state.pivot_point);
                    this.state.start_rotation = this.node.rotation;
                    return;
                }
                this.state.action = Base.Action.MOVE;
                this.setMovePoints({
                    position: this.node.position
                });
                break;
        }
    };

    /**
     * Handle move event
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onInteractMove(event) {
        if (this.mode === Base.Mode.CREATE) {
            this.node.addPoint(event.point);
        } else if (this.mode === Base.Mode.UPDATE) {
            if (this.action === Base.Action.MOVE) {
                let delta = this.move(event);
                if (delta === false) {
                    return;
                }
                this.node.move(this.getMovePoints(), delta);
            } else if (this.action === Base.Action.ROTATE) {
                let vector = event.point.subtract(this.state.pivot_point),
                    delta = vector.angle - this.state.last_vector.angle;
                this.state.angle_change += delta;
                let angle = Angle.add(this.state.start_rotation, this.state.angle_change);
                let sign = Math.sign(angle);
                angle = Math.round(Math.abs(angle) / 15) * 15;
                if (sign !== 0) {
                    angle *= sign;
                }
                this.node.update({
                    rotation: angle
                });
                this.state.last_vector = vector;
            }
        }
    };

    /**
     * Handle up event
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onInteractUp(event) {
        switch (this.mode) {
            case Base.Mode.CREATE:
                this.node.addPoint(event.point);
                break;
            case Base.Mode.UPDATE:
                switch (this.action) {
                    case Base.Action.MOVE:
                        this.node.commit();
                        this.clearMoveData();
                        break;
                    case Base.Action.ROTATE:
                        this.state.pivot_point = null;
                        this.state.last_vector = null;
                        this.state.start_rotation = null;
                        this.state.angle_change = 0;
                        this.node.commit();
                        break;
                }
                this.state.action = null;
                break;
        }
    };

    /**
     * Handle node duplication
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.FreeformLine} node
     */
    onDuplicateNode(node) {
        node.update({
            position: node.position.add(this.state.duplicate_offset)
        });
        super.onDuplicateNode(node);
    };
}

module.exports = FreeformLine;
