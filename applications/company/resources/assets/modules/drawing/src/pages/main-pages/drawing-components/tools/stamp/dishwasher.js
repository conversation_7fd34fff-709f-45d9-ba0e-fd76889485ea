'use strict';

const Stamp = require('../stamp');
const Node = require('../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Stamp
 */
class Dishwasher extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Dishwasher.Type.DISHWASHER);
        Object.assign(this.state, {
            label: 'Dishwasher',
            icon: 'module--drawing--tools--dishwasher',
            node_type: Node.Entity.Type.DISHWASHER
        });
    };
}

module.exports = Dishwasher;
