'use strict';

const Line = require('../line');
const Node = require('../../nodes/base');
const ConfigPanel = require('../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Line
 */
class Arrow extends Line {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Arrow.Type.ARROW);
        Object.assign(this.state, {
            label: 'Arrow',
            icon: 'module--drawing--tools--arrow',
            node_type: Node.Entity.Type.ARROW,
            config_panel_type: ConfigPanel.Type.LINE_STYLE_ONLY
        });
    };

    /**
     * <PERSON>le create start action
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onCreateStart(event) {
        this.node = this.controller.paper.createNode(this.state.node_type, {
            from: event.down_point,
            to: event.point
        });
    };

    /**
     * <PERSON>le create tween action
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onCreateTween(event) {
        this.node.update({
            to: event.point
        });
    };

    /**
     * Handle create end action
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onCreateEnd(event) {
        this.node.update({
            to: event.point
        });
        this.node.commit();
        this.clearTool();
        super.onCreateEnd(event);
    };

    /**
     * Handle move start action
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onMoveStart(event) {
        this.setMovePoints({
            from: this.node.from,
            to: this.node.to
        });
    };

    /**
     * Handle move tween action
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onMoveTween(event) {
        let delta = this.move(event);
        if (delta === false) {
            return;
        }
        this.node.move(this.getMovePoints(), delta);
    };

    /**
     * Handle move end action
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onMoveEnd(event) {
        this.node.commit();
        this.clearMoveData();
    };

    /**
     * Handle edit start action
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onEditStart(event) {
        this.setMovePoints({
            endpoint: this.node[this.state.endpoint]
        });
    };

    /**
     * Handle edit tween action
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onEditTween(event) {
        if (this.move(event) === false) {
            return;
        }
        this.node.update({
            [this.state.endpoint]: this.getMovePoint('endpoint')
        });
    };

    /**
     * Handle edit end action
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Event} event
     */
    onEditEnd(event) {
        this.node.commit();
        this.clearMoveData();
    };
}

module.exports = Arrow;
