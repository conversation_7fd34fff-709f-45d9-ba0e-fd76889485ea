'use strict';

const Stamp = require('../stamp');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Stamp
 */
class WaterHeater extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: WaterHeater.Entity.Type.WATER_HEATER,
            tool_type: Tool.Type.WATER_HEATER,
            handles: Object.assign(this.properties.handles, {
                scale: true
            }),
            sizes: {
                template: new paper.ps.Size([50, 50]),
                min: paper.getSizeFromUnits({feet: 1})
            }
        });
    };

    /**
     * Get default state for node
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            size: this.paper.getSizeFromUnits({feet: 2})
        });
        return state;
    };

    /**
     * Create paper.js path template to clone from
     *
     * @returns {*}
     */
    getPathTemplate() {
        let circle = new this.paper.ps.Path.Rectangle({
                size: this.properties.sizes.template,
                radius: this.properties.sizes.template.width / 2,
                strokeWidth: 1,
                strokeColor: '#000',
                fillColor: '#fff',
                insert: false
            });
        // 'W' shape as path so it will scale properly
        let letter_w = new this.paper.ps.Path({
            fillColor: [0, 0, 0],
            insert: false,
            segments: [
                [15.764,32.828],[13.762,32.828],[10,18],[12.024,18],[[13.784,25.502],[0,0],[0.44,1.848]],
                [[14.884,30.628],[-0.264,-1.43],[0,0]],[[14.928,30.628],[0,0],[0.242,-1.474]],
                [[16.138,25.48],[-0.506,1.914],[0,0]],[18.118,18],[20.12,18],[[21.924,25.524],[0,0],[0.418,1.76]],
                [[22.958,30.606],[-0.22,-1.562],[0,0]],[[23.002,30.606],[0,0],[0.308,-1.628]],
                [[24.19,25.48],[-0.462,1.848],[0,0]],[26.148,18],[28.106,18],[23.904,32.828],[21.902,32.828],
                [[20.032,25.106],[0,0],[-0.462,-1.892]],[[19.064,20.266],[0.198,1.496],[0,0]],
                [[19.02,20.266],[0,0],[-0.264,1.474]],[[17.876,25.106],[0.55,-1.914],[0,0]]
            ],
            closed: true
        });
        // 'H' shape as path so it will scale properly
        let letter_h = new this.paper.ps.Path({
            fillColor: [0, 0, 0],
            insert: false,
            segments: [
                [29.954,18],[31.868,18],[31.868,24.204],[39.04,24.204],[39.04,18],[40.976,18],[40.976,32.828],
                [39.04,32.828],[39.04,25.876],[31.868,25.876],[31.868,32.828],[29.954,32.828]
            ],
            closed: true
        });
        return new this.paper.ps.Group({
            children: [circle, letter_w, letter_h]
        });
    };
}

module.exports = WaterHeater;
