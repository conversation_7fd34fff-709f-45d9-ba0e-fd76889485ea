'use strict';

const Pier = require('../pier');
const Node = require('../../../nodes/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineStamp/Pier
 */
class Exterior extends Pier {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Exterior.Type.EXTERIOR_PIER);
        Object.assign(this.state, {
            label: 'Exterior Pier',
            icon: 'module--drawing--tools--exterior-pier',
            node_type: Node.Entity.Type.EXTERIOR_PIER
        });
    };
}

module.exports = Exterior;
