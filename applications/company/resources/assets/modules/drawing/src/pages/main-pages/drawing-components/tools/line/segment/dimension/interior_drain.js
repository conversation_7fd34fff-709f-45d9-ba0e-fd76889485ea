'use strict';

const Dimension = require('../dimension');
const Node = require('../../../../nodes/base');
const ConfigPanel = require('../../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Line/Segment/Dimension
 */
class InteriorDrain extends Dimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, InteriorDrain.Type.INTERIOR_DRAIN);
        Object.assign(this.state, {
            label: 'Interior Drain',
            icon: 'module--drawing--tools--interior-drain',
            node_type: Node.Entity.Type.INTERIOR_DRAIN,
            config_panel_type: ConfigPanel.Type.LINE_DIMENSION_ONLY
        });
    };
}

module.exports = InteriorDrain;
