'use strict';

const LineOpening = require('../line_opening');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/LineOpening
 */
class Window extends LineOpening {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Window.Entity.Type.WINDOW,
            tool_type: Tool.Type.WINDOW,
            width: paper.getPixelsFromUnit({inches: 6}),
            side_snap: false
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            width: this.paper.getPixelsFromUnit({feet: 2, inches: 6})
        });
        return state;
    };

    /**
     * Create paper.js path
     *
     * @returns {*} - paper.js item
     */
    getPath() {
        let line = this.line,
            attached_line_width = this.properties.width,
            color = '#000000';
        if (line !== null) {
            attached_line_width = line.width;
            color = line.color;
        }
        let line_thickness = 1,
            fill_color = this.selected ? this.properties.selected_color : color,
            background = new this.paper.ps.Path.Rectangle({
                point: [0, 0],
                size: [this.state.width, attached_line_width],
                fillColor: fill_color,
                insert: false
            }),
            line_thickness2x = line_thickness * 2,
            inner_width = this.state.width - line_thickness2x,
            foreground = new this.paper.ps.Path.Rectangle({
                point: [line_thickness, line_thickness],
                size: [inner_width, attached_line_width - line_thickness2x],
                fillColor: 'white',
                insert: false
            }),
            mid_line = new this.paper.ps.Path.Rectangle({
                point: [line_thickness, (attached_line_width / 2) - (line_thickness / 2)],
                size: [inner_width, line_thickness],
                fillColor: fill_color,
                insert: false
            });
        return new this.paper.ps.Group({
            position: [0, 0],
            children: [background, foreground, mid_line],
            applyMatrix: false
        });
    };
}

module.exports = Window;
