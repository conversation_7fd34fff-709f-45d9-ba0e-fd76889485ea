'use strict';

const Dimension = require('../dimension');
const Node = require('../../../../nodes/base');
const ConfigPanel = require('../../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Line/Segment/Dimension
 */
class InteriorDrainTile extends Dimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, InteriorDrainTile.Type.INTERIOR_DRAIN_TILE);
        Object.assign(this.state, {
            label: 'Interior Drain Tile',
            icon: 'module--drawing--tools--interior-drain-tile',
            node_type: Node.Entity.Type.INTERIOR_DRAIN_TILE,
            config_panel_type: ConfigPanel.Type.LINE_DIMENSION_ONLY
        });
    };
}

module.exports = InteriorDrainTile;
