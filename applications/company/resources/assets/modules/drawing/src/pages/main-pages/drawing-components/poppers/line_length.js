'use strict';

const Base = require('./base');

const line_length_tpl = require('@cam-drawing-tpl/pages/main-pages/drawing-components/poppers/line_length.hbs');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Poppers
 */
class LineLength extends Base {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.PopperManager} manager
     */
    constructor(manager) {
        super(manager);
        Object.assign(this.state, {
            type: Base.Type.LINE_LENGTH,
            name: 'line-length',
            popper_options: {
                placement: 'top'
            }
        });
    };

    /**
     * Update length value in popper
     *
     * @param {number} length
     */
    updateLength(length) {
        this.elem.length.text(this.state.node.paper.getUnitsFromPixels(length));
    };

    /**
     * Destroy popper
     */
    destroy() {
        this.state.node.off('update', this.state.node_update);
        super.destroy();
    };

    /**
     * Boot popper
     *
     * @param {jQuery} root
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes.Line} node
     * @param {string} endpoint
     */
    boot(root, node, endpoint) {
        super.boot(root);
        this.elem.length = this.elem.root.fxFind('length');

        Object.assign(this.state, {
            node,
            endpoint,
            node_update: () => {
                this.setPosition(node[endpoint]);
                this.updateLength(node.vector.length);
            }
        });
        node.on('update', this.state.node_update);
        this.updateLength(node.vector.length);
        this.initPopper(node[endpoint]);
    };

    /**
     * Get content for popper
     */
    getContent() {
        return line_length_tpl();
    };
}

module.exports = LineLength;
