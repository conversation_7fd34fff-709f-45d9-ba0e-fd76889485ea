/**
 * @module Drawing/Pages/MainPages/DrawingComponents/Nodes/ElevationReading
 */

'use strict';

const Base = require('./base');
const Paper = require('../paper');
const Tool = require('../tools/base');
const Point = require('../utils/point');

/**
 * Base class for elevation reading nodes
 *
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes
 * @mixes SelectionOverlay
 * @abstract
 */
class ElevationReading extends Base {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            layer: Paper.Layer.TEXT,
            type: Base.Entity.Type.ELEVATION_READING,
            tool_type: Tool.Type.ELEVATION_READING
        });
        this.paperSubscribe('pdf-scale-changed', 'onPdfScaleChange');
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            elevation: null,
            position: null,
            rotation: 0,
            color: '#000000',
            size: 10
        });
        return state;
    };

    /**
     * Load data into state
     *
     * @param {object} state
     * @param {object} data
     * @returns {object}
     */
    loadData(state, data) {
        state = super.loadData(state, data);
        state.position = Point.of(state.position);
        return state;
    };

    /**
     * Get available font sizes
     *
     * @readonly
     *
     * @returns {number[]}
     */
    static get Size() {
        return [10, 12, 14, 18, 22, 24, 32, 48];
    };

    /**
     * Get position
     *
     * @readonly
     *
     * @returns {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point}
     */
    get position() {
        return this.state.position;
    };

    /**
     * Get rotation
     *
     * @readonly
     *
     * @returns {number}
     */
    get rotation() {
        return this.state.rotation;
    };

    /**
     * Get color
     *
     * @readonly
     *
     * @returns {string}
     */
    get color() {
        return this.state.color;
    };

    /**
     * Get color
     *
     * @readonly
     *
     * @returns {string}
     */
    get elevation() {
        return this.state.elevation;
    };

    /**
     * Get font size
     *
     * @readonly
     *
     * @returns {number}
     */
    get size() {
        return this.state.size;
    };

    /**
     * Handle a zoom change by rendering node again
     */
    onZoomChanged() {
        this.drawSelected(this.getPaperItem('main'));
    };

    /**
     * Select node
     *
     * Subscribe to zoom change event so our selection overlay can be resized as needed.
     */
    select() {
        this.paperSubscribe('zoom-changed', 'onZoomChanged');
        super.select();
    };

    /**
     * Deselect node
     *
     * Unsubscribe from zoom change event.
     */
    deselect() {
        if (!this.selected || this.deleted) {
            return;
        }
        this.paperUnsubscribe('zoom-changed');
        super.deselect();
    };

    /**
     * Handle PDF scaling change
     */
    onPdfScaleChange() {
        this.render(true);
    };

    /**
     * Perform hit test of node only to determine if point exists within
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Paper.Point} point
     * @returns {(null|object)}
     */
    hitTest(point) {
        return this.selectionOverlayHitTest(point, {
            tolerance: this.paper.hit_test_tolerance
        });
    };

    /**
     * Draw selected state
     *
     * @param {paper.Item} item
     */
    drawSelected(item) {
        let size = item.firstChild.bounds.size,
            spacing = 0.05;
        this.drawSelectionOverlay(size, item.localToParent(item.firstChild.bounds.center), {
            handles: {
                rotate: true
            },
            spacing: Math.min(size.width * spacing, size.height * spacing)
        });
    };

    /**
     * Draw node and register
     */
    draw() {
        let pdf_scale = Math.max(1, this.paper.pdf_scale),
            text = new this.paper.ps.PointText({
                content: '(' + this.state.elevation + '")',
                fillColor: this.state.color,
                fontFamily: this.properties.font_family,
                fontSize: Math.floor(this.state.size * pdf_scale),
                insert: false
            }),
            item = new this.paper.ps.Group({
                children: [text],
                applyMatrix: false,
                position: this.state.position,
                rotation: this.state.rotation,
                parent: this.getLayer(this.selected ? 'selected' : 'default')
            });
        this.setPaperItem('main', item, true);

        if (this.selected) {
            this.drawSelected(item);
        }
    };

    /**
     * Normalize properties of node after update
     */
    postUpdate() {
        this.state.position = this.state.position.round();
        this.state.rotation = Math.round(this.state.rotation);
    };

    /**
     * Get data for storage in entity
     *
     * @returns {object}
     */
    entityData() {
        return {
            position: Point.toArray(this.position),
            elevation: this.elevation,
            color: this.color,
            rotation: this.rotation
        };
    };
}


require('../node-mixins/selection_overlay')(ElevationReading);

module.exports = ElevationReading;
