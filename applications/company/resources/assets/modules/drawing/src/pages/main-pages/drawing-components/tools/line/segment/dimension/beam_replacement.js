'use strict';

const Dimension = require('../dimension');
const Node = require('../../../../nodes/base');
const ConfigPanel = require('../../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/Line/Segment/Dimension
 */
class BeamReplacement extends Dimension {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, BeamReplacement.Type.BEAM_REPLACEMENT);
        Object.assign(this.state, {
            label: 'Beam Replacement',
            icon: 'module--drawing--tools--beam-replacement',
            node_type: Node.Entity.Type.BEAM_REPLACEMENT,
            config_panel_type: ConfigPanel.Type.LINE_DIMENSION_ONLY
        });
    };
}

module.exports = BeamReplacement;
