'use strict';

const Entry = require('../entry');
const Tool = require('../../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/LineOpening/Entry
 */
class DoubleDoor extends Entry {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: DoubleDoor.Entity.Type.DOUBLE_DOOR,
            tool_type: Tool.Type.DOUBLE_DOOR,
            door_thickness: Math.ceil(paper.getPixelsFromUnit({inches: 2}))
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            width: this.paper.getPixelsFromUnit({feet: 6})
        });
        return state;
    };

    /**
     * Calculate node offset based on line and paper.js path info
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Line.Segment} line
     * @param {object} path - paper.js path
     * @returns {number}
     */
    calculateNodeOffset(line, path) {
        return Math.floor(path.bounds.height / 2) + line.half_width;
    };

    /**
     * Create paper.js path
     *
     * @returns {*} - paper.js item
     */
    getPath() {
        let door_color = this.line !== null ? this.line.color : '#000000',
            door_size = {
                width: this.state.width / 2,
                thickness: this.properties.door_thickness
            },
            door_1 = new this.paper.ps.Path.Rectangle({
                point: [0, 0],
                size: [door_size.thickness, door_size.width],
                fillColor: this.selected ? this.properties.selected_color : door_color,
                strokeWidth: 0
            }),
            door_2 = new this.paper.ps.Path.Rectangle({
                point: [this.state.width - door_size.thickness, 0],
                size: [door_size.thickness, door_size.width],
                fillColor: this.selected ? this.properties.selected_color : door_color,
                strokeWidth: 0
            })
        ;

        let start_point_1 = new this.paper.ps.Point(door_size.thickness, door_size.width),
            anchor_point_1 = new this.paper.ps.Point(start_point_1.x, 0),
            midpoint_length_1 = ((door_size.width - door_size.thickness) + door_size.width) / 2,
            vector_1 = start_point_1.subtract(anchor_point_1).rotate(-45).normalize(midpoint_length_1),
            midpoint_1 = anchor_point_1.add(vector_1),
            endpoint_1 = new this.paper.ps.Point(door_size.width, 0);
        let sweep_1 = new this.paper.ps.Path.Arc({
            from: start_point_1,
            through: midpoint_1,
            to: endpoint_1,
            insert: false,
            strokeColor: this.selected ? this.properties.selected_color : '#b4b4b4',
            dashArray: [8, 2]
        });

        let start_point_2 = new this.paper.ps.Point(this.state.width - door_size.thickness, door_size.width),
            anchor_point_2 = new this.paper.ps.Point(start_point_2.x, 0),
            midpoint_length_2 = ((door_size.width - door_size.thickness) + door_size.width) / 2,
            vector_2 = start_point_2.subtract(anchor_point_2).rotate(45).normalize(midpoint_length_2),
            midpoint_2 = anchor_point_2.add(vector_2),
            endpoint_2 = new this.paper.ps.Point(door_size.width, 0);
        let sweep_2 = new this.paper.ps.Path.Arc({
            from: start_point_2,
            through: midpoint_2,
            to: endpoint_2,
            insert: false,
            strokeColor: this.selected ? this.properties.selected_color : '#b4b4b4',
            dashArray: [8, 2]
        });

        return new this.paper.ps.Group({
            position: [0, 0],
            children: [door_1, sweep_1, door_2, sweep_2],
            applyMatrix: false
        });
    };
}

module.exports = DoubleDoor;
