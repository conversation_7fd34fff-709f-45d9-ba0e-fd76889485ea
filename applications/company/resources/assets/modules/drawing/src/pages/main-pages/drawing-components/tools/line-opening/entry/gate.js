'use strict';

const Entry = require('../entry');
const Node = require('../../../nodes/base');
const ConfigPanel = require('../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineOpening/Entry
 */
class Gate extends Entry {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, Gate.Type.GATE);
        Object.assign(this.state, {
            label: 'Single Gate',
            icon: 'module--drawing--tools--gate',
            node_type: Node.Entity.Type.GATE,
            config_panel_type: ConfigPanel.Type.LINE_OPENING_FULL
        });
    };
}

module.exports = Gate;
