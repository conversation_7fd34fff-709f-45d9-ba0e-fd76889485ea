'use strict';

const Entry = require('../entry');
const Node = require('../../../nodes/base');
const ConfigPanel = require('../../../config-panels/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Tools/LineOpening/Entry
 */
class DoubleGate extends Entry {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages.Drawing} controller
     */
    constructor(controller) {
        super(controller, DoubleGate.Type.DOUBLE_GATE);
        Object.assign(this.state, {
            label: 'Double Gate',
            icon: 'module--drawing--tools--double-gate',
            node_type: Node.Entity.Type.DOUBLE_GATE,
            config_panel_type: ConfigPanel.Type.LINE_OPENING_WIDTH_SIDE_ONLY
        });
    };
}

module.exports = DoubleGate;
