'use strict';

const DrawingEntity = require('./entities/drawing');
const DrawingRepo = require('./repositories/drawing');

const import_tpl = require('@cam-drawing-tpl/import.hbs');

/**
 * @memberof module:Drawing
 */
class Importer {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages.Main} controller
     */
    constructor(controller) {
        this.elem = {
            root: $(import_tpl())
        };
        this.elem.file = this.elem.root.fxFind('file');
        this.elem.file.on('change', () => {
            let files = Array.from(this.elem.file[0].files);
            for (let file of files) {
                this.getFileJSON(file).then(async (data) => {
                    await this.run(data);
                }).catch((e) => {
                    console.error('Unable to get file JSON', e);
                });
            }
            this.hide();
        });

        this.state = {
            controller
        };

        $('body').append(this.elem.root);
    };

    /**
     * Open importer popup with file input
     */
    open() {
        this.elem.root.show();
    };

    /**
     * Hide importer popup
     */
    hide() {
        this.elem.root.hide();
    };

    /**
     * Get JSON data from uploaded file
     *
     * @param {File} file
     * @returns {Promise<object>}
     */
    getFileJSON(file) {
        return new Promise((resolve, reject) => {
            let reader = new FileReader();
            reader.onload = (e) => {
                try {
                    resolve(JSON.parse(e.target.result));
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = (e) => {
                reject(e);
            };
            reader.readAsText(file);
        });
    };

    /**
     * Run import for drawing
     *
     * @param {object} data - drawing data
     * @returns {Promise<void>}
     */
    async run(data) {
        let drawing = new DrawingEntity(data);
        drawing.status = DrawingEntity.Status.IN_PROGRESS;
        drawing.sync_status = DrawingEntity.SyncStatus.PENDING;
        drawing.is_published = false;
        drawing.project = null;
        drawing.project_id = null;
        drawing.created_by_user_id = this.state.controller.current_user.id;
        await DrawingRepo.store(drawing);
    };
}

module.exports = Importer;
