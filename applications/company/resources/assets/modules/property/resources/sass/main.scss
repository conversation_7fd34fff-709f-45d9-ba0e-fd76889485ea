@use '~@cac-sass/config/global' with (
    $legacy: false
);
@use '~@cac-sass/base';
@use '~@cas-layout-sass/layout';
@use '~@cas-table-sass/table';
@use '~@cas-modal-sass/modal';
@use '~@cas-form-input-sass/date-time';

.m-property-list {
    @include base.full-width-height;
    padding: base.unit-rem-calc(24px);
    @include base.respond-to('<small') {
        padding: 0;
    }
    .c-pl-table {
        @include base.full-width-height;
        padding: base.unit-rem-calc(12px);
        background-color: base.$color-white-default;
        border-radius: base.unit-rem-calc(12px);
        box-shadow: base.$elevation-level-1;
        @include base.respond-to('<small') {
            padding: base.unit-rem-calc(12px) 0;
            border-radius: 0;
            box-shadow: none;
        }
    }
}
