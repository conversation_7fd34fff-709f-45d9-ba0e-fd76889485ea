<div class="c-ac-page m-set-user-details">
    <form class="c-sp-inputs" data-js="form">
        <div class="c-spi-header">
            <div class="c-spih-title">Complete Your Profile</div>
            <p>Fill in the details below to finish setting up your account.</p>
        </div>
        <div class="c-spi-form">
            <div class="f-field t-first-name">
                <label class="f-f-label" for="first-name">First Name</label>
                <input class="f-f-input" id="first-name" type="text" data-js="first_name" tabindex="1">
            </div>
            <div class="f-field t-last-name">
                <label class="f-f-label" for="last-name">Last Name</label>
                <input class="f-f-input" id="last-name" type="text" data-js="last_name" tabindex="2">
            </div>
            <div class="f-field t-phone">
                <label class="f-f-label" for="phone">Phone #</label>
                <input class="f-f-input" id="phone" type="text" data-js="phone" tabindex="3">
            </div>
            <div class="f-field t-password">
                <label class="f-f-label" for="set-password">Password</label>
                <input class="f-f-input" id="set-password" type="password" data-js="password" tabindex="4">
            </div>
        </div>
        <div class="c-spi-actions">
            <a class="c-spia-action t-logout" href="{{logout_url}}">Cancel</a>
            <button class="c-spia-action t-submit" tabindex="3">Complete</button>
        </div>
    </form>
</div>