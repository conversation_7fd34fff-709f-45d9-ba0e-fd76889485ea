<div class="c-tdm-header">
    <h1 class="c-tdmh-text">Task Details</h1>
    <a class="c-tdmh-close" data-close>
        <svg class="c-tdmhc-icon"><use xlink:href="#remix-icon--system--close-line"></use></svg>
    </a>
</div>
<div class="m-content">
    <div class="c-c-wrapper">
        <div class="c-cw-header">
            <h4 data-js="title"></h4>
            <button class="c-cwh-button" data-js="edit-button">
                <div data-text>Edit</div>
                <svg data-icon><use xlink:href="#remix-icon--design--edit-2-line"></use></svg>
            </button>
        </div>
        <div class="c-cw-row t-status">
            <p class="c-cwr-title">Status</p>
            <div class="c-cwr-line"></div>
            <div class="c-cwr-content-wrapper t-status">
                <div data-js="status"></div>
            </div>
        </div>
        <div class="c-cw-row">
            <p class="c-cwr-title">Type</p>
            <div class="c-cwr-line"></div>
            <div class="c-cwr-content-wrapper" data-js="type"></div>
        </div>
        <div class="c-cw-row t-hidden t-priority" data-js="priority_content">
            <p class="c-cwr-title">Priority</p>
            <div class="c-cwr-line"></div>
            <div class="c-cwr-content-wrapper" data-js="priority"></div>
        </div>
        <div class="c-cw-row">
            <p class="c-cwr-title">Assigned To</p>
            <div class="c-cwr-line"></div>
            <div class="c-cwr-content-wrapper" data-js="assigned_to"></div>
        </div>
        <div class="c-cw-row t-hidden t-date" data-js="due_date_content">
            <p class="c-cwr-title">Due Date</p>
            <div class="c-cwr-line"></div>
            <div class="c-cwr-content-wrapper" data-js="due_date"></div>
        </div>
        <div class="c-cw-row t-hidden t-association" data-js="association_content">
            <p class="c-cwr-title">Association</p>
            <div class="c-cwr-line"></div>
            <div class="c-cwr-content-wrapper" data-js="association"></div>
        </div>
        <div class="c-cw-actions">
            <button class="c-cwa-button t-complete" data-js="complete-button">
                <div data-text>Complete</div>
                <svg data-icon><use xlink:href="#remix-icon--system--check-double-line"></use></svg>
            </button>
            <button class="c-cwa-button t-open" data-js="open-button">
                <div data-text>reopen</div>
                <svg data-icon><use xlink:href="#remix-icon--arrows--arrow-go-back-line"></use></svg>
            </button>
        </div>
    </div>
    <div class="c-c-notes t-hidden" data-js="notes_wrapper">
        <div class="c-cn-content t-hidden" data-js="notes_content">
            <h4 class="c-cnc-title t-header">Notes</h4>
            <div class="c-cnc-content-wrapper" data-js="notes"></div>
        </div>
        <div class="c-cn-line t-hidden" data-js="notes_line"></div>
        <div class="c-cn-content t-complete t-hidden" data-js="completion_notes_content">
            <h4 class="c-cnc-title t-header">Completion Notes</h4>
            <div class="c-cnc-content-wrapper" data-js="completion_notes"></div>
        </div>
    </div>
</div>

