'use strict';

import Modal from '@ca-package/router/src/modal';

import ManageLeadModal from '../../../modals/main/manager/manage_lead';

export class Create extends Modal {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent
        });
    };

    /**
     * Get and cache manage lead modal
     *
     * @returns {module:Modal.Confirm}
     */
    get modal() {
        if (this.state.modal === undefined) {
            let modal = ManageLeadModal;
            this.state.modal = new modal(this);
        }
        return this.state.modal;
    };

    /**
     * Open manage lead modal with promise
     *
     * @returns {Promise<undefined>}
     */
    openModal() {
        return new Promise((resolve, reject) => {
            return this.modal.open({
                config: {
                    action: ManageLeadModal.Action.ADD,
                },
                promise: {resolve, reject}
            });
        });
    };

    /**
     * Load modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);

        this.openModal().then((result) => {
            this.router.navigate('manager');
        });
    };

    /**
     * Unload modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.modal.close();
        await super.unload(request, next);
    };
}
