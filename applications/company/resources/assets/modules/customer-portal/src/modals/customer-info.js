'use strict';

import {Base} from '@ca-submodule/modal';
import customer_info_modal_tpl from '@cam-customer-portal-tpl/modals/customer-info.hbs';

/**
 * Customer Information Modal
 * 
 * Displays customer information in a modal dialog
 */
export class CustomerInfoModal extends Base {
    /**
     * Constructor
     */
    constructor() {
        super('', {
            size: Base.Size.SMALL,
            closable: true,
            wrapper: true,
            classes: ['c-customer-info-modal']
        });
        
        this.setTitle('Customer Information');
        
        this.addAction({
            type: Base.Action.CLOSE
        });
    }

    /**
     * Open modal with customer data
     *
     * @param {Object} data - Customer information
     * @param {Object} data.customer - Customer details
     * @param {string} data.customer.business_name - Business name
     * @param {string} data.customer.name - Customer name
     * @param {string} data.customer.phone - Phone number
     * @param {string} data.customer.phone_description - Phone description (e.g., "Primary")
     * @param {string} data.customer.email - Email address
     */
    open(data) {
        this.setContent(customer_info_modal_tpl({
            customer: data.customer || {}
        }));

        this.elem.content.find('[data-js="request-info-change"]').on('click', () => {
            //To-do: implement the request change action
        });

        super.open();
    }
}
