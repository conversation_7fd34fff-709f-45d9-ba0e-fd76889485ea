<!-- Bids card component -->
<div class="c-card">
    <header class="c-card-header">
        <h3>{{#if title}}{{title}}{{else}}Open Bids{{/if}}</h3>
        {{#if showSeeAll}}
            <a class="c-see-all" href="#" data-js="see-all-bids">See All →</a>
        {{/if}}
    </header>
    {{#if bids.length}}
        <div class="c-bids-container">
            <div class="c-bc-grid">
                {{#each bids}}
                    <div class="m-content-box m-bid-card">
                        <div class="c-bc-header">
                            <div class="c-bch-title-section">
                                <div
                                    class="c-bchts-title"
                                >{{project.description}}</div>
                                <div
                                    class="c-bchts-number"
                                >(#{{referenceID}})</div>
                            </div>
                            <div class="c-bch-download">
                                <button class="c-bchd-btn">
                                    <span>Download</span>
                                    <svg><use
                                            xlink:href="#remix-icon--system--download-line"
                                        ></use></svg>
                                </button>
                            </div>
                        </div>

                        <div class="c-bc-address">
                            <div class="c-bca-line">
                                {{project.property.address}}
                            </div>
                            {{#if project.property.address2}}
                                <div class="c-bca-line">
                                    {{project.property.address2}}
                                </div>
                            {{/if}}
                            <div class="c-bca-line">
                                {{project.property.city}},
                                {{project.property.state}}
                                {{project.property.zip}}
                            </div>
                        </div>
                        <div class="c-bc-total">
                            <span class="c-bct-label">Bid Total</span>
                            <span
                                class="c-bct-divider"
                                aria-hidden="true"
                            ></span>
                            <span class="c-bct-amount">
                                {{formatCurrency total}}
                            </span>
                        </div>

                        <div class="c-bcf-action">
                            <button class="button">View Bid</button>
                        </div>

                        <div class="c-bc-footer">
                            <div class="c-bcf-date">
                                <div class="c-bcfd-text">Bid
                                    {{status_summary.status}}
                                    {{#if
                                        status_summary.date
                                    }}{{formatBidDate
                                            status_summary.date
                                        }}{{/if}}</div>
                            </div>
                        </div>
                    </div>
                {{/each}}
            </div>
        </div>
    {{else}}
        <div class="c-empty">
            <svg class="c-sbciw-image"><use
                    xlink:href="#module--customer-portal--bids"
                ></use></svg>
            <p class="title">No Bids</p>
            <p class="subtitle">
                It looks like there are currently no bids placed.
            </p>
        </div>
    {{/if}}
</div>
