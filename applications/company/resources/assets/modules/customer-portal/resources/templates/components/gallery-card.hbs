<!-- Gallery card component -->
<div class="c-card">
    <header class="c-card-header">
        <h3>{{#if title}}{{title}}{{else}}Recent Uploads{{/if}}</h3>
        {{#if showSeeAll}}
            <a class="c-see-all" href="#" data-js="see-all-gallery">See All →</a>
        {{/if}}
    </header>
    {{#if uploads.length}}
        <div class="c-uploads-container">
            {{#each (groupByDate uploads)}}
                <div class="m-date-group">
                    <div class="c-dg-header">{{this.date}}</div>
                    <div class="c-dg-grid">
                        {{#each this.images}}
                            <div
                                class="m-image-card"
                                data-file-id="{{fileID}}"
                            >
                                <div class="c-imc-image">
                                    <img
                                        src="{{url}}"
                                        alt="{{name}}"
                                        loading="lazy"
                                        onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                                    />
                                    <div
                                        class="c-imci-placeholder"
                                        style="display: none;"
                                    >
                                        <svg><use
                                                xlink:href="#remix-icon--document--file-line"
                                            ></use></svg>
                                    </div>
                                </div>
                                <div
                                    class="c-imc-number"
                                >{{displayIndex}}</div>
                                <div class="c-imc-info-button">
                                    <svg><use
                                            xlink:href="#remix-icon--system--information-line"
                                        ></use></svg>
                                </div>
                            </div>
                        {{/each}}
                    </div>
                </div>
            {{/each}}
        </div>
    {{else}}
        <div class="c-empty">
            <svg class="c-sbciw-image"><use
                    xlink:href="#module--customer-portal--uploads"
                ></use></svg>
            <p class="title">No Images</p>
            <p class="subtitle">
                It seems there are no updates to share at this time.
            </p>
        </div>
    {{/if}}
</div>
