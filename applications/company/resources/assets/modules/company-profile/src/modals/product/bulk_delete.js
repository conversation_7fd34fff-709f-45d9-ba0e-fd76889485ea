'use strict';

const Api = require('@ca-package/api');

const DeleteModal = require('@ca-submodule/modal').Delete;

const content_tpl = require('@cam-company-profile-tpl/modals/product/bulk_delete.hbs');

class Delete extends DeleteModal {
    constructor() {
        super({
            classes: ['m-delete-products-modal']
        });
        this.state.product_ids = [];
        this.setTitle('Delete Products');
        this.setContent(content_tpl());
        this.elem.products = this.elem.content.fxFind('products');
        this.elem.product_list = this.elem.content.fxFind('product-list');
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {string} $0.product_id
     * @param {object} $0.promise
     * @returns {Modal}
     */
    open({product_ids, promise}) {
        this.state.product_ids = product_ids;
        this.startWorking();
        this.elem.products.text(product_ids.length);
        Api.Resources.ProductItems()
            .filters({
                'id': Api.Filter.make('in', product_ids)
            })
            .all().then((collection, response) => {
                let product_list = '';
                for (let item of collection.entities) {
                    item = item.data;
                    product_list = `${product_list}<div class="c-dpm-pl-items">${item.name}</div>`;
                }
                this.elem.product_list.html(product_list);
            this.resetWorking();
        }, (error) => {
            this.showErrorMessage('Unable to fetch product info');
        });

        this.state.promise = promise;
        return super.open();
    };

    /**
     * Create Batch Request
     *
     * @param {array} products
     * @returns {Api.BatchRequest.Multiple|boolean}
     */
    getDeleteProductsBatchRequest(products) {
        let requests = [];
        products.forEach((product_id) => {
            let request = new Api.BatchRequest.Single('product-item', 'partial-update', {
                id: product_id,
                status: Api.Constants.ProductItems.Status.ARCHIVED
            });
            request.promise.done(() => {
                // console.log('Can do actions after is done, like remove item from UI');
            });
            requests.push(request);
        });
        if (requests.length === 0) {
            return false;
        }
        let batch_request = new Api.BatchRequest.Multiple(true);
        for (let request of requests) {
            batch_request.add(request);
        }
        return batch_request;
    };

    /**
     * Handle 'delete' response
     */
    handleDelete() {
        this.startWorking();
        let batch_request = this.getDeleteProductsBatchRequest(this.state.product_ids);
        batch_request.promise.then((result) => {
            this.resetWorking();
            this.state.promise.resolve(true);
            this.close();
        }, () => {
            this.resetWorking();
            this.showErrorMessage('Unable to delete product');
        });
        let queue = new Api.BatchQueue;
        queue.addRequest(batch_request);
    };

    /**
     * Handle 'no' response
     */
    handleNo() {
        this.close();
        this.resetWorking();
        this.state.promise.resolve(null);
    };

    /**
     * Close modal
     */
    close() {
        this.elem.products.text('');
        this.elem.product_list.text('');
        this.state.product_ids = [];
        super.close();
    };
}

module.exports = Delete;
