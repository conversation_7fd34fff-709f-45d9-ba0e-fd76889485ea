'use strict';

const Api = require('@ca-package/api');
const Confirm = require('@ca-submodule/modal').Confirm;
const content_tpl = require('@cam-company-profile-tpl/modals/integrations/wisetack/disconnect.hbs');

class Disconnect extends Confirm {
    constructor() {
        super();
        this.setTitle('Disconnect');
        this.setContent(content_tpl());
    };

    /**
     * Open modal
     *
     * @param {object} $0.promise
     * @returns {Modal}
     */
    open({promise} = {}) {
        this.state.promise = promise;
        return super.open();
    };

    /**
     * Handle 'yes' response
     */
    handleYes() {
        this.startWorking();
            const WISETACK_URL = `${window.fx_url.API}integration/wisetack/merchant`;
            $.ajax({
                url: WISETACK_URL,
                type: Api.Request.Method.DELETE,
                contentType: 'application/json',
            }).then(() => {
                this.resetWorking();
                this.state.promise.resolve(true);
                this.close();
            }, () => {
                this.resetWorking();
                this.showErrorMessage('Unable to disconnect from Wisetack integration');
            });
    };

    /**
     * Handle 'no' response
     */
    handleNo() {
        this.resetWorking();
        this.state.promise.resolve(null);
        this.close();
    };
}

module.exports = Disconnect;
