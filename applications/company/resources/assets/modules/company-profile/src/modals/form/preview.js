'use strict';

const isEqual = require('lodash/isEqual');

const Api = require('@ca-package/api');

const Form = require('@ca-submodule/form').Controller;
const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/button_group'));
const Modal = require('@ca-submodule/modal').Base;

const modal_tpl = require('@cam-company-profile-tpl/modals/form/preview.hbs');
const iframe_form_tpl = require('@cam-company-profile-tpl/modals/form/preview/iframe_form.hbs');

const LayoutTypes = {
    BID_DOCUMENT: 4,
    JOB_DOCUMENT: 5
}
const TypeOptions = {
    [Api.Constants.FormItems.Type.BID]: [
        {type: LayoutTypes.BID_DOCUMENT, name: 'Bid Document', description: 'This view shows what your customer will see within the proposal PDF.'},
        {type: LayoutTypes.JOB_DOCUMENT, name: 'Job Document', description: 'This view shows the internal version of the bid. It will include all information from the Bid Document as well as any internal fields.'}
    ]
};

class Preview extends Modal {
    constructor(parent) {
        let iframe_name = `${parent.namespace}-form-preview`;
        super(modal_tpl({
            iframe_name
        }), {
            size: Modal.Size.SMALL,
            closable: true,
            classes: ['t-preview']
        });
        Object.assign(this.state, {
            parent,
            form: null,
            iframe_name,
            loaded_callback: 'formPreviewRendered',
            last_form: null,
            last_payload: null,
            render_type: null,
            payload: null,
            entry: null,
            preview_action: null,
            back_action: null,
            form_messages: {},
            render_messages: {}
        });

        this.elem.form_container = this.elem.content.fxFind('form-container');
        this.elem.render_container = this.elem.content.fxFind('render-container').hide();
        this.elem.type_container = this.elem.render_container.fxFind('type-container').hide();
        this.elem.type = this.elem.type_container.fxFind('type');
        this.elem.description = this.elem.content.fxFind('description');
        this.elem.iframe = this.elem.render_container.fxFind('iframe');

        let options = TypeOptions[parent.structure.type];
        if (!Array.isArray(options)) {
            throw new Error(`No render type options found for structure type: ${parent.structure.type}`);
        }
        let render_types = new Map;
        if (options.length === 1) {
            let option = options[0];
            render_types.set(option.type, option);
            this.elem.type_container.hide();
        } else {
            for (let option of options) {
                this.elem.type.append(`<option value="${option.type}">${option.name}</option>`);
                render_types.set(option.type, option);
            }
            FormInput.init(this.elem.type);
            this.elem.type_container.show();
        }
        this.state.render_types = render_types;

        window[this.state.loaded_callback] = this.renderLoaded.bind(this);

        this.elem.type.fxEvent('change', () => {
            this.renderType(parseInt(this.elem.type.val()));
        });

        this.on('close', () => {
            this.reset();
        });
    };

    /**
     * Load category info by id
     *
     * @param {string} category_id
     * @returns {Promise<void>}
     */
    async loadData(category_id) {
        let {data: category} = await Api.Resources.CompanyFormCategories().retrieve(category_id);
        return category;
    };

    /**
     * Get current form payload
     *
     * Used to submit to preview to change form structure for use with form submodule.
     *
     * @returns {*}
     */
    getPayload() {
        if (this.state.payload === null) {
            this.state.payload = Object.assign(this.state.parent.getInfoPayload(), this.state.parent.getRelationPayload());
        }
        return this.state.payload;
    };

    /**
     * Load form
     *
     * If payload hasn't changed since last time a preview was request, we just use the cached version to save a
     * request. Otherwise, the form payload is sent to the preview endpoint to be validated, setup, and return the
     * client structure needed for the form submodule.
     *
     * @returns {Promise<void>}
     */
    async loadForm() {
        this.setTitle('Form Preview');
        let form,
            payload = this.getPayload();
        if (this.state.last_form !== null && isEqual(this.state.last_payload, payload)) {
            form = this.state.last_form;
        } else {
            form = await Api.Resources.CompanyFormItems()
                .method(Api.Request.Method.POST)
                .custom('preview', payload);
            this.state.last_form = form;
            this.state.last_payload = payload;
        }
        let {id, form: {structure, info}} = form;
        this.state.preview_id = id;
        let instance = new Form({
            auto_save: false,
            preview_mode: true
        });
        instance.load(structure, info);
        this.elem.form_container.append(instance.render());
        instance.boot();
        this.state.form = instance;
    };

    /**
     * Open modal
     */
    open() {
        this.startWorking();
        this.loadForm().then(() => {
            this.showForm();
            this.resetWorking();
        }, e => {
            this.showErrorMessage('Unable to load preview, please contact support');
            this.resetWorking();
            console.log(e);
        });
        super.open();
    };

    /**
     * Add message for form view
     *
     * @param {string} name
     * @param {module:FlashMessage.Message} message
     */
    addFormMessage(name, message) {
        this.state.form_messages[name] = message;
    };

    /**
     * Determines if form flash message exists
     *
     * @param {string} name
     * @returns {boolean}
     */
    hasFormMessage(name) {
        return this.state.form_messages[name] !== undefined;
    };

    /**
     * Remove specified form message by name
     *
     * @param {string} name
     */
    removeFormMessage(name) {
        if (this.state.form_messages[name] === undefined) {
            return;
        }
        this.state.form_messages[name].delete();
        delete this.state.form_messages[name];
    };

    /**
     * Show form preview
     */
    showForm() {
        this.elem.form_container.show();
        this.addPreviewAction();
        this.setTitle('Form Preview');
        let payload = this.getPayload();
        if (this.state.parent.structure.type === Api.Constants.FormItems.Type.BID) {
            if (payload.item.is_hidden_from_list && !this.hasFormMessage('active')) {
                this.addFormMessage('active', this.showErrorMessage('Form is not active and will not be available in bid'));
            }
        }
    };

    /**
     * Add preview action to modal
     */
    addPreviewAction() {
        this.state.preview_action = this.addAction({
            type: Modal.Action.SAVE,
            label: 'Next',
            handler: () => this.render()
        });
    };

    /**
     * Remove preview action from modal
     */
    removePreviewAction() {
        this.removeAction(this.state.preview_action);
        this.state.preview_action = null;
    };

    /**
     * Add back action to modal
     */
    addBackAction() {
        this.state.back_action = this.addAction({
            type: Modal.Action.SAVE,
            label: 'Back',
            handler: () => this.back()
        });
    };

    /**
     * Remove back action from modal
     */
    removeBackAction() {
        this.removeAction(this.state.back_action);
        this.state.back_action = null;
    };

    /**
     * Get entry data from preview form
     *
     * @returns {object}
     */
    getEntry() {
        if (this.state.entry === null) {
            this.state.entry = this.state.form.getEntry();
        }
        return this.state.entry;
    };

    /**
     * Add render flash message
     *
     * @param {string} name
     * @param {module:FlashMessage.Message} message
     */
    addRenderMessage(name, message) {
        this.state.render_messages[name] = message;
    };

    /**
     * Clear all render view messages
     */
    clearRenderMessages() {
        for (let name of Object.keys(this.state.render_messages)) {
            this.state.render_messages[name].delete();
        }
        this.state.render_messages = {};
    };

    /**
     * Render specified layout type
     *
     * @param {number} type
     */
    renderType(type) {
        if (this.state.render_type === type) {
            return;
        }
        this.clearRenderMessages();
        this.startWorking();
        this.elem.iframe.hide().css('height', 'auto');
        let form = $(iframe_form_tpl({
            action: `${window.fx_pages.FORM_PREVIEW_RENDER_URL.replace('{id}', this.state.preview_id)}?loaded_callback=${this.state.loaded_callback}`,
            target: this.state.iframe_name
        }));
        form.fxFind('entry').val(JSON.stringify(this.getEntry()));
        form.fxFind('layout-type').val(type);
        form.appendTo('body').submit();
        form.remove();
        let info = this.state.render_types.get(type);
        this.elem.description.text(info.description);
        if (this.state.parent.structure.type === Api.Constants.FormItems.Type.BID) {
            let payload = this.getPayload();
            switch (type) {
                case LayoutTypes.BID_DOCUMENT:
                    if (payload.item.is_hidden_from_bid) {
                        this.addRenderMessage('bid-hidden', this.showErrorMessage('Form is set to not show on bid document'));
                    }
                    break;
                case LayoutTypes.JOB_DOCUMENT:
                    if (payload.item.is_hidden_from_scope_of_work) {
                        this.addRenderMessage('job-hidden', this.showErrorMessage('Form is set to not show on job document'));
                    }
                    break;
            }
        }
        this.state.render_type = type;
    };

    /**
     * Render form using entry data from preview
     */
    render() {
        this.startWorking();

        if (!this.state.form.isValid()) {
            this.addFormMessage('validation', this.showErrorMessage('Form has validation errors'));
            this.resetWorking();
            return;
        }
        this.removeFormMessage('validation');
        this.elem.content.addClass('t-document-view');
        this.setTitle('Document Preview');
        this.removePreviewAction();
        this.addBackAction();
        this.elem.form_container.hide();
        this.elem.render_container.show();
        let [{type: default_type}] = this.state.render_types.values();
        this.elem.type.val(default_type).trigger('button-group:change');
        this.renderType(default_type);
    };

    /**
     * Handle when rendered form is finished loading in iframe
     */
    renderLoaded() {
        this.elem.iframe.show();
        let doc = this.elem.iframe[0].contentWindow.document,
            body = doc.body,
            html = doc.documentElement,
            height = Math.max(body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight);
        this.elem.iframe.css('height', `${height}px`);
        this.resetWorking();
    };

    /**
     * Navigate back to form preview
     */
    back() {
        this.elem.content.removeClass('t-document-view');
        this.elem.render_container.hide();
        this.removeBackAction();
        this.clearRenderMessages();
        this.state.entry = null;
        this.state.render_type = null;
        this.showForm();
    };

    /**
     * Reset modal to default state
     */
    reset() {
        this.resetWorking();
        this.clearMessages();
        this.state.payload = null;
        if (this.state.form !== null) {
            this.state.form.destroy();
        }
        this.state.entry = null;
        this.state.render_type = null;
        this.elem.content.removeClass('t-document-view');
        this.elem.render_container.hide();
        this.removeBackAction();
        this.removePreviewAction();
        this.state.form_messages = {};
        this.state.render_messages = {};
    };

    /**
     * Unload stored data from module
     *
     * Called when associated page is unloaded to free up memory.
     */
    unload() {
        this.state.last_form = null;
        this.state.last_payload = null;
    };
}

module.exports = Preview;
