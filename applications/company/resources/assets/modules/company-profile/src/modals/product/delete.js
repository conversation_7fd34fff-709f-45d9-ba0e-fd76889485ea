'use strict';

const Api = require('@ca-package/api');

const DeleteModal = require('@ca-submodule/modal').Delete;

const content_tpl = require('@cam-company-profile-tpl/modals/product/delete.hbs');

class Delete extends DeleteModal {
    constructor() {
        super();
        this.setTitle('Delete Product');
        this.setContent(content_tpl());
        this.elem.product = this.elem.content.fxFind('product');
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {string} $0.product_id
     * @param {object} $0.promise
     * @returns {Modal}
     */
    open({product_id, promise}) {
        this.startWorking();
        Api.Resources.ProductItems()
            .retrieve(product_id)
            .then(({data}) => {
                if (data.status !== Api.Constants.ProductItems.Status.ACTIVE) {
                    promise.resolve(null);
                    return;
                }
                this.state.product = data;
                this.elem.product.text(data.name);
                this.resetWorking();
            }, error => {
                this.showErrorMessage('Unable to fetch product info');
                console.log(error);
            });
        this.state.promise = promise;
        return super.open();
    };

    /**
     * Handle 'delete' response
     */
    handleDelete() {
        this.startWorking();
        Api.Resources.ProductItems()
            .partialUpdate(this.state.product.id, {
                status: Api.Constants.ProductItems.Status.ARCHIVED
            })
            .then(() => {
                this.resetWorking();
                this.state.promise.resolve(true);
            }, () => {
                this.resetWorking();
                this.showErrorMessage('Unable to delete product');
            });
    };

    /**
     * Handle 'no' response
     */
    handleNo() {
        this.resetWorking();
        this.state.promise.resolve(null);
    };
}

module.exports = Delete;
