'use strict';

const Api = require('@ca-package/api');

const Confirm = require('@ca-submodule/modal').Confirm;

const content_tpl = require('@cam-company-profile-tpl/modals/term-condition/delete.hbs');


class Delete extends Confirm {
    constructor() {
        super();
        this.setTitle('Delete Terms & Conditions');
        this.setContent(content_tpl());
        this.elem.name = this.elem.content.fxFind('name');
        this.elem.type = this.elem.content.fxFind('type');
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {string} $0.bid_content_id
     * @param {string} $0.name
     * @param {string} $0.type
     * @param {object} $0.promise
     * @returns {Modal}
     */
    open({bid_content_id, name, type, promise}) {
        this.state.bid_content_id = bid_content_id;
        this.elem.name.text(name);
        this.elem.type.text(type);
        this.state.promise = promise;
        return super.open();
    };

    /**
     * Handle 'yes' response
     */
    handleYes() {
        this.startWorking();
        Api.Resources.BidContent()
            .partialUpdate(this.state.bid_content_id, {
                status: Api.Constants.BidContent.Status.ARCHIVED
            })
            .then(() => {
                this.resetWorking();
                this.state.promise.resolve(true);
            }, (error) => {
                let message = 'Unable to delete terms & conditions item';
                if (error.code === 1009) { //forbidden
                    message = 'Cannot delete a terms & conditions attached to a product';
                }
                this.showErrorMessage(message);
                this.resetWorking();
            });
    };

    /**
     * Handle 'no' response
     */
    handleNo() {
        this.resetWorking();
        this.state.promise.resolve(null);
    };
}

module.exports = Delete;
