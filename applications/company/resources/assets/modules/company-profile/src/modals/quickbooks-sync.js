'use strict';

require('datatables.net');
require('select2');

const Modal = require('@ca-submodule/modal').Legacy;

const modal_quickbooks_sync_tpl = require('@cam-company-profile-tpl/modals/integrations/quickbooks_sync.hbs');
const table_customer_option_tpl = require('@cam-company-profile-tpl/modals/integrations/customer_option.hbs');

const lang = require('lodash/lang');

module.exports = class extends Modal {
    constructor() {
        super(Modal.Size.LARGE, true, modal_quickbooks_sync_tpl());
        this.elem.close = this.elem.root.find('.close-settings');
        this.elem.submit_btn = this.elem.root.find('.button.save');
        this.elem.loading = this.elem.root.find('.modal-loading');
        this.elem.close = this.elem.root.find('.close-sync');
        this.table = this.elem.root.find('table');
        this.data_tables = '';
        this.customer_options = '';
        this.opened = false;
        this.paginate_buttons = null;

        super.on('close', () => {
            this.opened = false;
        });

        this.elem.close.on('click.fx', (e) => {
            e.preventDefault();
            this.close();
            return false;
        });

        this.table.on('change.fx', 'select.fx-customer', (e) => {
            e.preventDefault();
            this.save(e.target);
            return false;
        });

        this.table.on('click.fx', '.remove-sync.show', (e) => {
            e.preventDefault();
            this.remove(e.target);
            return false;
        });
    };

    buildSelect() {
        return '<select class="fx-customer"><option></option>' + this.customer_options + '</select><span class="button-loading table"></span><span class="remove-sync">x</span>';
    };

    build() {
        if ($.fn.DataTable.isDataTable(this.table) !== true) {
            this.data_tables = this.table.DataTable({
                ajax: {
                    url: window.fx_url.API + 'integration/quickbooks/customers',
                    dataType: "json",
                    type: "GET"
                },
                serverSide: true,
                pagingType: 'simple_numbers',
                columns: [
                    {
                        data: 'id',
                        visible: false
                    },
                    {
                        data: 'name',
                        orderable: false
                    },
                    {
                        data: 'address',
                        orderable: false
                    },
                    {
                        //defaultContent: this.buildSelect(),
                        render: (data, type, row) => {
                            return this.buildSelect();
                        },
                        orderable: false
                    }
                ],
                paging: true,
                info: true,
                pageLength: 50,
                searching: false,
                processing: true,
                autoWidth: false,
                language: {
                    processing: '<img src="' + window.fx_url.assets.IMAGE + 'ajax-loader.gif">'
                },
                initComplete: (settings, json) => {
                    this.paginate_buttons = this.elem.root.find('.paginate_button');
                },
                createdRow: (row, data, dataIndex) => {
                    $(row).find('select.fx-customer').select2({
                        placeholder: 'Select an option',
                        width: "90%"
                    });

                    let row_item = $(row).find(`select.fx-customer option[quickbooks="${data.id}"]`);

                    if (row_item.length > 0) {
                        row_item.prop('selected', true).trigger('change');
                        $(row).find('.remove-sync').addClass('show');
                    }
                }
            });
            this.data_tables.on('page.dt', () => {
                this.paginate_buttons.addClass('loading');
            });
            this.data_tables.on('draw.dt', () => {
                if (!lang.isNull(this.paginate_buttons)) {
                    this.paginate_buttons.removeClass('loading');
                    this.paginate_buttons = this.elem.root.find('.paginate_button');
                }
                this.fire('load');
                if (this.opened === false) {
                    this.open();
                }
                this.elem.root.scrollTop(0);
            });
        } else {
            this.data_tables.ajax.reload();
        }
    };

    load(reload) {
        $.ajax({
            url: window.fx_url.BASE + 'getCustomers.php',
            dataType: "json",
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            success: (response) => {

                if (!lang.isNull(response)) {

                    this.customer_options = '';

                    for (let item in response) {
                        let this_item = response[item];
                        if (lang.isNull(this_item.quickbooksID)) {
                            this_item.quickbooksID = '';
                        }
                        this.customer_options += table_customer_option_tpl({
                            quickbooksID: this_item.quickbooksID,
                            customerID: this_item.customerID,
                            firstName: this_item.firstName,
                            lastName: this_item.lastName,
                            address: this_item.ownerAddress
                        });
                    }

                    if (!reload) {
                        this.build();
                    }
                } else {
                    this.fire('empty');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });
    };

    save(element) {
        let $element = $(element);
        let customerID = $element.val();

        if (customerID !== '') {
            let row = $element.parents('tr');
            let loader = $element.parent().find('.button-loading');

            let data = this.data_tables.row(row).data();

            loader.addClass('show');

            $.ajax({
                url: window.fx_url.API + 'company/profile/sync-quickbooks',
                dataType: "json",
                type: "POST",
                data: {
                    customerID: customerID,
                    quickbooksID: data.id
                },
                contentType: "application/x-www-form-urlencoded",
                success: (response) => {
                    loader.removeClass('show');
                    row.find('.remove-sync').addClass('show');
                    this.load(true);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                    console.log(jqXHR.responseText);
                }
            });
        }
    };

    remove(element) {
        let $element = $(element);

        let row = $element.parents('tr');
        let customerID = row.find('select.fx-customer').val();
        let loader = $element.parent().find('.button-loading');
        let data = this.data_tables.row(row).data();
        let removeSyncButton = row.find('.remove-sync');

        loader.addClass('show');
        removeSyncButton.addClass('disabled');

        $.ajax({
            url: window.fx_url.API + 'company/profile/delete-quickbooks',
            dataType: "json",
            type: "POST",
            data: {
                customerID: customerID,
                quickbooksID: data.id
            },
            contentType: "application/x-www-form-urlencoded",
            success: (response) => {
                loader.removeClass('show');
                removeSyncButton.removeClass('disabled').removeClass('show');
                row.find('select.fx-customer').val('').trigger('change');
                this.load(true);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
                console.log(jqXHR.responseText);
            }
        });
    };

    open() {
        super.open();
        this.opened = true;
    };
};
