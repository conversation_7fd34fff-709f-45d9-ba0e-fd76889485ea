'use strict';

const Modal = require('@ca-submodule/modal').Legacy;
const Api = require('@ca-package/api');

const lang = require('lodash/lang');

module.exports = class extends Modal {
    constructor(modal) {
        super(Modal.Size.TINY, false, modal);

        this.elem.title = this.elem.root.find('.modal_title');
        this.elem.form_elements = this.elem.root.find('input, textarea');
        this.elem.form = this.elem.root.find('form');

        this.elem.file = this.elem.root.find('input[type="file"]');

        this.elem.close_media = this.elem.root.find('.close-media');

        this.resource = Api.Resources.Media;

        this.form = this.elem.form.parsley().on('form:submit', () => {
            this.save();
            return false;
        }).on('form:validate', () => {
            this.elem.form.find('.parsley-errors-list [class^="parsley-fx-"]').remove();
        });

        let fields = {
            name: {
                required: true,
                maxlength: 200,
                maxlengthMessage: 'Invalid length - 200 chars. max'
            },
            description: {
                maxlength: 5000,
                maxlengthMessage: 'Invalid length - 5000 chars. max'
            },
            file: {
                required: true,
                maxFileSize: 100,
                fileTypes: {
                    'audio/mp3': 'mp3',
                    'audio/mpeg': 'mp3',
                    'audio/ogg': 'oga',
                    'audio/wav': 'wav',
                    'image/jpeg': 'jpeg',
                    'image/png': 'png',
                    'image/gif': 'gif',
                    'video/ogg': 'ogv',
                    'video/mp4': 'mp4',
                    'video/m4v': 'm4v',
                    'video/quicktime': 'mov',
                    'video/x-ms-wmv': 'wmv',
                    'video/x-msvideo': 'avi',
                    'video/mpeg': 'mpeg',
                    'video/webm': 'webm',
                    'application/pdf': 'pdf',
                    'application/msword': 'doc',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
                    'application/vnd.ms-excel': 'xls',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
                    'application/vnd.ms-powerpoint': 'ppt',
                    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
                    'application/vnd.ms-powerpoint.slideshow.macroenabled.12': 'ppsm',
                    'application/vnd.openxmlformats-officedocument.presentationml.slideshow': 'ppsx',
                    'text/csv': 'csv',
                    'text/plain': 'txt'
                }
            }
        };
        this.field = {};
        for (let item in fields) {
            if (lang.isUndefined(fields[item].requiredMessage)) {
                fields[item].requiredMessage = 'This value is required.';
            }
            this.field[item] = this.elem.form_elements.filter(`[name="${item}"]`).parsley(fields[item]);
        }
    };

    getApiRequest() {
        return this.resource();
    };

    formReset() {
        this.elem.form.find('.parsley-errors-list [class^="parsley-fx-"]').remove();
        this.form.reset();
    };

    clearForm() {
        for (let item of this.elem.form_elements) {
            let this_item = $(item);

            switch(this_item.attr('type')) {
                case 'submit':
                    break;
                default:
                    this_item.val('');
            }

        }
        this.elem.root.scrollTop(0);
        this.formReset();
    };

    getData() {
        let data = {};

        for (let item of this.elem.form_elements) {
            let this_item = $(item);

            if (lang.isString(this_item.attr('name'))) {
                switch(this_item.attr('type')) {
                    case 'submit':
                        break;
                    case 'file':
                        break;
                    default:
                        data[this_item.attr('name')] = this_item.val();
                }
            }
        }
        return data;
    };
};
