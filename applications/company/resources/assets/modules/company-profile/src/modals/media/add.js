const MediaModal = require('../media');

const modal_media_tpl = require('@cam-company-profile-tpl/modals/media/media.hbs');

module.exports = class extends MediaModal {
    constructor() {
        super(modal_media_tpl());

        this.elem.close_media.on('click.fx', (e) => {
            e.preventDefault();
            this.clearForm();
            this.close();
            return false;
        });
    };

    open() {
        this.elem.title.html('Add Media');
        super.open();
    }

    save() {
        this.formReset();
        this.fire('loading');
        let data = this.getData();

        this.getApiRequest().file(this.elem.file[0].files[0]).store(data).then((entity, response) => {
            this.fire('redraw');
            this.fire('loaded');
            super.close();
            this.clearForm();
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    for (let item in item_errors) {
                        this.field[item].addError('fx-'+item, {message: item_errors[item]});
                    }
                    this.fire('loaded');
                    break;
                default:
                    this.fire('loaded');
                    super.close();
                    this.fire('error', error);
                    this.clearForm();
                    break;
            }
        });
    };
};
