const MediaModal = require('../media');

const lang = require('lodash/lang');

const modal_media_tpl = require('@cam-company-profile-tpl/modals/media/media.hbs');

module.exports = class extends MediaModal {
    constructor() {
        super(modal_media_tpl());

        this.elem.form_elements.filter('[name="file"]').parsley({
            required: false
        });

        this.elem.close_media.on('click.fx', (e) => {
            e.preventDefault();
            this.clearForm();
            this.close();
            return false;
        });
    };

    open(id) {
        if (lang.isNull(id)) {
            throw new Error('Media id is not defined.');
        }
        this.getApiRequest().retrieve(id).then((entity, response) => {
            let data = entity.get();

            for (let item of this.elem.form_elements) {
                let this_item = $(item);

                switch(this_item.attr('type')) {
                    case 'submit':
                        break;
                    default:
                        this_item.val(data[this_item.attr('name')]);
                }
            }

            this.fire('loaded');
            this.elem.title.html('Edit Media');
            super.open();
            this.elem.root.scrollTop(0);
        }, (error) => {
            this.fire('error', error);
        });
    }

    save() {
        this.formReset();
        this.fire('loading');
        let data = this.getData();

        let request = this.getApiRequest();
        if (this.elem.file[0].files.length > 0) {
            request.file(this.elem.file[0].files[0]);
        }

        request.partialUpdate(data['id'], data).then((entity, response) => {
            this.fire('redraw');
            this.fire('loaded');
            super.close();
            this.clearForm();
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    for (let item in item_errors) {
                        if (!lang.isUndefined(this.field[item])) {
                            this.field[item].addError('fx-'+item, {message: item_errors[item]});
                        }
                    }
                    this.fire('loaded');
                    break;
                default:
                    this.fire('loaded');
                    super.close();
                    this.fire('error', error);
                    this.clearForm();
                    break;
            }
        });
    };
};
