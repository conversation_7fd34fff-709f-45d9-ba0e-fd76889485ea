'use strict';

const Modal = require('@ca-submodule/modal').Legacy;
const Api = require('@ca-package/api');

const tinymce = require('tinymce/tinymce');
require('tinymce/themes/silver/theme');
require('tinymce/models/dom/model');
require('tinymce/icons/default/icons');
require('@cac-js/utils/wysiwyg_icons');
require('tinymce/plugins/code');
require('tinymce/plugins/lists');
require('tinymce/plugins/link');

const lang = require('lodash/lang');

module.exports = class extends Modal {
    constructor(modal) {
        super(Modal.Size.LARGE, false, modal);

        this.elem.title = this.elem.root.find('.modal_title');
        this.elem.form_elements = this.elem.root.find('input, textarea, select');
        this.elem.form = this.elem.root.find('form');
        this.elem.content = this.elem.root.find('[name="content"]');
        this.elem.type_container = this.elem.root.find('.type-container');
        this.elem.type_display = this.elem.root.find('.type-display');
        this.elem.type_name = this.elem.root.find('.type_name');
        this.elem.is_required = this.elem.root.find('[name="is_required"]');
        this.elem.is_locked = this.elem.root.find('[name="is_locked"]');
        this.elem.is_default = this.elem.root.find('[name="is_default"]');

        this.elem.product_select = this.elem.root.find('select[name="product_items"]');
        this.elem.loading_products = this.elem.root.find('.loading.products');

        this.elem.close_term_condition = this.elem.root.find('.close-term-condition');

        e$(this.elem.root[0]).find('[data-tooltip]').foundation();

        this.elem.product_select.select2({
            placeholder: 'Choose product',
            ajax: {
                transport: (params, success, failure) => {
                    let request = Api.Resources.ProductItems()
                        .fields(['id', 'name'])
                        .filter('status', Api.Constants.ProductItems.Status.ACTIVE)
                        .sort('name', 'asc')
                        .page(params.data.page || 1).perPage(15);
                    if (params.data.term) {
                        request.search(params.data.term);
                    }
                    return request.all().then(success, failure);
                },
                delay: 250,
                dataType: 'json',
                processResults: (collection) => {
                    let results = [];
                    collection.entities.forEach((product) => {
                        results.push({
                            id: product.get('id'),
                            text: product.get('name')
                        });
                    });
                    return {
                        results: results,
                        pagination: {
                            more: collection.response.meta('pagination.next_page') !== null
                        }
                    };
                }
            }
        });

        this.resource = Api.Resources.BidContent;

        this.form = this.elem.form.parsley().on('form:submit', () => {
            this.save();
            return false;
        }).on('form:validate', () => {
            this.elem.form.find('.parsley-errors-list [class^="parsley-fx-"]').remove();
        });

        let fields = {
            name: {
                required: true,
                maxlength: 100,
                maxlengthMessage: 'Invalid length - 100 chars. max'
            },
            type: {
                required: true
            },
            content: {
                required: true
            },
            product_items: {
                valueEmpty: false,
                valueEmptyMessage: 'Products must be empty if content is marked required'
            }
        };
        this.field = {};
        for (let item in fields) {
            if (lang.isUndefined(fields[item].requiredMessage)) {
                fields[item].requiredMessage = 'This value is required.';
            }
            this.field[item] = this.elem.form_elements.filter(`[name="${item}"]`).parsley(fields[item]);
        }

        tinyMCE.baseURL = window.fx_url.assets.VENDOR + '/tinymce/';

        tinymce.init({
            target: this.elem.content[0],
            content_css: window.fx_url.assets.STYLE+'tinymce-content.css',
            icons: 'remix-icons',
            skin: false,
            browser_spellcheck: true,
            contextmenu: false,
            min_height: 240,
            link_assume_external_targets: 'http',
            link_default_target: '_blank',
            link_target_list: false,
            setup: function (editor) {
                editor.on('change', function () {
                    editor.save();
                });
                editor.ui.registry.addMenuButton('tags', {
                    text: 'Tags',
                    fetch: (callback) => {
                        let menu = [
                            {
                                text: 'Current Date',
                                type: 'menuitem',
                                onAction: function () {editor.insertContent('{date}');}
                            },
                            {
                                text: 'First Name',
                                type: 'menuitem',
                                onAction: function () {editor.insertContent('{firstName}');}
                            },
                            {
                                text: 'Last Name',
                                type: 'menuitem',
                                onAction: function() {editor.insertContent('{lastName}'); }
                            },
                            {
                                text: 'Business Name',
                                type: 'menuitem',
                                onAction: function() { editor.insertContent('{businessName}'); }
                            },
                            {
                                text: 'Address',
                                type: 'menuitem',
                                onAction: function() { editor.insertContent('{address}'); }
                            },
                            {
                                text: 'City',
                                type: 'menuitem',
                                onAction: function () { editor.insertContent('{city}'); }
                            },
                            {
                                text: 'State',
                                type: 'menuitem',
                                onAction: function() { editor.insertContent('{state}'); }
                            },
                            {
                                text: 'Zip',
                                type: 'menuitem',
                                onAction: function() { editor.insertContent('{zip}'); }
                            },
                            {
                                text: 'Phone',
                                type: 'menuitem',
                                onAction: function() { editor.insertContent('{phone}')}
                            },
                            {
                                text: 'Email',
                                type: 'menuitem',
                                onAction: function() { editor.insertContent('{email}')}
                            },
                            {
                                text: 'Bid Number',
                                type: 'menuitem',
                                onAction: function() { editor.insertContent('{bidNumber}')}
                            },
                            {
                                text: 'Project Description',
                                type: 'menuitem',
                                onAction: function() { editor.insertContent('{description}')}
                            }
                        ];
                        callback(menu);
                    }
                });
            },
            block_formats: 'Paragraph=p;Header 1=h3;Header 2=h4;Header 3=h5',
            anchor_top: false,
            anchor_bottom: false,
            branding: false,
            height: 300,
            plugins: [
                'code', 'lists', 'link'
            ],
            toolbar1: 'tags | blocks | bold italic underline | bullist numlist | link | code',
            menubar: false,
            elementpath: false,
            toolbar_items_size: 'small',
            relative_urls: false,
            remove_script_host: false,
            paste_remove_spans: true,
            paste_as_text: true
        });

        onEvent(findChild(content, this.elem.is_required), 'change', function(e) {
            e.preventDefault();
            if ($(e.target).is(':checked')) {
                this.elem.is_locked.prop('checked', true);
                this.elem.is_default.prop('checked', true);
                this.elem.is_default.prop('disabled', true);
            } else {
                this.elem.is_default.prop('disabled', false);
            }
            this.elem.products.val('').trigger('change');
            return false;
        });

        this.elem.is_required.on('change.fx', (e) => {
            if ($(e.target).is(':checked')) {
                this.elem.is_locked.prop('checked', true);
                this.elem.is_default.prop('checked', true);
                this.elem.is_default.prop('disabled', true);
                this.elem.form_elements.filter('[name="product_items"]').parsley({
                    valueEmpty: true
                });
            } else {
                this.elem.is_default.prop('disabled', false);
                this.elem.form_elements.filter('[name="product_items"]').parsley({
                    valueEmpty: false
                });
            }
        });
    };

    getApiRequest() {
        return this.resource();
    };

    formReset() {
        this.elem.form.find('.parsley-errors-list [class^="parsley-fx-"]').remove();
        this.form.reset();
    };

    clearForm() {
        for (let item of this.elem.form_elements) {
            let this_item = $(item);

            if (this_item.is('textarea')) {
                this_item.val('');
                tinymce.get(`${this.namespace}-term-condition`).setContent('');
            } else if (this_item.is('select')) {
                this_item.val('');
                if (this_item.attr('name') === 'product_items') {
                    this_item.empty().val(null).trigger('change');
                }
            } else {
                switch (this_item.attr('type')) {
                    case 'checkbox':
                        this_item.prop('checked', false);
                        this_item.prop('disabled', false);
                        break;
                    case 'submit':
                        break;
                    default:
                        this_item.val('');
                }
            }
        }
        this.elem.form_elements.filter('[name="product_items"]').parsley({
            valueEmpty: false
        });
        this.elem.root.scrollTop(0);
        this.elem.type_container.hide();
        this.formReset();
    };

    getData() {
        let data = {};

        for (let item of this.elem.form_elements) {
            let this_item = $(item);

            if (lang.isString(this_item.attr('name'))) {
                if (this_item.is('textarea')) {
                    data[this_item.attr('name')] = tinymce.get(`${this.namespace}-term-condition`).getContent();
                } else {
                    switch (this_item.attr('type')) {
                        case 'checkbox':
                            data[this_item.attr('name')] = this_item.is(':checked');
                            break;
                        case 'submit':
                            break;
                        default:
                            data[this_item.attr('name')] = this_item.val();
                    }
                }
            }
        }
        return data;
    };
};
