'use strict';

const Api = require('@ca-package/api');

const Confirm = require('@ca-submodule/modal').Confirm;

const content_tpl = require('@cam-company-profile-tpl/modals/emails/delete.hbs');

class Delete extends Confirm {
    constructor() {
        super();
        this.setTitle('Delete Email');
        this.setContent(content_tpl());
        this.elem.email = this.elem.content.fxFind('email');
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {string} $0.email_id
     * @param {object} $0.promise
     * @returns {Modal}
     */
    open({email_id, promise}) {
        this.startWorking();
        Api.Resources.EmailTemplates()
            .retrieve(email_id)
            .then(({data}) => {
                if (data.status !== Api.Constants.EmailTemplates.Status.ACTIVE) {
                    promise.resolve(null);
                    return;
                }
                this.state.email = data;
                this.elem.email.text(data.name);
                this.resetWorking();
            }, error => {
                this.showErrorMessage('Unable to fetch email info');
                console.log(error);
            });
        this.state.promise = promise;
        return super.open();
    };

    /**
     * Handle 'yes' response
     */
    handleYes() {
        this.startWorking();
        Api.Resources.EmailTemplates()
            .partialUpdate(this.state.email.id, {
                status: Api.Constants.EmailTemplates.Status.ARCHIVED
            })
            .then(() => {
                this.resetWorking();
                this.state.promise.resolve(true);
            }, (error) => {
                let message = 'Unable to delete email';
                this.showErrorMessage(message);
                this.resetWorking();
            });
    };

    /**
     * Handle 'no' response
     */
    handleNo() {
        this.resetWorking();
        this.state.promise.resolve(null);
    };
}

module.exports = Delete;
