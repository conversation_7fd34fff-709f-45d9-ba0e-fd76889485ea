"use strict";

const accounting = require("accounting");

const Api = require("@ca-package/api");
const { findChild, jsSelector } = require("@ca-package/dom");

const {
    createSuccessMessage,
} = require("@cas-notification-toast-js/message/success");
const {
    createErrorMessage,
} = require("@cas-notification-toast-js/message/error");
const {
    validateRecords,
} = require("@cam-company-profile-js/pages/main-pages/products-pages/import-pages/utils/validation");

const BaseReview = require("@cam-company-profile-js/pages/main-pages/products-pages/import-pages/base_review");

const final_review_tpl = require("@cam-company-profile-tpl/pages/main-pages/products-pages/import-pages/final_review.hbs");

/**
 * FinalReview Page
 *
 * Fourth and final step of the product import wizard.
 * Allows users to review validated records, edit or delete them, and submit the import.
 */
class FinalReview extends BaseReview {
    /**
     * Returns all imported products.
     */
    getProducts() {
        return Array.from(this.state.parent.state.import_data?.values() || []);
    };

    /**
     * Returns the columns configuration for the datatable.
     *
     * @returns {Object} Columns configuration object.
     */
    getTableColumns() {
        return {
            id: { label: "Id", width: "10%", visible: false },
            name: {
                label: "Name",
                width: "20%",
                value: (data) =>
                    data.name
                        ? this.state.table.trimColumn(data.name, 60, true, true)
                        : "",
            },
            price: {
                label: "Price",
                width: "20%",
                value: (data) => data.price && accounting.formatMoney(data.price),
            },
            unit: { label: "Unit", width: "10%" },
        };
    };

    /**
     * Returns the items to be displayed in the datatable.
     *
     * @returns {Array<object>} Array of product records to display.
     */
    getTableItems() {
        return this.getProducts();
    };

    /**
     * Re-validates and refreshes the datatable.
     */
    reloadTable() {
        this.state.parent.state.import_data = validateRecords(
            this.state.parent.state.import_data,
            this.parent.state.product_rules,
        );
        const invalid_records = this.getInvalidProducts();
        this.state.table.setTableData(this.getProducts());
        this.elem.records_queued_count.text(
            this.state.parent.state.import_data?.size - invalid_records?.length,
        );
    };

    /**
     * Submits valid products to the backend API for import.
     */
    importProducts() {
        try {
            this.parent.startWorking();

            const validProducts = [
                ...(this.parent.state.import_data?.values() || []),
            ].filter((record) => !record.error);

            if (!validProducts || validProducts.length === 0) {
                const message = createErrorMessage("No valid products to import");
                this.router.main_route.layout.toasts.addMessage(message);
                return;
            }

            const formattedProducts = validProducts.map((product) => ({
                id_user_defined: product.id_user_defined || "",
                name: product.name,
                description: product.description || "",
                price: product.price.toString(),
                unit: product.unit,
                pricing_disclaimer: product.pricing_disclaimer || "",
                category: product.category || "",
            }));

            // Prepare data for API
            const data = {
                products: formattedProducts,
            };

            // Send request to API using the Api client
            Api.Resources.ImportProducts()
                .timeout(60)
                .store(data)
                .then((response) => {
                    const stats = response.data.load_stats || {};
                    const { inserted = 0, updated = 0, errored = 0 } = stats;

                    const parts = [];
                    if (inserted > 0) {
                        parts.push(`${inserted} product(s) added`);
                    }
                    if (updated > 0) {
                        parts.push(`${updated} product(s) updated`);
                    }
                    if (errored > 0) {
                        parts.push(`${errored} product(s) with errors`);
                    }

                    this.router.navigate("products.items.manager");

                    this.parent.resetWorking();

                    setTimeout(() => {
                        const message = createSuccessMessage(
                            `Import successfully completed: ${parts.join(", ")}`,
                        );
                        this.router.main_route.layout.toasts.addMessage(message);
                    }, 500);
                })
                .catch(() => {
                    let errorMessage =
                        "Unexpected error occurred, please contact support.";
                    this.parent.resetWorking();

                    const message = createErrorMessage(errorMessage);
                    this.router.main_route.layout.toasts.addMessage(message);
                });
        } catch (error) {
            const message = createErrorMessage(
                "Unexpected error occurred, please contact support.",
            );
            this.router.main_route.layout.toasts.addMessage(message);
            this.parent.resetWorking();
        }
    };

    /**
     * Loads the final review page and initializes or redraws the product table.
     */
    async load(request, next) {
        await super.load(request, next);

        if (!(this.state.parent.state.import_data?.size > 0)) {
            this.router.navigate("products.import.upload");
            return;
        }

        const invalid_records = this.getInvalidProducts();
        this.elem.errors_count.text(invalid_records.length);
        this.elem.records_queued_count.text(
            this.state.parent.state.import_data?.size - invalid_records?.length,
        );
        this.state.child_row_open = false;
        if (!this.state.table_loaded) {
            this.createTable();
        } else {
            this.state.table.draw();
        }
    };

    /**
     * Initializes the page DOM elements and bind the import button.
     */
    boot(root) {
        super.boot(root);
        this.elem.table = findChild(root, jsSelector("final-review-table"));
        this.elem.errors_count = findChild(root, jsSelector("errors-count"));
        this.elem.records_queued_count = findChild(
            root,
            jsSelector("records-queued-count"),
        );
        this.elem.import_button = findChild(root, jsSelector("import-button"));

        this.elem.import_button.on("click", () => {
            this.importProducts();
        });
    };

    /**
     * Renders the final review HTML template.
     */
    render() {
        return final_review_tpl({
            cancel_route: "products.import.upload",
            finish_route: "products.import.finish",
        });
    };
}

module.exports = FinalReview;
