/**
 * Validates a Map of records using Parsley rules.
 *
 * @param {Map<number, Object>} recordsMap - Map of records indexed by ID or array index.
 * @param {Object<string, Object>} rules - Parsley validation rules for each field.
 * @returns {Map<number, Object>} A new Map with each record, possibly including an `error` field.
 */
function validateRecords(recordsMap = new Map(), rules = {}) {
    /**
     *
     */
    function camelToDash(str) {
        return str.replace(/([a-z])([A-Z])/g, "$1-$2").toLowerCase();
    }

    const form = document.createElement("form");
    const fieldMap = {};

    for (const [fieldName, fieldRules] of Object.entries(rules)) {
        const el = document.createElement("input");
        el.name = fieldName;

        for (const [ruleKey, val] of Object.entries(fieldRules)) {
            const attr = ruleKey.startsWith("data-")
                ? ruleKey
                : `data-parsley-${camelToDash(ruleKey)}`;
            el.setAttribute(
                attr,
                ruleKey === "required" && val === true ? "true" : val,
            );
        }

        form.appendChild(el);
        fieldMap[fieldName] = el;
    }

    const parsley = $(form).parsley({ excluded: "" });

    const validatedMap = new Map();

    for (const [key, record] of recordsMap.entries()) {
        for (const [field, input] of Object.entries(fieldMap)) {
            input.value = record[field] ?? "";
        }

        const ok = parsley.validate();

        let error = null;
        if (!ok) {
            error = {};
            parsley.fields.forEach((f) => {
                if (!f.isValid()) {
                    error[f.$element.attr("name")] = f.getErrorsMessages();
                }
            });
        }
        validatedMap.set(key, { ...record, error });
    }

    return validatedMap;
}

export { validateRecords };
