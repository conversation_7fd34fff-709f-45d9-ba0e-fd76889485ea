'use strict';

const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector} = require("@ca-package/dom");

const leads_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/leads.hbs');

class Leads extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent
        });
    };

    /**
     * Get available routes
     *
     * @readonly
     *
     * @returns {object}
     */
    static get routes() {
        return {
            details: {
                default: true,
                path: '/',
                page: require('./leads-pages/details')
            },
            edit: {
                path: '/edit',
                page: require('./leads-pages/edit')
            }
        };
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.page = findChild(root, jsSelector('leads-pages'));
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return leads_tpl();
    };
}

module.exports = Leads;