'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector} = require("@ca-package/dom");
const Tooltip = require('@ca-submodule/tooltip');

const reminders_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/reminders-pages/details.hbs');

class Details extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            settings: parent.getParentByName('settings')
        });
    };

    /**
     * Populate data on page
     *
     * @param {Object} data
     */
    populate(data) {
        let summary = '',
            follow_ups = data.settings.bid_follow_up_notifications_config;
        if (data.settings.bid_follow_up_notifications_config === null) {
            summary = 'None';
        } else {
            let active_count = 0,
                inactive_count = 0;
            for (let item of follow_ups) {
                if (item.is_enabled) {
                    ++active_count;
                    continue;
                }
                ++inactive_count;
            }
            if (active_count > 0) {
                summary = `${active_count} Active`
            }
            if (inactive_count > 0) {
                if (summary !== '') {
                    summary = `${summary}, `
                }
                summary = `${summary}${inactive_count} Inactive`
            }
        }

        let bid_follow_up_statuses = new Map([
            [false, '<span class="h-text t-grey">Disabled</span>'],
            [true, '<span class="h-text t-green">Enabled</span>']
        ]);

        let template = '<div class="f-field t-status"><label class="f-f-label">Status</label></div>';
        this.elem.bid_follow_up_notifications_status.html(`${template}${bid_follow_up_statuses.get(data.settings.bid_follow_up_notifications)}`);
        this.elem.bid_follow_up_notifications_summary.text(summary);
    };

    /**
     * Fetch settings data from server
     */
    async fetchData() {
        try {
            let {data: entity} = await Api.Resources.Companies().
            fields(['id']).relations({
                'settings': {}
            }).retrieve('current');
            this.populate(entity);
        } catch (e) {}
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    async refresh(request) {
        await this.fetchData();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (!profile_data.features.bid_follow_ups) {
            this.router.redirect('settings.email.details');
        }
        this.state.settings.showLoader();
        await this.fetchData();
        this.state.settings.hideLoader();
        await super.load(request, next);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.elem.bid_follow_up_notifications_status.text('');
        this.elem.bid_follow_up_notifications_summary.text('');
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.bid_follow_up_notifications_status = findChild(root, jsSelector('bid-follow-up-notifications-status'));
        this.elem.bid_follow_up_notifications_summary = findChild(root, jsSelector('bid-follow-up-notifications-summary'));
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return reminders_tpl({
            title: 'Reminder Settings'
        });
    };
}

module.exports = Details;