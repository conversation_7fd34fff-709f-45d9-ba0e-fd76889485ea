'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector} = require("@ca-package/dom");
const Tooltip = require('@ca-submodule/tooltip');

const text_messaging_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/text-messaging-pages/details.hbs');

class Details extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            settings: parent.getParentByName('settings')
        });
    };

    /**
     * Populate data on page
     *
     * @param {Object} data
     */
    populate(data) {
        let text_messaging_status = new Map([
            [false, '<span class="h-text t-grey">Disabled</span>'],
            [true, '<span class="h-text t-green">Enabled</span>']
        ]);

        let recipients = data.settings.text_message_delivery_failure_email_recipients;
        if (recipients === undefined || recipients.length === 0) {
            recipients = '<em>None</em>';
        } else {
            recipients = recipients.join(', ');
        }

        if (data.settings.text_messaging_enabled === undefined) {
            this.elem.text_messaging_enabled.html(text_messaging_status.get(false));
        } else {
            this.elem.text_messaging_enabled.html(text_messaging_status.get(data.settings.text_messaging_enabled));
        }
        this.elem.delivery_failure_email_recipients.html(recipients);
    };

    /**
     * Fetch settings data from server
     */
    async fetchData() {
        try {
            let {data: entity} = await Api.Resources.Companies().
            fields(['id']).relations({
                'settings': {}
            }).retrieve('current');
            this.populate(entity);
        } catch (e) {}
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    async refresh(request) {
        await this.fetchData();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (!profile_data.features.text_messaging) {
            this.router.redirect('settings.email.details');
        }
        this.state.settings.showLoader();
        await this.fetchData();
        this.state.settings.hideLoader();
        await super.load(request, next);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.elem.text_messaging_enabled.text('');
        this.elem.delivery_failure_email_recipients.text('');

        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.text_messaging_enabled = findChild(root, jsSelector('text-messaging-enabled'));
        this.elem.delivery_failure_email_recipients = findChild(root, jsSelector('delivery-failure-email-recipients'));
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return text_messaging_tpl({
            title: 'Text Messaging Settings',
            edit_route: 'settings.text_messaging.edit'
        });
    };
}

module.exports = Details;