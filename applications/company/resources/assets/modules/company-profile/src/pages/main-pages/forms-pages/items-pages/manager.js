'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const Table = require('@ca-submodule/table').Base;

const manager_tpl = require('@cam-company-profile-tpl/pages/main-pages/forms-pages/items-pages/manager.hbs');
const moment = require("moment-timezone");

class Manager extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            table: null,
            table_scope: {
                sorts: {
                    name: Table.Sort.ASC
                }
            }
        });
    };

    /**
     * Get available routes
     *
     * @readonly
     *
     * @returns {object}
     */
    static get routes() {
        return {
            delete: {
                path: '/delete/{form_id}',
                modal: require('./manager-pages/delete'),
                bindings: {
                    form_id: 'uuid'
                }
            }
        };
    };

    /**
     * Create the DataTable and apply settings and defaults
     */
    createTable() {
        this.state.table = new Table(this.elem.table)
            .on('row_click', (data) => {
                this.router.navigate('forms.items.update', {form_id: data.id});
            });

        // set header config
        this.state.table.setHeader({
            custom_search: true,
            search: true,
            search_placeholder: 'Search',
            filter_name: 'Forms'
        });

        this.state.table.setFilterOptions({
            is_active: {
                label: 'Status',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: {
                    1: {
                        label: 'Active',
                        value: 1
                    },
                    2: {
                        label: 'Inactive',
                        value: 0
                    }
                }
            },
            created_at: {
                label: 'Created Date',
                type: Table.FilterValueTypes.DATE,
            },
            updated_at: {
                label: 'Updated Date',
                type: Table.FilterValueTypes.DATE,
            }
        });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        let status_map = new Map([
            [0, '<span class="h-text t-grey">Inactive</span>'],
            [1, '<span class="h-text t-green">Active</span>'],
        ]);

        // set columns config
        this.state.table.setColumns({
            name: {
                label: 'Name',
                responsive: 1
            },
            category_list: {
                label: 'Categories',
                value: (data) => {
                    let category_list = data.category_list;
                    if (category_list === null) {
                        return '---';
                    }
                    return category_list;
                }
            },
            is_active: {
                label: 'Status',
                value: (data) => {
                    return status_map.get(data.is_active);
                }
            },
            created_at: {
                label: 'Created',
                value: (data) => {
                    let date = data.created_at;
                    if (date === null) {
                        return '';
                    }
                    return moment(date).tz(this.router.main_route.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            updated_at: {
                label: 'Updated',
                value: (data) => {
                    let date = data.updated_at;
                    if (date === null) {
                        return '';
                    }
                    return moment(date).tz(this.router.main_route.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            }
        });

        // set buttons config
        let buttons = {
            organize: {
                label: 'Form Filters',
                action: () => this.router.navigate('forms.categories'),
                type_class: 't-tertiary-icon',
                icon: 'system--list-settings-line'
            }
        };
        if (window.profile_data.features.marketplace) {
            buttons.add = {
                label: 'New Form',
                action: () => this.router.externalRedirect(window.fx_pages.FORM_MARKETPLACE),
                type_class: 't-primary'
            };
        }
        this.state.table.setButtons(buttons);

        // set row action config
        this.state.table.setRowActions({
            edit: {
                label: 'Edit',
                action: data => this.router.navigate('forms.items.update', {form_id: data.id})
            },
            duplicate: {
                label: 'Duplicate',
                action: data => this.router.navigate('forms.items.duplicate', {form_id: data.id})
            },
            delete: {
                label: 'Delete',
                negate: true,
                action: data => this.router.navigate('forms.items.manager.delete', {form_id: data.id})
            }
        });

        this.state.table.setAjax(Api.Resources.CompanyFormItems, (request) => {
            request.fields(['id', 'category_list', 'name'])
                .filter('status', Api.Constants.CompanyFormItems.Status.ACTIVE)
                .accept('application/vnd.adg.fx.collection-v1+json');
        });

        // modify table state
        if (this.state.table_scope) {
            this.state.table.setState(this.state.table_scope);
        }
    };

    /**
     * Build or draw table
     */
    loadTable() {
        this.createTable();
        this.state.table.setState(this.state.table_scope);
        this.state.table.build();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        let forms = this.getParentByName('forms');
        forms.showLoader();
        await super.load(request, next);
        this.loadTable();
        forms.hideLoader();
    };

    /**
     * Refresh page
     *
     * @param request
     */
    refresh(request) {
        this.state.table.draw();
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        // We have to force the filter to hide if it's still open when they unload
        this.state.table.hideFilterMenu();

        this.state.table.destroy();
        await super.unload(request, next);
    };

    /**
     * Boot page
     */
    boot(root) {
        super.boot(root);
        this.elem.table = this.elem.root.fxFind('table-container');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return manager_tpl();
    };
}

module.exports = Manager;
