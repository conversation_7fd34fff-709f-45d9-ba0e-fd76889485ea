'use strict';

const moment = require('moment-timezone');
const tinymce = require('tinymce/tinymce');
require('tinymce/themes/silver/theme');
require('tinymce/models/dom/model');
require('tinymce/icons/default/icons');
require('@cac-js/utils/wysiwyg_icons');
require('tinymce/plugins/code');
require('tinymce/plugins/image');
require('tinymce/plugins/lists');
require('tinymce/plugins/link');

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');

const cover_letter_tpl = require('@cam-company-profile-tpl/pages/main-pages/warranties-pages/cover_letter.hbs');

class CoverLetter extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);

        Object.assign(this.state, {
            data: {},
            modals: {},
            company: false,
            editor: null
        });
    };

    /**
     * Populate data on page
     */
    populate() {
        if (this.state.company) {
            tinymce.get('finalPacketCoverLetter').setContent(this.state.data.company.cover_letter);

            if (this.state.data.company.cover_letter_last_updated !== null) {
                let last_saved_utc = moment.tz(this.state.data.company.cover_letter_last_updated, 'UTC');
                let last_saved = last_saved_utc.clone().tz(moment.tz.guess()).format('M/D/YY h:mm a');
                this.elem.last_saved.text(`Last Saved: ${last_saved}`);
            }
        }
    };

    /**
     * Load company information from server
     */
    async loadCompany() {
        try {
            let {data: entity} = await Api.Resources.Companies()
                .fields([
                    'id', 'name', 'address', 'address_2', 'city', 'state', 'zip', 'email_from', 'website', 'cover_letter',
                    'logo_file_id', 'cover_letter_last_updated'
                ])
                .relations({
                    'phones': {},
                    'logo_media_urls': {}
                })
                .retrieve('current');
            this.state.company = true;
            this.state.data.company = entity;
            this.populate();
        } catch (e) {
            this.state.modals.error.setError(e);
            this.state.modals.error.open();
        }

    };

    /**
     * Clear page
     */
    clear() {
        this.state.company = false;
        this.elem.cover_letter.val('');
        this.elem.last_saved.empty();
    };

    /**
     * Fetch necessary data
     */
    async fetchData() {
        await Promise.all([this.loadCompany()]);
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    refresh(request) {
        this.clear();
        this.fetchData();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.elem.loader.show();
        await super.load(request, next);
        await this.loadWysiwyg();
        await this.fetchData();
        this.elem.loader.hide();
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.clear();
        await super.unload(request, next);
    };

    async loadWysiwyg () {
        if (this.state.editor === null) {
            this.state.editor = await new Promise((resolve) => {
                let that = this;
                tinyMCE.baseURL = window.fx_url.assets.VENDOR + '/tinymce/';
                tinymce.init({
                    target: this.elem.cover_letter[0],
                    content_css: window.fx_url.assets.STYLE + 'tinymce-content.css',
                    icons: 'remix-icons',
                    skin: false,
                    browser_spellcheck: true,
                    contextmenu: false,
                    min_height: 240,
                    link_assume_external_targets: 'http',
                    link_default_target: '_blank',
                    link_target_list: false,
                    relative_urls: false,
                    remove_script_host: false,
                    paste_remove_spans: true,
                    paste_as_text: true,
                    setup: function (editor) {
                        editor.on('init', () => {
                            resolve(editor);
                        });
                        that.elem.cover_letter_save.on('click', function (e) {
                            let coverLetterText = tinyMCE.get('finalPacketCoverLetter').getContent();
                            that.elem.cover_letter.html(coverLetterText);
                            let form_error = that.elem.cover_letter.parent().find('.form-error');
                            form_error.removeClass('is-visible');

                            if (coverLetterText == '') {
                                form_error.addClass('is-visible');
                            } else {
                                let data = {
                                    cover_letter: coverLetterText
                                };

                                Api.Resources.Companies().partialUpdate('current', data).then((entity, response) => {
                                    let last_saved_utc = moment.tz(entity.data['cover_letter_last_updated'], 'UTC');
                                    let last_saved = last_saved_utc.clone().tz(moment.tz.guess()).format('M/D/YY h:mm a');
                                    that.elem.last_saved.text(`Last Updated on ${last_saved}`);

                                    e$('#coverLetterSavedModal').foundation('open');

                                }, (error) => {

                                });
                            }
                        });
                        editor.ui.registry.addMenuButton('tags', {
                            text: 'Tags',
                            fetch: (callback) => {
                                let menu = [
                                    {
                                        text: 'Customer First Name',
                                        type: 'menuitem',
                                        onAction: function () {
                                            editor.insertContent('{customerFirstName}');
                                        }
                                    },
                                    {
                                        text: 'Customer Last Name',
                                        type: 'menuitem',
                                        onAction: function () {
                                            editor.insertContent('{customerLastName}');
                                        }
                                    },
                                    {
                                        text: 'Customer Business Name',
                                        type: 'menuitem',
                                        onAction: function () {
                                            editor.insertContent('{customerBusinessName}');
                                        }
                                    },
                                    {
                                        text: 'Property Address',
                                        type: 'menuitem',
                                        onAction: function () {
                                            editor.insertContent('{propertyAddress}');
                                        }
                                    },
                                    {
                                        text: 'Evaluation Name',
                                        type: 'menuitem',
                                        onAction: function () {
                                            editor.insertContent('{evaluationName}')
                                        }
                                    }
                                ];
                                callback(menu);
                            }
                        });
                        editor.ui.registry.addButton('preview', {
                            icon: 'preview',
                            onAction: () => {
                                let finalPacketCoverLetter = tinyMCE.get('finalPacketCoverLetter').getContent();

                                finalPacketCoverLetter = finalPacketCoverLetter.replace(/{customerFirstName}/gi, 'John');
                                finalPacketCoverLetter = finalPacketCoverLetter.replace(/{customerLastName}/gi, 'Sample');
                                finalPacketCoverLetter = finalPacketCoverLetter.replace(/{customerBusinessName}/gi, 'Business Name');
                                finalPacketCoverLetter = finalPacketCoverLetter.replace(/{propertyAddress}/gi, 'Installation Street Address, Cityville, MO 1234');
                                finalPacketCoverLetter = finalPacketCoverLetter.replace(/{evaluationName}/gi, 'evaluation name');

                                let companyLogo = '';
                                if (that.state.data.company.logo_file_id != null) {
                                    companyLogo = `<img src="${that.state.data.company.logo_media_urls['profile_thumbnail']}"><br>`;
                                }

                                let companyAddress2 = '';
                                if (that.state.data.company.address_2 != null) {
                                    companyAddress2 = ', ' + that.state.data.company.address_2;
                                }
                                let companyAddress = that.state.data.company.address + companyAddress2 + ', ' + that.state.data.company.city + ', ' + that.state.data.company.state + ' ' + that.state.data.company.zip;

                                //March 7, 2018
                                let todays_date = moment().format("MMMM D, YYYY");

                                let companyPhones = '';
                                for (let phone in that.state.data.company.phones) {
                                    let this_phone = that.state.data.company.phones[phone];
                                    let primary = '';
                                    if (this_phone.is_primary === true) {
                                        primary = ' <span>primary</span>';
                                    }
                                    companyPhones += this_phone.description + ': ' + this_phone.number + primary + '<br/>';
                                }

                                let PreviewHtml =
                                    '<body style=\" font-family: sans-serif;\">' +
                                    '<p style=\"width:100%; padding:5px 45px;\">' + companyLogo + '</p>' +
                                    '<p style=\"width:100%; padding:5px 45px;\">' + todays_date + '</p>' +
                                    '<p style=\"width:100%; padding:5px 45px;\">John Sample <br/>' +
                                    '1234 Some Street,<br />' +
                                    'Cityville, MO 12345' + '</p>' +
                                    '<div class=\"row\" style=\"padding:5px 44px;\"><p>' +
                                    finalPacketCoverLetter +
                                    '</p></div>' +
                                    '<p style=\"width:100%; padding:5px 45px; text-align:center;\" >' +
                                    '<strong>' + that.state.data.company.name + '</strong> | ' + companyAddress + '<br/>' +
                                    companyPhones +
                                    '</p></body></html>';

                                let pageHeight = $(window).height() * .85;

                                let cover_letter_preview_modal = e$('#coverLetterPreviewModal');

                                cover_letter_preview_modal.css('max-height', pageHeight);
                                cover_letter_preview_modal.find('#modalContent').html(PreviewHtml);
                                cover_letter_preview_modal.foundation('open');
                            }
                        });
                    },
                    branding: false,
                    height: 300,
                    plugins: ['code', 'lists', 'link', 'image'],
                    toolbar1: "tags | fontsize | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | image | link | code | preview",
                    font_size_formats: '8pt 9pt 10pt 11pt 12pt 14pt 18pt 24pt 36pt',
                    image_description: false,
                    menubar: false,
                    elementpath: false,
                    toolbar_items_size: 'small',
                });
            });
        }
        return this.state.editor;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    async boot(root) {
        super.boot(root);

        e$(this.elem.root[0]).foundation();

        this.elem.cover_letter = this.elem.root.find('#finalPacketCoverLetter');
        this.elem.cover_letter_save = this.elem.root.find('#saveFinalPacketCoverLetter');
        this.elem.last_saved = this.elem.root.find('#finalPacketCoverLetterLastSaved');
        this.elem.loader = this.elem.root.fxFind('loader');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return cover_letter_tpl();
    };
}

module.exports = CoverLetter;
