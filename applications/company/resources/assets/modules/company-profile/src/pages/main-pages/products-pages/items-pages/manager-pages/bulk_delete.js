'use strict';

const Modal = require('@ca-package/router/src/modal');
const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');

class BulkDelete extends Modal {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            product_ids: []
        });
    };

    /**
     * Get and cache bulk delete product modal
     *
     * @returns {module:Modal.Confirm}
     */
    get modal() {
        if (this.state.modal === undefined) {
            let modal = require('../../../../../modals/product/bulk_delete');
            this.state.modal = new modal(this);
        }
        return this.state.modal;
    };

    /**
     * Open bulk delete product modal with promise
     *
     * @param {array} product_ids - uuid
     * @returns {Promise<undefined>}
     */
    openModal(product_ids) {
        return new Promise((resolve, reject) => {
            return this.modal.open({
                product_ids: product_ids,
                promise: {resolve, reject}
            });
        });
    };

    /**
     * Load modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
        this.state.product_ids = request.query.product_ids.split(',');
        this.openModal(this.state.product_ids).then((result) => {
            if (result === null) { // no action was taken
                this.router.navigate('products.items.manager');
                return;
            }
            this.router.main_route.layout.toasts.addMessage(createSuccessMessage(`${this.state.product_ids.length} products successfully deleted`));
            // redirect instead of navigate to remove the delete from the nav history so user can't hit back
            // and see the same modal which will no longer work
            this.router.redirect('products.items.manager');
        });
    };

    /**
     * Unload modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.modal.close();
        await super.unload(request, next);
    };
}

module.exports = BulkDelete;
