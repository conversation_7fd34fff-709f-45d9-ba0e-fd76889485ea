'use strict';

const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector} = require("@ca-package/dom");

const text_messaging_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/text_messaging.hbs');

class TextMessaging extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent
        });
    };

    /**
     * Get available routes
     *
     * @readonly
     *
     * @returns {object}
     */
    static get routes() {
        return {
            details: {
                default: true,
                page: require('./text-messaging-pages/details')
            },
            edit: {
                path: '/text-messaging/edit',
                page: require('./text-messaging-pages/edit')
            }
        };
    };

    /**
     * Get container for child pages to be rendered
     *
     * @returns {jQuery}
     */
    getPageContainer() {
        return this.elem.page;
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.page = findChild(root, jsSelector('text-messaging-pages'));
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return text_messaging_tpl();
    };
}

module.exports = TextMessaging;