'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector} = require("@ca-package/dom");
const Tooltip = require('@ca-submodule/tooltip');

const email_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/email-pages/details.hbs');

class Details extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            settings: parent.getParentByName('settings')
        });
    };

    /**
     * Populate data on page
     *
     * @param {Object} data
     */
    populate(data) {
        let appointment_reminder_statuses = new Map([
            [false, '<span class="h-text t-grey">Disabled</span>'],
            [true, '<span class="h-text t-green">Enabled</span>']
        ]);

        let recipients = data.settings.additional_email_recipients;
        if (recipients === undefined || recipients.length === 0) {
            recipients = '<em>None</em>';
        } else {
            recipients = recipients.join(', ');
        }

        this.elem.email_from.text(data.email_from);
        this.elem.email_reply.text(data.email_reply);
        this.elem.additional_email_recipients.html(recipients);
        if (data.settings.appointment_reminder_24_hours === undefined) {
            this.elem.reminder_24_hours_before_appointment.html(appointment_reminder_statuses.get(false));
        } else {
            this.elem.reminder_24_hours_before_appointment.html(appointment_reminder_statuses.get(data.settings.appointment_reminder_24_hours));
        }
    };

    /**
     * Fetch data from server
     */
    async fetchData() {
        try {
            let {data: entity} = await Api.Resources.Companies().
            fields(['email_from', 'email_reply']).relations({
                'settings': {}
            }).retrieve('current');
            this.populate(entity);
        } catch (e) {

        }
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    async refresh(request) {
        await this.fetchData();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.state.settings.showLoader();
        await this.fetchData();
        this.state.settings.hideLoader();
        await super.load(request, next);
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.elem.email_from.text('');
        this.elem.email_reply.text('');
        this.elem.additional_email_recipients.text('');
        this.elem.reminder_24_hours_before_appointment.text('');
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root, {
            max_width: 400
        });

        this.elem.email_from = findChild(root, jsSelector('email-from'));
        this.elem.email_reply = findChild(root, jsSelector('email-reply'));
        this.elem.additional_email_recipients = findChild(root, jsSelector('additional-email-recipients'));
        this.elem.reminder_24_hours_before_appointment = findChild(root, jsSelector('reminder-24-hours-before-appointment'));
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return email_tpl({
            title: 'Email Settings',
            edit_route: 'settings.email.edit',
            brand_name: profile_data.brand_name
        });
    };
}

module.exports = Details;