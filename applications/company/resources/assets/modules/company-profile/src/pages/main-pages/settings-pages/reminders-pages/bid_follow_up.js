'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onEvent} = require("@ca-package/dom");

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');
const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/switch'));
const NumberInput = require('@ca-submodule/form-input/src/number');
FormInput.use(NumberInput);
const Tooltip = require('@ca-submodule/tooltip');

const FormValidator = require("@cas-validator-js");
const KeyCodes = require('@cac-js/data/event_key_codes');

const {initSelectPlaceholder} = require('@cac-js/utils/select_placeholder');

const bid_follow_up_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/reminders-pages/bid_follow_up.hbs');
const bid_follow_up_row_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/reminders-pages/bid-follow-up-components/bid_follow_up_row.hbs');

class BidFollowUp extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            settings: parent.getParentByName('settings'),
            bid_follow_up_items: {},
            bid_follow_up_emails: []
        });
    };

    /**
     * Prepare entity to save
     *
     * @returns {object}
     */
    buildEntity() {
        let config = [];
        for (let item in this.state.bid_follow_up_items) {
            let $item = this.state.bid_follow_up_items[item];
            config.push({
                email_template_id: $item.fields.email_template.val(),
                send_after_number_of_days: parseFloat($item.fields.send_after_number_of_days.val()),
                is_enabled: $item.fields.enable_toggle.is(':checked')
            });
        }

        return {
            settings: {
                bid_follow_up_notifications: this.elem.bid_follow_up_notifications.is(':checked'),
                bid_follow_up_notifications_config: config
            }
        };
    };

    /**
     * Save settings data to server
     */
    save() {
        this.state.settings.showLoader();
        let data = this.buildEntity();

        Api.Resources.Companies().partialUpdate('current', data).then(({data}) => {
            setTimeout(() => {
                this.state.settings.hideLoader();
                let message = createSuccessMessage(`Company bid follow-up settings saved successfully`);
                this.router.main_route.layout.toasts.addMessage(message);
                this.router.navigate('settings.reminders.details');
            }, 2000);
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    // for (let item in item_errors) {
                    //     if (this.state.field[item] === undefined) {
                    //         continue;
                    //     }
                    //     this.state.field[item].addError('fx-' + item, {message: item_errors[item]});
                    // }
                    this.state.settings.hideLoader();
                    break;
                default:
                    let message = createErrorMessage('Unable to save bid follow-up settings, please contact support');
                    this.router.main_route.layout.toasts.addMessage(message);
                    break;
            }
        });
    };

    /**
     * Remove bid follow up row
     *
     * @param {number} index
     */
    removeBidFollowUpRow(index) {
        this.state.bid_follow_up_items[index].elem.remove();
        delete this.state.bid_follow_up_items[index];

        this.elem.add_bid_follow_up.prop('disabled', false);
        this.elem.bid_follow_up_footer.removeClass('t-info');

        if (Object.keys(this.state.bid_follow_up_items).length === 0) {
            this.addBidFollowUpRow();
        }
    };

    /**
     * Add bid follow up row
     *
     * @param {object} data
     */
    addBidFollowUpRow(data = null) {
        let index = Object.keys(this.state.bid_follow_up_items).length,
           row = $(bid_follow_up_row_tpl({
           index,
           send_after_number_of_days: data === null ? '' : data.send_after_number_of_days,
           enabled: this.elem.bid_follow_up_notifications.is(':checked'),
           emails: this.state.bid_follow_up_emails
        }));
        let email_template = findChild(row, jsSelector('email-template')),
            enable_toggle = findChild(row, jsSelector('is-enabled')),
            send_after_number_of_days = findChild(row, jsSelector('send-after-number-of-days')),
            remove_row = findChild(row, jsSelector('remove'));
        initSelectPlaceholder(email_template);
        let toggle_form_input = FormInput.init(enable_toggle);
        FormInput.init(send_after_number_of_days, {
            type: NumberInput.Type.INT,
            right_align: true
        });

        if (!this.elem.bid_follow_up_notifications.is(':checked')) {
            enable_toggle.prop('disabled', true);
            toggle_form_input.toggleDisabled(true);
        }

        if (data !== null) {
            email_template.val(data.email_template_id).trigger('change');
            enable_toggle.prop('checked', data.is_enabled).trigger('change');
        }

        onEvent(send_after_number_of_days, 'change',  () => {
            this.validateDaysAfter();
            return false;
        });
        let that = this;
        onEvent(remove_row, 'click',  function (e) {
            e.preventDefault();
            let index = $(this).data('index');
            that.removeBidFollowUpRow(index);
            return false;
        });
        onEvent(send_after_number_of_days, 'keydown', e => {
            switch (e.keyCode) {
                case KeyCodes.ENTER:
                    return false;
            }
        });
        Tooltip.initAll(row);

        this.state.bid_follow_up_items[index] = {
            elem: row,
            fields: {
                email_template,
                send_after_number_of_days,
                enable_toggle,
                toggle_form_input
            }
        };
        this.elem.bid_follow_up_table.append(row);
    };

    /**
     * Loop through follow up table and validate days after is greater than the last row
     *
     * Throw errors when necessary
     */
    validateDaysAfter() {
        this.clearError();

        let error = false;
        // loop through table to validate days after bid finalized
        let last_row_index = 0;
        for (let item in this.state.bid_follow_up_items) {
            if (item === '0') {
                continue;
            }
            let this_item = this.state.bid_follow_up_items[item];
            let previous_row_days = this.state.bid_follow_up_items[last_row_index].fields.send_after_number_of_days,
                this_row_days = this_item.fields.send_after_number_of_days;
            this_row_days.removeClass('parsley-error');
            if (parseInt(previous_row_days.val()) >= parseInt(this_row_days.val())) {
                error = true;
                this_row_days.addClass('parsley-error');
            }
            last_row_index = item;
        }
        if (error) {
            let message = 'Days after bid finalized must be greater than the previous row';
            this.setError(message);
        }
    };

    /**
     * Toggle all rows on or off based on main bid follow up toggle
     *
     * @param {boolean} checked
     */
    toggleAllRows(checked) {
        for (let item in this.state.bid_follow_up_items) {
            let $item = this.state.bid_follow_up_items[item];
            $item.fields.enable_toggle.prop('checked', checked);
            if (!checked) {
                $item.fields.enable_toggle.prop('disabled', true);
                $item.fields.toggle_form_input.toggleDisabled(true);
                continue;
            }
            $item.fields.enable_toggle.prop('disabled', false);
            $item.fields.toggle_form_input.toggleDisabled(false);
        }
    };

    /**
     * Populate data on page
     *
     * @param {Object} data
     */
    populate(data) {
        this.elem.bid_follow_up_notifications.prop('checked', data.settings.bid_follow_up_notifications);
        this.clearTable();
        if (data.settings.bid_follow_up_notifications_config === null) {
            this.addBidFollowUpRow();
            return;
        }
        for (let item of data.settings.bid_follow_up_notifications_config) {
            this.addBidFollowUpRow(item);
        }
    };

    /**
     * Fetch settings data from server
     */
    async fetchData() {
        try {
            let {data: entity} = await Api.Resources.Companies().
            fields(['id']).relations({
                'settings': {}
            }).retrieve('current');
            this.populate(entity);
        } catch (e) {}
    };

    /**
     * Fetch settings data from server
     */
    async fetchEmails() {
        try {
            let {entities: email_templates} = await Api.Resources.EmailTemplates()
                .filters({
                    'status': Api.Constants.EmailTemplates.Status.ACTIVE,
                    'type': Api.Constants.EmailTemplates.Type.BID_FOLLOW_UP
                })
                .sort('name', 'asc')
                .all();
            this.state.bid_follow_up_emails = email_templates.map(entity => entity.data);
        } catch (e) {}
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    async refresh(request) {
        await this.fetchData();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);
        if (!profile_data.features.bid_follow_ups) {
            this.router.redirect('settings.email.details');
        }
        this.state.settings.setEditMode(true);
        this.state.settings.showLoader();
        await this.fetchEmails();
        await this.fetchData();
        this.state.settings.hideLoader();
    };

    /**
     * Clear table and reset object
     */
    clearTable() {
        for (let item in this.state.bid_follow_up_items) {
            let $item = this.state.bid_follow_up_items[item].elem;
            $item.remove();
        }
        this.state.bid_follow_up_items = {};
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.clearTable();
        this.clearError();
        this.state.settings.setEditMode(false);
        this.elem.add_bid_follow_up.prop('disabled', false);
        this.elem.bid_follow_up_footer.removeClass('t-info');
        await super.unload(request, next);
    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.elem.form.scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    };

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Initialize form to use on submit
     */
    initForm() {
        this.state.validator = FormValidator.create(this.elem.form, {}, {
            validate_event: true,
            error_event: true
        })
            .on('submit', () => this.save())
            .on('validate', () => {})
            .on('error', () => this.setError('Please review errors below'));
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.form = findChild(root, jsSelector('form'));
        this.elem.bid_follow_up_table = findChild(root, jsSelector('bid-follow-up-table'));
        this.elem.error = findChild(root, jsSelector('error'));
        this.elem.save = findChild(root, jsSelector('save'));
        this.elem.add_bid_follow_up = findChild(root, jsSelector('add-bid-follow-up'));
        this.elem.bid_follow_up_footer = findChild(root, jsSelector('bid-follow-up-footer'));

        this.initForm();

        this.elem.bid_follow_up_notifications = findChild(root, jsSelector('bid-follow-up-notifications'));
        FormInput.init(this.elem.bid_follow_up_notifications);

        onEvent(this.elem.add_bid_follow_up, 'click', (e) => {
            e.preventDefault();
            if (Object.keys(this.state.bid_follow_up_items).length >= 5) {
                this.elem.add_bid_follow_up.prop('disabled', true);
                this.elem.bid_follow_up_footer.addClass('t-info');
                return false;
            }
            this.addBidFollowUpRow();
            return false;
        });

        onEvent(this.elem.save, 'click', (e) => {
            e.preventDefault();
            this.elem.form.trigger('submit');
            return false;
        });

        onEvent(this.elem.bid_follow_up_notifications, 'change', (e) => {
            e.preventDefault();
            this.toggleAllRows(this.elem.bid_follow_up_notifications.is(':checked'));
            return false;
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return bid_follow_up_tpl({
            title: 'Bid Follow-Up',
            cancel_route: 'settings.reminders.details'
        });
    };
}

module.exports = BidFollowUp;