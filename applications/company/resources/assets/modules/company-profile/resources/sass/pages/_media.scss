@use '~@cac-sass/base';

@font-face {
    font-family: 'icomoon';
    src:  url('~@cac-public/fonts/icomoon.eot?e7ly90');
    src:  url('~@cac-public/fonts/icomoon.eot?e7ly90#iefix') format('embedded-opentype'),
    url('~@cac-public/fonts/icomoon.ttf?e7ly90') format('truetype'),
    url('~@cac-public/fonts/icomoon.woff?e7ly90') format('woff');
    font-weight: normal;
    font-style: normal;
}

.m-media {
    .c-m--pages {
        height: 100%;
    }
    .c-m--p-page {
        height: 100%;
        &.t-hidden {
            display: none;
        }
    }
}

.section[data-id="media"] {
    [class^="icon-"], [class*=" icon-"] {
        /* use !important to prevent issues with browser extensions that change fonts */
        font-family: 'icomoon' !important;
        speak: none;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        line-height: 1;

        /* Better Font Rendering =========== */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .media-icon {
        display: inline;
        font-size: 16px;
        &.icon-file-empty:before {
            content: "\e924";
        }
        &.icon-file-picture:before {
            content: "\e927";
        }
        &.icon-file-music:before {
            content: "\e928";
        }
        &.icon-file-video:before {
            content: "\e92a";
        }
        &.icon-file-zip:before {
            content: "\e92b";
        }
        &.icon-circle-down:before {
            content: "\ea43";
        }
        &.icon-file-pdf:before {
            content: "\eadf";
        }
        &.icon-file-word:before {
            content: "\eae1";
        }
        &.icon-file-excel:before {
            content: "\eae2";
        }
    }


}

.m-media-form {
    overflow: auto;
    height: calc(100% - 57px);
    padding: base.unit-rem-calc(24px) base.unit-rem-calc(24px) base.unit-rem-calc(40px);
    display: flex;
    flex-direction: column;
    gap: base.unit-rem-calc(16px);
    justify-content: flex-start;
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(16px) base.unit-rem-calc(16px) base.unit-rem-calc(40px);
    }
    .c-mf-error {
        width: 100%;
        display: none;
        @include base.callout-error;
        &.t-show {
            display: inline-flex;
        }
    }
    .c-mf-row {
        display: flex;
        gap: base.unit-rem-calc(16px);
        &.t-name {
            align-items: flex-end;
            div:first-of-type {
                flex: 0 0 75%;
                @include base.respond-to('<small') {
                    flex: 0 0 100%;
                }
            }
        }
        @include base.respond-to('<small') {
            flex-wrap: wrap;
            gap: base.unit-rem-calc(16px);
        }
        > div {
            flex: 1;
            @include base.respond-to('<small') {
                width: 100%;
                flex: auto;
            }
        }
        > a {
            &.t-tertiary {
                @include base.button-text-icon-tertiary;
            }
        }
    }
        .c-mfr-file-type {
            padding-bottom: base.unit-rem-calc(4px);
        }
    .c-mf-placeholder {
        display: none;
        width: 25%;
        height: 25%;
        object-fit: cover;
        padding: 20%;
        fill: base.$color-grey-light-2;
        border-radius: base.unit-rem-calc(160px);
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        &.t-show {
            display: flex;
        }
    }

    .c-mf-img {
        border-radius: 5%;
    }

    .c-mf-button-wrapper {
        display: flex;
        gap: base.unit-rem-calc(16px);
        align-items: center;
    }

    .c-m-image-flex-block {
        display: flex;
        align-items: flex-end;
        gap: 16px;
        flex: 0;
    }
        .c-m-file {
            display: none;
        }
        .c-m-thumbnail {
            &.t-hidden {
                display: none;
            }
        }

        .c-mf-image-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: base.unit-rem-calc(100px);
        height: base.unit-rem-calc(100px);
        border: base.unit-rem-calc(1px) dashed base.$color-grey-light-4;
        border-radius: base.unit-rem-calc(8px);
        position: relative;

        .c-mf-delete {
            display: flex;
            align-items: center;
            justify-content: center;
            width: base.unit-rem-calc(24px);
            height: base.unit-rem-calc(24px);
            background-color: base.$color-red-default;
            border-radius: base.unit-rem-calc(24px);
            box-shadow: base.$elevation-level-3;
            position: absolute;
            top: base.unit-rem-calc(-10px);
            right: base.unit-rem-calc(-10px);
            &.t-hidden {
                display: none;
            }
            > svg {
                color: base.$color-white-default;
                @include base.svg-icon('default-16');
            }
            &:hover {
                background-color: base.$color-red-dark-1;
                box-shadow: base.$elevation-level-4;
            }
        }
    }

    .c-mf-file-filename {
        color: #5C6F85;
        width: inherit;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }
    .c-mf-button {
        @include base.button-icon-text-primary;
        width: base.unit-rem-calc(160px);
    }
    .c-mf-error {
        margin-bottom: base.unit-rem-calc(5px);
        color: base.$form-error-text-color;
        font-size: base.unit-rem-calc(14px);
        &:last-child {
            margin-bottom: 0;
        }
    }

    .c-mf-settings {
        padding: base.unit-rem-calc(8px) base.unit-rem-calc(12px) base.unit-rem-calc(12px);
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        border-radius: base.unit-rem-calc(8px);
        background: base.$color-background-form;
        > h4 {
            margin-bottom: base.unit-rem-calc(16px);
            .f-fl-optional {
                font-size: base.unit-rem-calc(12px);
                font-weight: 400;
                font-style: italic;
                line-height: base.unit-rem-calc(16px);
            }
        }
        > p {
            color: base.$color-grey-dark-1;
            @include base.typo-paragraph-small;
            font-style: italic;
        }
    }
        .c-mfs-toggles {
            display: grid;
            gap: base.unit-rem-calc(16px);
            flex-direction: column;
            .f-field {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(16px);
                > .t-label {
                    @include base.typo-paragraph-medium;
                    padding: 0;
                    margin-right: base.unit-rem-calc(16px);
                }
                .m-tooltip-info {
                    padding-left: base.unit-rem-calc(4px);
                }
            }
        }
}

.m-company-media-list {
    padding: base.unit-rem-calc(12px);
    height: 100%;
    width: 100%;
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(12px) 0;
    }
}
    .c-cml-table {
        height: 100%;
        width: 100%;
        @include base.respond-to('<small') {
            .c-t-table-wrapper {
                border-radius: 0;
                border-width: base.unit-rem-calc(1px) 0;
            }
            .c-t-header {
                padding: 0 base.unit-rem-calc(8px) 0 base.unit-rem-calc(12px);
            }
        }
        .t-icon-checked {
            @include base.svg-icon('default-18');
            color: base.$color-green-light-1;
        }
        .t-icon-unchecked {
            @include base.svg-icon('default-18');
            color: base.$color-grey-light-4;
        }
        .c-cmlt-image-type {
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(4px);
        }
    }
    