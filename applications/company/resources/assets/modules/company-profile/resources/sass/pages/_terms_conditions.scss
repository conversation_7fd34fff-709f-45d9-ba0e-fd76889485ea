@use '~@cac-sass/base';
@use '~@cac-sass/app/form';
@use '~@cas-form-input-sass/switch';

.m-terms-conditions {
    .c-t--pages {
        height: 100%;
    }
    .c-t--p-page {
        height: 100%;
        &.t-hidden {
            display: none;
        }
    }
}

.m-terms-conditions-list {
    padding: base.unit-rem-calc(12px);
    height: 100%;
    width: 100%;
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(12px) 0;
    }
}
    .c-tl-table {
        height: 100%;
        width: 100%;
        @include base.respond-to('<small') {
            .c-t-table-wrapper {
                border-radius: 0;
                border-width: base.unit-rem-calc(1px) 0;
            }
            .c-t-header {
                padding: 0 base.unit-rem-calc(8px) 0 base.unit-rem-calc(12px);
            }
        }
        .c-thwb-button {
            margin-right: 0;
        }
        .t-icon-checked {
            @include base.svg-icon('default-18');
            color: base.$color-green-light-1;
        }
        .t-icon-unchecked {
            @include base.svg-icon('default-18');
            color: base.$color-grey-light-4;
        }
    }

.m-terms-conditions-form {
    overflow: auto;
    height: calc(100% - 57px);
    padding: base.unit-rem-calc(24px) base.unit-rem-calc(24px) base.unit-rem-calc(40px);
    display: flex;
    flex-direction: column;
    gap: base.unit-rem-calc(32px);
    justify-content: flex-start;
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(16px) base.unit-rem-calc(16px) base.unit-rem-calc(40px);
    }
    .c-tf-name {
        display: flex;
        flex-wrap: wrap;
        column-gap: base.unit-rem-calc(16px);
        row-gap: base.unit-rem-calc(8px);
        .f-field {
            flex: 1;
            min-width: base.unit-rem-calc(256px);
        }
    }
    .c-tf-settings {
        padding: base.unit-rem-calc(8px) base.unit-rem-calc(12px) base.unit-rem-calc(12px);
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        border-radius: base.unit-rem-calc(8px);
        background: base.$color-background-form;
        > h4 {
            margin-bottom: base.unit-rem-calc(24px);
            .f-fl-optional {
                font-size: base.unit-rem-calc(12px);
                font-weight: 400;
                font-style: italic;
                line-height: base.unit-rem-calc(16px);
            }
        }
        > p {
            color: base.$color-grey-dark-1;
            @include base.typo-paragraph-small;
            font-style: italic;
        }
        .c-tfs-toggles {
            display: grid;
            gap: base.unit-rem-calc(16px);
            flex-direction: column;
            .f-field {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(16px);
                > .t-label {
                    @include base.typo-paragraph-medium;
                    padding: 0;
                    margin-right: base.unit-rem-calc(16px);
                }
                .m-tooltip-info {
                    padding-left: base.unit-rem-calc(4px);
                }
            }
        }
    }
    .c-tf-products {
        .f-field {
            display: grid;
            grid-column-gap: base.unit-rem-calc(16px);
            grid-template-columns: repeat(2, 1fr);
            grid-column: span 1;
            @include base.respond-to('<small') {
                grid-column: span 2;
            }
            .f-f-label {
                grid-column: span 2;
            }
            .tag-list {
                margin: base.unit-rem-calc(4px) 0 0;
            }
            @include base.respond-to('<small') {
                display: block;
                .tag-list {
                    margin: base.unit-rem-calc(8px) 0 0;
                }
            }
            .select2-container {
                &.select2-container--disabled {
                    .select2-selection--multiple {
                        background: unset;
                        border-color: base.$color-grey-light-4;
                        box-shadow: none;
                        .select2-search__field {
                            -webkit-text-fill-color: base.$color-grey-light-3;
                        }
                        &::after {
                            opacity: 50%;
                        }
                    }
                }
            }
        }
    }
}