<div class="c-s--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>{{title}}</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary-negate-grey" data-js="button" data-navigate="{{cancel_route}}">Cancel</button>
            <button class="c-pha-primary" data-js="save">Save</button>
        </div>
    </div>
    <div class="m-page-wrapper">
        <form class="m-company-settings-form" data-js="form">
            <div class="c-csf-error" data-js="error"></div>
            <div class="c-csf-row">
                <p>
                    Select your custom created email and input the number of days after bid delivery for each
                    to send. To create a new email, visit the <a data-navigate="emails.items.manager">Email Section</a> within Company Profile.
                </p>
            </div>
            <div class="c-csf-row t-switch">
                <div class="f-field">
                    <input class="f-f-input" type="checkbox" id="follow_up_notifications"
                           data-fx-form-input="switch" data-js="bid-follow-up-notifications">
                    <label class="f-f-label t-label" for="follow_up_notifications">
                        Enable Workflow
                    </label>
                </div>
            </div>
            <div class="m-reminder-settings t-edit">
                <div class="c-rs-table">
                    <div class="c-rst-header">
                        <div class="f-field t-enabled">
                            <span data-tooltip data-type="info">Enable Workflow at the top right to activate automation. Then, enable each line to trigger the send of that email.</span>
                        </div>
                        <div class="f-field t-email">
                            <label class="f-f-label">
                                Email Name
                            </label>
                        </div>
                        <div class="f-field t-days">
                            <label class="f-f-label">
                                Days After Bid Finalized
                                <span data-tooltip data-type="info">
                            Automatic reminder emails send based on the number of days after the bid is finalized unless the contract is signed or the customer is opted out
                        </span>
                            </label>
                        </div>
                        <div class="f-field t-remove"></div>
                    </div>
                    <div class="c-rst-body" data-js="bid-follow-up-table"></div>
                    <div class="c-rst-footer" data-js="bid-follow-up-footer">
                        <div class="c-rstf-info" data-js="add-bid-follow-up-info">
                            <a href="mailto:<EMAIL>">Contact Support</a> to add more
                        </div>
                        <button class="c-rstf-add" data-js="add-bid-follow-up">
                            <div data-text>Add Row</div>
                            <svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>