<div class="c-s--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>{{title}}</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary t-edit" data-js="edit" data-navigate="{{edit_route}}">
                <div data-text>Edit</div>
                <svg data-icon><use xlink:href="#remix-icon--design--edit-2-line"></use></svg>
            </button>
        </div>
    </div>
    <div class="m-bid-defaults">
        <div class="c-bd-wrapper">
            <div class="c-bdw-title t-bid-defaults">One-Time Payment Due Time Frame</div>
            <div class="c-bdw-content t-bid-defaults" data-js="payment-term-one-time-due-time-frame"></div>
        </div>
        <div class="c-bd-title t-installments">Installments</div>
        <div class="c-bd-table">
            <div class="c-bdt-header">
                <div class="f-field t-name">
                    <label class="f-f-label">
                        Name
                    </label>
                </div>
                <div class="f-field t-configured-emails">
                    <label class="f-f-label">
                        Payment Due
                    </label>
                </div>
                <div class="f-field t-status">
                    <label class="f-f-label">
                        Amount
                    </label>
                </div>
            </div>
            <div class="c-bdt-body" data-js="installment-table"></div>
        </div>
    </div>
</div>