<div class="c-w--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>Warranty Certificates</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-primary" id="addWarranty" data-js="add-warranty">Add Warranty</button>
        </div>
    </div>
    <div class="m-warranties">
        <table id="warrantyTable" class="evaluationTable" cellpadding="0" cellspacing="0">
            <thead>
            <tr>
                <th style="text-align:left">Warranty Name</th>
                <th>Last Updated</th>
                <th>Warranty Type</th>
                <th width="30%"></th>
            </tr>
            </thead>
            <tbody>
            <tr style="display:none;">
                <td class="WarrantyName" style="text-align:left"></td>
                <td class="WarrantyDate"></td>
                <td class="WarrantyStatus"></td>
                <td style="text-align: right;">
                    <button id="editWarranty" style="display:inline-block;" class="b-text t-primary">Edit</button>
                    <a id="previewWarranty" target="_blank" style="margin: 0rem 0 1rem 0;display:inline-block;" class="b-text t-primary" href="">Preview</a>
                    <a class="deleteItem" id="deleteWarranty"><img src="{{delete}}" /></a>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="tiny reveal" id="warrantyDeleteModal" data-reveal data-close-on-esc="false" data-close-on-click="false">
        <div class="row">
            <div class="medium-12 columns">
                <h3 class="text-center">Delete Warranty</h3>
                <p class="text-center" style="margin-top: 1rem;">Are you sure you want to delete this warranty?</p>
                <p class="text-center no-margin">
                    <input type="hidden" name="warrantyID" value="" />
                    <button class="button" id="yesDeleteWarranty">Yes</button>
                    <button class="button secondary" id="cancelDeleteWarranty">Cancel</button>
                </p>
            </div>
        </div>
    </div>
    <div class="large reveal" id="warrantyModal" data-reveal data-close-on-esc="false" data-close-on-click="false">
        <div class="row">
            <div class="medium-12 columns">
                <h3 class="text-center">Edit Warranty</h3>
                <div class="medium-6 columns no-pad">
                    <label>Warranty Name</label>
                    <input name="warrantyName" type="text" value="" />
                    <input name="warrantyID" type="hidden" value=""/ >
                    <small id="warrantyNameError" style="margin-top: 1rem;" class="form-error">You must specify a warranty name.</small>
                </div>
                <div class="medium-6 columns no-pad" style="margin-bottom: 1rem;">
                    <label>Page Orientation</label>
                    <label>
                        <input name="optType" type="radio" value="0" id="optCertificate" checked="checked"/>
                        Certificate (landscape)
                    </label>
                    <label>
                        <input name="optType" type="radio" value="1" id="optDocument" />
                        Document (portrait)
                    </label>
                    <small class="form-error" id="documentTypeError">You must choose a document type</small>

                </div>
                <label>
                    <textarea id="warrantyBody" rows="20"></textarea>
                    <small id="warrantyError" style="margin-top: 1rem;" class="form-error">You must include warranty information.</small>
                </label>
                <p class="text-center no-margin" style="margin-top: 1.4rem;">
                    <button class="button" id="saveWarrantyBody">Save</button>
                    <button class="button secondary" id="cancelWarrantyBody">Cancel</button>
                </p>
            </div>
        </div>
    </div>
    <div class="tiny reveal" id="successModal" data-reveal>
        <div class="row">
            <div class="medium-12 columns">
                <p class="modal-content text-center">
                </p>
                <p class="text-center no-margin">
                    <button class="button" data-close>OK</button>
                </p>
            </div>
        </div>
    </div>
</div>
