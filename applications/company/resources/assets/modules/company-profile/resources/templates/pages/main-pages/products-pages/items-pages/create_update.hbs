<div class="c-p--p-page">
    <div class="m-page-header">
        <h3 class="c-ph-title" data-title>{{title}}</h3>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary-negate-grey" data-js="button" data-navigate="{{cancel_route}}">Cancel</button>
            <button class="c-pha-primary" data-js="save">Save</button>
        </div>
    </div>
    <form class="m-product-form" data-js="form">
        <div class="c-pf-error" data-js="error"></div>
        <div class="c-pf-instructions" data-js="instructions"></div>
        <div class="c-pf-name">
            <div class="f-field">
                <label class="f-f-label">Name</label>
                <input class="f-f-input" type="text" data-js="name" />
            </div>
        </div>
        {{#if is_feature_product_components}}
        <div class="c-pf-type">
            <div class="f-field">
                <label class="f-f-label">
                    Pricing Type
                    <span data-tooltip data-type="info">
                        Basic pricing is a simple per-unit price. Component pricing allows you to configure the price based on materials, labor, and other COGS.
                    </span>
                </label>
            </div>
            <div class="f-field">
                <input class="f-f-input" id="{{ns}}-basic-pricing" name="pricing_type" required type="radio" data-js="pricing-type" data-id="1"/>
                <label class="f-f-sub-label" for="{{ns}}-basic-pricing">Basic Pricing</label>
            </div>
            <div class="f-field">
                <input class="f-f-input" id="{{ns}}-component-pricing" name="pricing_type" required type="radio" data-js="pricing-type" data-id="2"/>
                <label class="f-f-sub-label" for="{{ns}}-component-pricing">Component Pricing</label>
            </div>
        </div>
        {{/if}}
        <div class="c-pf-price-unit">
            <div class="f-field t-price">
                <label class="f-f-label">Price</label>
                <input class="f-f-input" placeholder="---" type="text" data-fx-form-input="number" data-js="price"/>
            </div>
            <div class="f-field t-unit">
                <label class="f-f-label">
                    Unit
                </label>
                <select class="f-f-input" data-js="unit" placeholder="-- Select One --"></select>
            </div>
        </div>
        <div class="c-pf-volume-discount" data-js="volume-discount">
            <div class="f-field t-optional">
                <label class="f-f-label t-optional">
                    Volume Discount
                    <span data-tooltip data-type="info">
                        Optional pricing model with a discounted price for higher quantities of this product. Each row’s
                        unit price will be used if the quantity is within the given range.
                    </span>
                    <span class="f-fl-optional">(Optional)</span>
                </label>
                <h5>
                    Volume Discount
                    <span data-tooltip data-type="info">
                        Optional pricing model with a discounted price for higher quantities of this product. Each row’s
                        unit price will be used if the quantity is within the given range.
                    </span>
                </h5>
                <button class="f-f-button-remove" data-js="button" data-id="remove">
                    <div data-text>Remove</div>
                    <svg data-icon><use xlink:href="#remix-icon--system--delete-bin-2-line"></use></svg>
                </button>
                <button class="f-f-button-add" data-js="button" data-id="add">
                    <div data-text>Add</div>
                    <svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg>
                </button>
            </div>
            <div class="m-volume-discount">
                <div class="c-vd-error" data-js="volume-discount-error"></div>
                <div class="c-vd-header">
                    <div class="f-field t-min-count">
                        <label class="f-f-label">
                            Min Qty
                            <span data-tooltip data-type="info">
                                Minimum quantity for the volume discount range.
                            </span>
                            <span class="f-fl-optional">(From...)</span>
                        </label>
                    </div>
                    <div class="f-field t-max-count">
                        <label class="f-f-label">
                            Max Qty
                            <span data-tooltip data-type="info">
                                Maximum quantity is less than this number for the volume discount range.
                            </span>
                            <span class="f-fl-optional">(...to less than)</span>
                        </label>
                    </div>
                    <div class="f-field t-adjustment">
                        <label class="f-f-label">
                            Adjustment
                            <span data-tooltip data-type="info">
                                Amount the unit price will be discounted if the quantity is within the given range.
                            </span>
                        </label>
                    </div>
                    <div class="f-field t-price">
                        <label class="f-f-label">
                            Unit Price
                            <span data-tooltip data-type="info">
                                The price to charge per unit of this product when the quantity is within the given range.
                            </span>
                        </label>
                    </div>
                </div>
                <div class="c-vd-body" data-js="volume-discount-table"></div>
                <div class="c-vd-footer">
                    <a class="c-vdf-add" data-js="add-volume-discount">
                        <div data-text>Add Row</div>
                        <svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg>
                    </a>
                </div>
            </div>
        </div>
        {{#if is_feature_product_components}}
        <div class="c-pf-components" data-js="components">
            <div class="c-pfc-header">
                <h4>Components</h4>
            </div>
            <div class="c-pfc-materials">
                <div class="f-field t-materials-title">
                    <label class="f-f-label">
                        Materials
                    </label>
                    <p class="c-pfcm-paragraph">
                        Add the materials required per unit for the product. <a href="https://cxlratr.to/materials" target="_blank">Learn More</a>
                    </p>
                </div>
                <div class="m-component-materials">
                    <div class="c-cm-error" data-js="component-materials-error"></div>
                    <div class="c-cm-header">
                        <div class="f-field t-material">
                            <label class="f-f-label">
                                Material
                            </label>
                        </div>
                        <div class="f-field t-cost">
                            <label class="f-f-label">
                                Cost
                                <span class="f-fl-optional">(Before Markup)</span>
                            </label>
                        </div>
                        <div class="f-field t-quantity">
                            <label class="f-f-label">
                                Quantity
                            </label>
                        </div>
                        <div class="f-field t-markup">
                            <label class="f-f-label">
                                Markup
                                <span class="f-fl-optional">(%)</span>
                            </label>
                        </div>
                        <div class="f-field t-total">
                            <label class="f-f-label">
                                Total
                            </label>
                        </div>
                    </div>
                    <div class="c-cm-body" data-js="component-materials-table"></div>
                    <div class="c-cm-footer">
                        <a class="c-cmf-add" data-js="add-component-material">
                            <div data-text>Add Row</div>
                            <svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg>
                        </a>
                    </div>
                </div>
            </div>

            <div class="c-pfc-additional-costs" data-js="component-additional-costs">
                <div class="f-field t-ac-title">
                    <label class="f-f-label">
                        Additional Costs
                        <span class="f-fl-optional">(Optional)</span>
                    </label>
                    <button class="f-f-button-remove" data-js="button" data-id="remove">
                        <div data-text>Remove</div>
                        <svg data-icon><use xlink:href="#remix-icon--system--delete-bin-2-line"></use></svg>
                    </button>
                    <button class="f-f-button-add" data-js="button" data-id="add">
                        <div data-text>Add</div>
                        <svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg>
                    </button>
                    <p class="c-pfcac-paragraph">
                        Add any additional costs incurred per unit of this product. (labor, overhead, rentals, etc.) <a href="https://cxlratr.to/additional-costs" target="_blank">Learn More</a>
                    </p>
                </div>
                <div class="m-component-additional-costs">
                    <div class="c-cac-error" data-js="component-additional-costs-error"></div>
                    <div class="c-cac-header">
                        <div class="f-field t-item">
                            <label class="f-f-label">
                                Item
                            </label>
                        </div>
                        <div class="f-field t-cost">
                            <label class="f-f-label">
                                Cost
                                <span class="f-fl-optional">(Before Markup)</span>
                            </label>
                        </div>
                        <div class="f-field t-quantity">
                            <label class="f-f-label">
                                Quantity
                            </label>
                        </div>
                        <div class="f-field t-markup">
                            <label class="f-f-label">
                                Markup
                                <span class="f-fl-optional">(%)</span>
                            </label>
                        </div>
                        <div class="f-field t-total">
                            <label class="f-f-label">
                                Total
                            </label>
                        </div>
                    </div>
                    <div class="c-cac-body" data-js="component-additional-costs-table"></div>
                    <div class="c-cac-footer">
                        <a class="c-cacf-add" data-js="add-component-additional-cost">
                            <div data-text>Add Row</div>
                            <svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {{/if}}
        <div class="c-pf-description">
            <textarea class="f-f-input" data-js="description" data-fx-form-input="hidden-textarea"></textarea>
        </div>
        <div class="c-pf-pricing-disclaimer">
            <textarea class="f-f-input" data-js="pricing_disclaimer" data-fx-form-input="hidden-textarea"></textarea>
        </div>
        {{#if is_feature_product_attributes}}
        <div class="c-pf-attributes" data-js="attributes">
            <div class="f-field">
                <label class="f-f-label">
                    Attributes
                    <span class="f-fl-optional">(Optional)</span>
                </label>
                <button class="f-f-button-remove" data-js="button" data-id="remove">
                    <div data-text>Remove</div>
                    <svg data-icon><use xlink:href="#remix-icon--system--delete-bin-2-line"></use></svg>
                </button>
                <button class="f-f-button-add" data-js="button" data-id="add">
                    <div data-text>Add</div>
                    <svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg>
                </button>
                <div class="c-pfa-table">
                    Attributes Table Here
                </div>
            </div>
        </div>
        {{/if}}
        <div class="c-pf-categories-terms">
            <div class="c-pfct-menu" data-js="tab-menu">
                <div class="c-pfctm-tab t-active" data-js="tab-item" data-id="categories">
                    <svg class="c-pfctmt-icon"><use xlink:href="#remix-icon--weather--mist-line"></use></svg>
                    <a class="c-pfctmt-title">Categories</a>
                </div>
                <div class="c-pfctm-tab" data-js="tab-item" data-id="terms-conditions">
                    <svg class="c-pfctmt-icon"><use xlink:href="#remix-icon--document--file-paper-2-line"></use></svg>
                    <a class="c-pfctmt-title">Terms & Conditions</a>
                </div>
            </div>
            <div class="c-pfct-sections">
                <div class="c-pfcts-section t-active" data-js="tab-section" data-id="categories">
                    <div class="f-field">
                        <p class="c-pfctss-paragraph">
                            Assign one or more categories to attach it to a form's product dropdown option.
                            <span data-tooltip data-type="info">
                                A product category ties the product to the form allowing users to select it during bid
                                creation. A product can be an option under more than one form by assigning more than one
                                category. Please visit the Company Profile to create new Product Categories.
                            </span>
                        </p>
                        <select class="f-f-input" data-js="category" multiple data-fx-form-input="nested-dropdown"></select>
                    </div>
                </div>
                <div class="c-pfcts-section" data-js="tab-section" data-id="terms-conditions">
                    <div class="f-field">
                        <p class="c-pfctss-paragraph">
                            Assign Terms & Conditions (T&C) specific to this product.
                            <span data-tooltip data-type="info">
                                The system will automatically integrate the assigned T&C with any other default contract
                                into the bid's Terms and Conditions section. Please visit the Company Profile to create
                                new Terms and Conditions.
                            </span>
                        </p>
                        <select class="f-f-input" data-js="terms-conditions" multiple data-fx-form-input="static-dropdown"></select>
                    </div>
                </div>
            </div>
        </div>
        <div class="c-pf-hidden" data-js="hidden">
            <div class="f-field">
                <label class="f-f-label">
                    Alias
                    <span class="f-fl-optional">(Optional)</span>
                </label>
                <input class="f-f-input" type="text" data-js="alias" />
            </div>
        </div>
    </form>
</div>