<form class="m-user-form">
    <div class="row">
        <div class="small-12 columns">
            <h3 class="text-left modal_title"></h3>
        </div>
    </div>
    <div class="row">
        <div class="medium-6 small-12 columns">
            <label><strong>First Name</strong> <small>required</small>
                <input type="text" name="first_name" value="" />
            </label>
        </div>
        <div class="medium-6 columns">
            <label><strong>Last Name</strong> <small>required</small>
                <input type="text" name="last_name" value="" />
            </label>
        </div>
    </div>
    <div class="row">
        <div class="medium-6 columns">
            <label><strong>Email</strong> <small>required</small>
                <input type="text" name="email" value="" />
            </label>
        </div>
    </div>
    <div class="row">
        <div class="large-9 columns">
            <table class="phoneTable" cellpadding="0" cellspacing="0">
                <thead>
                <tr>
                    <th width="33%">Phone</th>
                    <th width="33%"></th>
                    <th width="14%">Primary</th>
                    <th width="20%">
                        <a class="addPhone"><img src="{{phoneAdd}}" /></a>
                    </th>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div class="primary-phone-error"></div>
        </div>
    </div>
    <div class="row">
        <div class="small-12 columns">
            <div>
                <label><strong>Bio</strong>
                    <textarea rows="5" name="bio"></textarea>
                </label>
            </div>
            <div class="row">
                <div class="medium-6 columns">
                    <label><strong>Photo</strong></label>
                    <div class="currentPhoto"></div>
                    <a class="addNewPhoto">Upload New Photo</a>
                    <div class="newPhotoUpload">
                        <label>Upload Photo <small>(JPG, PNG or GIF)</small>
                            <input type="file" name="image" value="" accept="image/gif, image/jpeg, image/png" />
                        </label>
                    </div>
                </div>
                <div class="medium-6 columns">
                    <label><strong>Color</strong></label>
                    <input type="text" class="showPalette"/>
                    <input type="hidden" name="calendar_bg_color" value="" />
                    <input type="hidden" name="calendar_text_color" value="" />
                </div>
            </div>
        </div>
        <div class="medium-6 small-12 columns rolesContainer">
            <label><strong>Roles</strong></label>
            <div class="row" data-same-user>
                <div class="medium-8 columns">
                    <label>Primary <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="1" title="This role has the ability to do all tasks within the other roles.  The primary can also manage the company profile."><img src="{{info}}" /></span>
                </div>
                <div class="text-right medium-4 columns">
                    <div class="switch tiny">
                        <input class="switch-input" id="{{ns}}-primary" type="checkbox" name="role_primary" value="1" data-parsley-multiple="roles">
                        <label class="switch-paddle" for="{{ns}}-primary"></label>
                    </div>
                </div>
            </div>
            <hr class="roles" data-same-user>
            <div class="row">
                <div class="medium-8 columns">
                    <label>Sales Management <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="2" title="This role is responsible for creating the customers and projects.  The sales manager can schedule sales appointments and installations.  The role can also modify the customer and project information as well as complete or cancel projects."><img src="{{info}}" /></span>
                </div>
                <div class="text-right medium-4 columns">
                    <div class="switch tiny">
                        <input class="switch-input" id="{{ns}}-projectManagement" type="checkbox" name="role_project_management" value="1" data-parsley-multiple="roles">
                        <label class="switch-paddle" for="{{ns}}-projectManagement"></label>
                    </div>
                </div>
            </div>
            <hr class="roles">
            <div class="row">
                <div class="medium-8 columns">
                    <label>Sales <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="3" title="This role is responsible for sales appointments.  This role will be scheduled on the calendar for sales."><img src="{{info}}" /></span>
                </div>
                <div class="text-right medium-4 columns">
                    <div class="switch tiny">
                        <input class="switch-input" id="{{ns}}-sales" type="checkbox" name="role_sales" value="1" data-parsley-multiple="roles">
                        <label class="switch-paddle" for="{{ns}}-sales"></label>
                    </div>
                </div>
            </div>
            <hr class="roles">
            <div class="row">
                <div class="medium-8 columns">
                    <label>Marketing <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="3" title="This role is responsible for overseeing marketing efforts."><img src="{{info}}" /></span>
                </div>
                <div class="text-right medium-4 columns">
                    <div class="switch tiny">
                        <input class="switch-input" id="{{ns}}-marketing" type="checkbox" name="role_marketing" value="1" data-parsley-multiple="roles">
                        <label class="switch-paddle" for="{{ns}}-marketing"></label>
                    </div>
                </div>
            </div>
            <hr class="roles">
            <div class="row">
                <div class="medium-8 columns">
                    <label>Bid Creation <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="5" title="This role creates the bid after the evaluation."><img src="{{info}}" /></span></label>
                </div>
                <div class="text-right medium-4 columns">
                    <div class="switch tiny">
                        <input class="switch-input" id="{{ns}}-bidCreation" type="checkbox" name="role_bid_creation" value="1" data-parsley-multiple="roles">
                        <label class="switch-paddle" for="{{ns}}-bidCreation"></label>
                    </div>
                </div>
            </div>
            <hr class="roles">
            <div class="row">
                <div class="medium-8 columns">
                    <label>Bid Verification <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="6" title="This role verifies the bid after it has been created.  This role will check the prices that were entered and change if necessary."><img src="{{info}}" /></span></label>
                </div>
                <div class="text-right medium-4 columns">
                    <div class="switch tiny">
                        <input class="switch-input" id="{{ns}}-bidVerification" type="checkbox" name="role_bid_verification" value="1" data-parsley-multiple="roles">
                        <label class="switch-paddle" for="{{ns}}-bidVerification"></label>
                    </div>
                </div>
            </div>
            <hr class="roles">
            <div class="row">
                <div class="medium-8 columns">
                    <label>Installation <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="4" title="This role is responsible for installations.  This role will be scheduled on the calendar for installations."><img src="{{info}}" /></span></label>
                </div>
                <div class="text-right medium-4 columns">
                    <div class="switch tiny">
                        <input class="switch-input" id="{{ns}}-installation" type="checkbox" name="role_installation" value="1" data-parsley-multiple="roles">
                        <label class="switch-paddle" for="{{ns}}-installation"></label>
                    </div>
                </div>
            </div>
            <hr class="roles">
            <div class="row">
                <div class="medium-8 columns">
                    <label>Timecard Approver <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="7" title="This role approves crewman timecards."><img src="{{info}}" /></span></label>
                </div>
                <div class="text-right medium-4 columns">
                    <div class="switch tiny">
                        <input class="switch-input" id="{{ns}}-timecardApprover" type="checkbox" name="role_timecard_approver" value="1" data-parsley-multiple="roles">
                        <label class="switch-paddle" for="{{ns}}-timecardApprover"></label>
                    </div>
                </div>
            </div>
            <hr class="roles">
            <div class="row">
                <div class="medium-8 columns">
                    <label>Metrics <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="7" title="This role can view and run metrics."><img src="{{info}}" /></span></label>
                </div>
                <div class="text-right medium-4 columns">
                    <div class="switch tiny">
                        <input class="switch-input" id="{{ns}}-metrics" type="checkbox" name="role_metrics" value="1" data-parsley-multiple="roles">
                        <label class="switch-paddle" for="{{ns}}-metrics"></label>
                    </div>
                </div>
            </div>
            <hr class="roles" data-same-user>
            <div class="roles-error"></div>
            <label class="user-active-label" data-same-user><strong>Active</strong></label>
            <div class="row" data-same-user>
                <div class="medium-8 columns">
                    <label>Is the user active?</label>
                </div>
                <div class="text-right medium-4 columns">
                    <div class="switch tiny">
                        <input class="switch-input" id="{{ns}}-userActive" type="checkbox" name="is_active" value="1">
                        <label class="switch-paddle" for="{{ns}}-userActive"></label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row user-edit-buttons">
        <div class="text-right small-12 columns">
            <input type="hidden" name="id" value="" />
            <input type="submit" class="button add" value="Save" />
            <a class="button secondary close-user">Close</a>
        </div>
    </div>
</form>