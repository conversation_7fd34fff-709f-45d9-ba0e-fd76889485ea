<div class="c-s--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>{{title}}</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary t-edit" data-js="edit" data-navigate="{{edit_route}}">
                <div data-text>Edit</div>
                <svg data-icon><use xlink:href="#remix-icon--design--edit-2-line"></use></svg>
            </button>
        </div>
    </div>
    <div class="m-company-settings">
        <div class="c-cs-wrapper">
            <div class="c-csw-content">
                <h4 class="c-cswc-title">
                    Calendar Sharing
                    <span data-tooltip data-type="info">
                Allow calendar sharing between sales and installation users
            </span>
                </h4>
                <div class="c-cswc-content" data-js="enable-calendar-sharing"></div>
            </div>
            <div class="c-csw-content">
                <h4 class="c-cswc-title">
                    Timezone
                    <span data-tooltip data-type="info">
                Timezone of your location to convert timestamps
            </span>
                </h4>
                <div class="c-cswc-content" data-js="timezone"></div>
            </div>
            <div class="c-csw-content">
                <h4 class="c-cswc-title">
                    Map Location
                    <span data-tooltip data-type="info">
                Coordinates for the calendar map when no appointments are scheduled
            </span>
                </h4>
                <div class="c-cswc-content" data-js="location"></div>
            </div>
        </div>
        <div class="m-business-hours">
            <div class="c-bh-title">
                Business Hours
                <span data-tooltip data-type="info">
                    Set your business hours to ensure weekends or off-hours appear in a different color on your calendar
                </span>
            </div>
            <div class="c-bh-table">
                <div class="c-bht-header">
                    <div class="f-field t-day">
                        <label class="f-f-label">Day</label>
                    </div>
                    <div class="f-field t-start">
                        <label class="f-f-label">Start</label>
                    </div>
                    <div class="f-field t-end">
                        <label class="f-f-label">End</label>
                    </div>
                    <div class="f-field t-status">
                        <label class="f-f-label">Open/Closed</label>
                    </div>
                </div>
                <div class="c-bht-body" data-js="business-hours-table"></div>
            </div>
        </div>
    </div>
</div>