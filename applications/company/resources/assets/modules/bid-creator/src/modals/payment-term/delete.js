/**
 * @module BidCreator/Modals/PaymentTerm/Delete
 */

'use strict';

const Confirm = require('@ca-submodule/modal').Confirm;

const PaymentTermEntity = require('../../entities/payment-term/base');

/**
 * @alias module:BidCreator/Modals/PaymentTerm/Delete
 */
class Delete extends Confirm {
    /**
     * Constructor
     */
    constructor() {
        super();
        this.setTitle('Delete Payment Term');
    };

    /**
     * Open modal
     *
     * @param {module:BidCreator/Modals/PaymentTerm/Configure} modal
     * @param {module:BidCreator/Entities/PaymentTerm.Base} entity
     */
    open(modal, entity) {
        this.state.modal = modal;
        this.state.entity = entity;
        let types = {
            [PaymentTermEntity.Type.ONE_TIME]: 'one-time',
            [PaymentTermEntity.Type.INSTALLMENT]: 'installment'
        };
        this.setContent(`<p>Are you sure you want to remove this ${types[entity.type]} payment term?</p>`);
        super.open();
    };

    /**
     * Handle 'yes' answer which means the user wants to delete
     */
    handleYes() {
        this.startWorking();
        this.state.entity.delete().then(() => {
            this.resetWorking();
            // close parent modal as well
            this.state.modal.close();
            this.close();
        }, () => {
            // @todo implement proper flash message once they are built into modals
            alert('Unable to delete payment term, please contact support');
            this.resetWorking();
        });
    };

    /**
     * Handle 'no' answer, which means the user wants to abort
     */
    handleNo() {
        this.close();
    };
}

module.exports = Delete;
