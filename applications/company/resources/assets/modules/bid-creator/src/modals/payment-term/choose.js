/**
 * @module BidCreator/Modals/PaymentTerm/Choose
 */

'use strict';

/**
 * @type {module:Modal}
 */
const Modal = require('@ca-submodule/modal').Base;

const choose_tpl = require('@cam-bid-creator-tpl/modals/payment-term/choose.hbs');

/**
 * @alias module:BidCreator/Modals/PaymentTerm/Choose
 */
class Choose extends Modal {
    /**
     * Constructor
     *
     * @param {module:BidCreator/Sidebar/Components/Pricing.PaymentTerms} parent
     */
    constructor(parent) {
        super(choose_tpl({
            types: [
                {
                    id: 1,
                    name: 'One-Time'
                },
                {
                    id: 2,
                    name: 'Installments'
                }
            ]
        }), {
            size: Modal.Size.TINY,
            closable: true,
            wrapper: false
        });

        Object.assign(this.state, {
            parent: parent
        });

        this.elem.types = this.elem.content.fxFind('types');

        const that = this;
        this.elem.types.fxClickWatcher('type', function (e) {
            e.preventDefault();
            that.chooseType(parseInt($(this).fxParents('item-wrapper').data('id')));
            return false;
        });
    };

    /**
     * Get parent
     *
     * @readonly
     *
     * @returns {module:BidCreator/Sidebar/Components/Pricing.PaymentTerms}
     */
    get parent() {
        return this.state.parent;
    };

    /**
     * Choose type and open associated modal
     *
     * @param {number} type
     */
    chooseType(type) {
        this.close();
        this.parent.configureType(type);
    };
}

module.exports = Choose;
