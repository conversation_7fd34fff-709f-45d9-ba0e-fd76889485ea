/**
 * @module BidCreator/Section
 */

'use strict';

const EventEmitter = require('events');
const Popper = require('popper.js');

/**
 * @type {module:Api}
 */
const Api = require('@ca-package/api');

const DeleteModal = require('./modals/section/delete');
const FlashMessage = require('./flash-message');
const PubSub = require('./pubsub');
const SectionForm = require('./section/form');

const section_tpl = require('@cam-bid-creator-tpl/section.hbs');

/**
 * @alias module:BidCreator/Section
 */
class Section {
    /**
     * Constructor
     *
     * @param {module:BidCreator} bid
     * @param {Object} data
     * @param {string} data.id - Section uuid
     * @param {?string} data.name - Section name
     * @param {number} data.order - Section order
     * @param {Array} data.forms - Array of form data
     */
    constructor(bid, data) {
        this.elem = {};
        /**
         * @private
         */
        this.state = {
            rendered: false,
            booted: false,
            bid: bid,
            id: data.id,
            name: data.name,
            order: data.order,
            forms: new Map,
            events: new EventEmitter,
            messages: null,
            form_action_menu_open: false,
            form_action_menu_id: null,
            form_action_menu_popper: null,
            form_action_menu_timer: null
        };
        for (let form of data.forms) {
            let $form = new SectionForm(this, form);
            this.state.forms.set($form.id, $form);
        }
    };

    /**
     * Get delete modal instance
     *
     * If modal doesn't exist, it will be created and cached for future calls
     *
     * @readonly
     *
     * @returns {Delete}
     */
    static get delete_modal() {
        if (this._delete_modal === undefined) {
            this._delete_modal = new DeleteModal;
        }
        return this._delete_modal;
    };

    /**
     * Get bid creator
     *
     * @readonly
     *
     * @returns {module:BidCreator}
     */
    get bid() {
        return this.state.bid;
    };

    /**
     * Get section id
     *
     * @readonly
     *
     * @returns {string}
     */
    get id() {
        return this.state.id;
    };

    /**
     * Get section name
     *
     * @readonly
     *
     * @returns {?string}
     */
    get name() {
        return this.state.name;
    };

    /**
     * Get section order
     *
     * @readonly
     *
     * @returns {number}
     */
    get order() {
        return this.state.order;
    };

    /**
     * Set section order
     *
     * @param {number} value
     */
    set order(value) {
        this.state.order = value;
    };

    /**
     * Get section forms
     *
     * @readonly
     *
     * @returns {Map<string, SectionForm>}
     */
    get forms() {
        return this.state.forms;
    };

    /**
     * Add event listener
     *
     * @param {string} event - Event name
     * @param {function} closure - Event handler
     * @returns {Section}
     */
    on(event, closure) {
        this.state.events.on(event, closure);
        return this;
    };

    /**
     * Get messages instance
     *
     * Will create one if it doesn't exist already
     *
     * @returns {FlashMessage.Controller}
     */
    getMessages() {
        if (this.state.messages === null) {
            let messages = new FlashMessage.Controller;
            this.elem.messages = this.elem.root.fxChildren('messages');
            this.elem.messages.append(messages.render());
            messages.boot(this.elem.messages);
            this.state.messages = messages;
        }
        return this.state.messages;
    };

    /**
     * Clear all flash messages if one has already been defined
     */
    clearMessages() {
        if (this.state.messages === null) {
            return;
        }
        this.state.messages.deleteAllMessages();
    };

    /**
     * Start a timer to hide the form action menu
     */
    startFormActionMenuTimer() {
        if (this.state.form_action_menu_timer !== null) {
            return;
        }
        this.state.form_action_menu_timer = setTimeout(() => this.hideFormActionMenu(), 1000);
    };

    /**
     * Clear active timer to hide form action menu
     */
    clearFormActionMenuTimer() {
        if (this.state.form_action_menu_timer === null) {
            return;
        }
        clearTimeout(this.state.form_action_menu_timer);
        this.state.form_action_menu_timer = null;
    };

    /**
     * Open form action menu next to specified element for a specific form
     *
     * @param {HTMLElement} element - Reference element to render popper with
     * @param {string} form_id - Form id for all menu actions to use
     */
    openFormActionMenu(element, form_id) {
        this.state.form_action_menu_id = form_id;
        this.elem.form_action_menu.show();
        this.state.form_action_menu_popper = new Popper(element, this.elem.form_action_menu[0], {
            placement: 'left'
        });
        this.state.form_action_menu_open = true;
        this.startFormActionMenuTimer();
    };

    /**
     * Toggle open state of action menu for a specific form
     *
     * @param {HTMLElement} element - Reference element to render popper with
     * @param {string} form_id - Form id for all menu actions to use
     */
    toggleFormActionMenu(element, form_id) {
        if (!this.state.form_action_menu_open) {
            this.openFormActionMenu(element, form_id);
        } else if (form_id === this.state.form_action_menu_id) {
            // if menu triggered from same form, then we just toggle the state
            this.hideFormActionMenu();
        } else {
            // since we are changing to another form, then we need to reset before opening again
            this.hideFormActionMenu();
            this.openFormActionMenu(element, form_id);
        }
    };

    /**
     * Handle form action menu actions
     *
     * @param {string} action
     */
    handleFormActionMenuAction(action) {
        switch (action) {
            case 'delete':
                this.getForm(this.state.form_action_menu_id).startDelete();
                this.hideFormActionMenu();
                break;
        }
    };

    /**
     * Hide form action menu and reset state
     */
    hideFormActionMenu() {
        if (!this.state.form_action_menu_open) {
            return;
        }
        this.elem.form_action_menu.hide();
        this.state.form_action_menu_popper.destroy();
        this.state.form_action_menu_popper = null;
        this.state.form_action_menu_open = false;
        this.state.form_action_menu_id = null;
        this.state.form_action_menu_timer = null;
    };

    /**
     * Set section name
     *
     * @param {?string} name
     *
     * @emits module:BidCreator/Section~renamed
     */
    setName(name) {
        let old_name = this.state.name;
        this.state.name = name;
        if (this.isBooted()) {
            this.elem.name.text(name);
            if (this.elem.name.hasClass('t-empty')) {
                this.elem.name.removeClass('t-empty');
            }
        }
        this.state.events.emit('renamed', {
            old_name: old_name,
            new_name: name
        });
    };

    /**
     * Get form by id
     *
     * @param {string} id - Form uuid
     * @returns {SectionForm}
     */
    getForm(id) {
        return this.state.forms.get(id);
    };

    /**
     * Add form
     *
     * @param {SectionForm} form
     *
     * @emits module:BidCreator/Section~formAdded
     */
    addForm(form) {
        this.state.forms.set(form.id, form);
        $(form.render()).appendTo(this.elem.forms);
        form.boot();
        this.state.events.emit('form-added', {
            form: form
        });
    };

    /**
     * Get reorder batch request
     *
     * @param {string[]} list - Array of form ids in the needed order
     * @returns {(boolean|Api.BatchRequest.Multiple)}
     */
    getReorderBatchRequest(list) {
        let requests = [];
        list.forEach((form_id, order) => {
            order++; // increment key since we start at 1 instead of zero
            let form = this.getForm(form_id);
            // form can be undefined when getting a batch request which contains new forms which haven't been added yet
            if (form === undefined) {
                return;
            }
            // if order didn't change then do nothing
            if (form.order === order) {
                return;
            }
            let request = new Api.BatchRequest.Single('bid-item-section-form', 'partial-update', {
                id: form_id,
                order: order
            });
            request.promise.done(() => {
                // update section form order so it is up-to-date
                form.order = order;
            });
            requests.push(request);
        });
        if (requests.length === 0) {
            return false;
        }
        let batch_request = new Api.BatchRequest.Multiple(true);
        for (let request of requests) {
            batch_request.add(request);
        }
        return batch_request;
    };

    /**
     * Update form order
     *
     * Sends API request to update any forms which order was changed in the provided list, sets the internal form order,
     * and reorders form elements in the DOM
     *
     * @param {string[]} list - Array of form uuid's
     */
    updateFormOrder(list) {
        let batch_request = this.getReorderBatchRequest(list);
        PubSub.Handler.publish(PubSub.Topics.Save.ENQUEUE_REQUEST, batch_request);
        this.setFormOrder(list);
    };

    /**
     * Set form order
     *
     * Updates internal form map to be in the specified order and optionally reorders form elements in the DOM
     *
     * @param {string[]} list - Array of form uuid's
     * @param {boolean} [reorder=true] - Determines if forms are reordered in the DOM
     * @param {boolean} [notify=true] - Determines if events are fired
     */
    setFormOrder(list, reorder = true, notify = true) {
        let forms = new Map;
        for (let form_id of list) {
            let form = this.getForm(form_id);
            forms.set(form.id, form);
        }
        this.state.forms = forms;
        if (reorder) {
            this.reorderForms();
        }
        if (notify) {
            this.state.events.emit('forms-reordered', {
                order: list,
                section: this
            });
        }
    };

    /**
     * Reorder forms in DOM according to the order they are in the forms map
     */
    reorderForms() {
        if (!this.isBooted()) {
            return;
        }
        // loop through map and adjust placement
        let first = true,
            last_elem;
        for (let form of this.state.forms.values()) {
            if (!first) {
                form.elem.root.insertAfter(last_elem);
            }
            last_elem = form.elem.root;
            if (first) {
                first = false;
            }
        }
    };

    /**
     * Delete form form internal cache by id
     *
     * @param {string} form_id - Form uuid
     */
    deleteForm(form_id) {
        let form = this.getForm(form_id);
        this.state.forms.delete(form_id);
        this.state.events.emit('form-deleted', {
            form: form
        });
    };

    /**
     * Get queue request for deletion
     *
     * @returns {Api.BatchRequest.Single}
     */
    getDeleteApiRequest() {
        return new Api.BatchRequest.Single('bid-item-section', 'delete', {
            id: this.state.id
        });
    };

    /**
     * Start delete process by opening confirmation modal
     */
    startDelete() {
        Section.delete_modal.open(this);
    };

    /**
     * Send delete request to server and remove from DOM if successful
     *
     * If removing a section in the middle of the bid, then a batch request will be sent which contains the new
     * order for all surrounding sections. After which the bid will reorder sections in the DOM.
     *
     * Note: this method is meant for one-off section deletes and shouldn't be used in any bulk operation, much more efficient
     *       means will be needed for that
     *
     * @returns {Promise}
     */
    delete() {
        return new Promise((resolve, reject) => {
            let request = this.getDeleteApiRequest();
            let sections = Array.from(this.bid.sections.keys());
            let index = sections.indexOf(this.state.id);
            sections.splice(index, 1);
            let reorder_request = this.bid.getReorderBatchRequest(sections);
            // if no reorder requests are needed, then we just send the single delete request
            if (reorder_request === false) {
                request.promise.then(() => {
                    this.destroy();
                    resolve();
                }, (result) => {
                    reject(result);
                });
                PubSub.Handler.publish(PubSub.Topics.Save.SEND_REQUEST, request);
                return;
            }
            // otherwise, we send an atomic batch request with the delete and section reorders, which upon success will
            // destroy this section and reorder the bid sections
            let batch_request = new Api.BatchRequest.Multiple(true);
            batch_request.add(request);
            batch_request.merge(reorder_request);
            batch_request.promise.then(() => {
                this.destroy();
                this.bid.setSectionOrder(sections);
                resolve();
            }, (result) => {
                reject(result);
            });
            PubSub.Handler.publish(PubSub.Topics.Save.SEND_REQUEST, batch_request);
        });
    };

    /**
     * Destroy section, remove all associated elements from DOM, unbind external events, and remove from bid cache
     *
     * @emits module:BidCreator/Section~destroyed
     */
    destroy() {
        this.bid.deleteSection(this.state.id);
        for (let form of this.state.forms.values()) {
            form.destroy();
        }
        this.elem.root.remove();
        // @todo needs tested
        $(document).fxEventDestroy(['click', 'touchend'], {namespace: `section.${this.state.id}`});
        this.state.events.emit('destroyed');
    };

    /**
     * Trigger resize of section and nested forms
     */
    resize() {
        for (let form of this.state.forms.values()) {
            form.resize();
        }
    };

    /**
     * Determines if any section components have incomplete uploads
     *
     * @returns {boolean}
     */
    hasIncompleteUploads() {
        for (let form of this.state.forms.values()) {
            if (!form.hasIncompleteUploads()) {
                continue;
            }
            return true;
        }
        return false;
    };

    /**
     * Validate section and all child components
     *
     * @returns {Promise}
     */
    validate() {
        let promises = [];
        this.forms.forEach((form) => {
            promises.push(form.validate().catch(data => {return data;}));
        });
        return new Promise((resolve, reject) => {
            this.clearMessages();
            let valid = true;
            if (this.state.name === null) {
                let message = FlashMessage.Message.make('Section name cannot be blank, click edit below to rename the section', FlashMessage.Message.Type.ERROR);
                this.getMessages().addMessage(message);
                valid = false;
            }
            // if we need to enforce they have at least one form, that would be done here
            Promise.all(promises).then((forms) => {
                for (let form of forms) {
                    if (form.valid) {
                        continue;
                    }
                    valid = false;
                    break;
                }
                if (valid) {
                    resolve({valid: true});
                    return;
                }
                reject({valid: false});
            });
        });
    };

    /**
     * Determines if section is booted
     *
     * @returns {boolean}
     */
    isBooted() {
        return this.state.booted;
    };

    /**
     * Boot section
     */
    async boot() {
        if (!this.isRendered()) {
            throw new Error('Unable to boot section which has not been rendered');
        }

        // find elements
        this.elem.root = this.bid.elem.content.fxFind('section', {id: this.state.id});
        this.elem.name = this.elem.root.fxFind('name');
        this.elem.form_action_menu = this.elem.root.fxChildren('form-action-menu');
        this.elem.forms = this.elem.root.fxChildren('forms');

        // boot forms
        for (let form of this.state.forms.values()) {
            await form.boot();
        }

        // event handlers
        const that = this;

        // if a click happens anywhere on the document and the menu is open, we close it
        $(document).fxEvent(['click', 'touchend'], function (e) {
            if (!that.state.form_action_menu_open) {
                return;
            }
            that.hideFormActionMenu();
        }, {namespace: `section.${this.state.id}`});

        // when the mouse enters the menu, we clear the timer so it won't disappear on them since they are interacting
        // with it
        this.elem.form_action_menu.fxEvent('mouseenter', () => {
            this.clearFormActionMenuTimer();
        });

        // when the mouse leaves the menu, we start a timer to hide the menu
        this.elem.form_action_menu.fxEvent('mouseleave', () => {
            this.startFormActionMenuTimer();
        });

        // when someone clicks an action within the form action menu, we send it to the action handler
        this.elem.form_action_menu.fxEventWatcher(['click', 'touchstart'], 'action', function (e) {
            e.preventDefault();
            e.stopPropagation();
            that.handleFormActionMenuAction($(this).data('action'));
            return false;
        });

        // when someone clicks the action menu icon, we toggle the form action menu
        this.elem.forms.fxClickWatcher('action-menu-trigger', function (e) {
            e.preventDefault();
            that.toggleFormActionMenu(this, $(this).fxParents('form').data('id'));
            return false;
        });

        this.state.booted = true;
    };

    /**
     * Determines if section is rendered
     *
     * @returns {boolean}
     */
    isRendered() {
        return this.state.rendered;
    };

    /**
     * Render section
     *
     * @returns {string}
     */
    render(count) {
        let content = '';
        this.state.forms.forEach((form) => {
            content += form.render();
        });
        this.state.rendered = true;
        return section_tpl({
            id: this.state.id,
            name: this.state.name !== null ? this.state.name : `Section ${count}`,
            unsaved: this.state.name === null,
            forms: content
        });
    };
}

module.exports = Section;
