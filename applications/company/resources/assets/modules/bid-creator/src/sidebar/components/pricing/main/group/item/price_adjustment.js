'use strict';

const Base = require('./base');
const LineItem = require('../../../../../../entities/line_item');
const Utils = require('../../../../../../utils');

/**
 * Price adjustment class
 *
 * Base class for price adjustment items, not used directly
 *
 * @memberof module:BidCreator/Sidebar/Components/Pricing/Main/Group/Item
 */
class PriceAdjustment extends Base {
    /**
     * Override calculate total to pass proper subtotal into line item total calculation since price adjustments
     * can be relative (i.e. percentages)
     *
     * @returns {Decimal}
     */
    calculateTotal() {
        let total = Utils.getDecimal('0.00');
        for (let line_item of this.state.line_items.values()) {
            total = total.plus(line_item.item.getTotal(this.group.getSubtotal(this.id), false));
        }
        return total;
    };

    /**
     * Recalculate total
     *
     * Used when changes happen upstream and this item's total's need updated
     */
    recalculateTotal() {
        if (this.isBooted()) {
            // if recalculating, the subtotal might of changes so we need to re-render all percentage based line item totals
            for (let info of this.state.line_items.values()) {
                if (info.item.amount_type !== LineItem.AmountType.PERCENTAGE) {
                    continue;
                }
                this.renderLineItem(info);
            }
        }
        super.recalculateTotal();
    };

    /**
     * Get list item title
     *
     * @param {Object} item
     * @returns {string}
     */
    getListItemTitleFromLineItem(item) {
        let title = `${item.name} (${item.getRawSubtotal()}`;
        if (item.amount_type === LineItem.AmountType.PERCENTAGE) {
            title += ` - ${item.getTotal(this.group.getSubtotal(this.id))}`;
        }
        title += ')';
        return `<div class="t-adjustment">${title}</div>`;
    };

    /**
     * Handle edit action
     *
     * @param {Object} item - List item config
     */
    handleEditAction(item) {
        this.group.parent.component.panel_stack.push('line-item-edit', {
            params: [{
                line_item: item.storage.line_item
            }]
        });
    };
}

module.exports = PriceAdjustment;
