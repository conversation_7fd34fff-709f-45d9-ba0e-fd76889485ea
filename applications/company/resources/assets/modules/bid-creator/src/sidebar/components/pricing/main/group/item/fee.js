'use strict';

const LineItem = require('../../../../../../entities/line_item');
const PriceAdjustment = require('./price_adjustment');

/**
 * @memberof module:BidCreator/Sidebar/Components/Pricing/Main/Group/Item
 */
class Fee extends PriceAdjustment {
    /**
     * Constructor
     */
    constructor() {
        super();
        Object.assign(this.state, {
            id: 'fee',
            title: 'Fees',
            no_items_text: 'No fees',
            add_action_label: 'Add Fee'
        });
    };

    /**
     * Handle add action
     */
    handleAddAction() {
        this.group.parent.component.panel_stack.push('line-item-add', {
            params: [{
                type: LineItem.Type.FEE,
                lock: true
            }]
        });
    };
}

module.exports = Fee;
