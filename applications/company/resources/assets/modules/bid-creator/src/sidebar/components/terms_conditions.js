/**
 * @module BidCreator/Sidebar/Components/TermsConditions
 */

'use strict';

const Accordion = require('../accordion');
const Component = require('../component');
const ComponentNavItem = require('../nav-items/component_nav_item');
const ContentEntity = require('../../entities/content');
const ContentType = require('./terms-conditions/content_type');
const LineItem = require('../../entities/line_item');
const PubSub = require('../../pubsub');

const terms_conditions_tpl = require('@cam-bid-creator-tpl/sidebar/components/terms-conditions/main.hbs');

/**
 * @typedef {Object} ContentTypeConfig
 * @property {module:BidCreator/Sidebar/Components/TermsConditions/ContentType} instance - ContentType instance
 * @property {number} type - Type
 * @property {Object} title
 * @property {string} title.singular - Singular version of title
 * @property {string} title.plural - Plural version of title
 */

/**
 * @alias module:BidCreator/Sidebar/Components/TermsConditions
 */
class TermsConditions extends Component {
    /**
     * Constructor
     *
     * @param {module:BidCreator.Sidebar} sidebar
     * @param {Object} data
     */
    constructor(sidebar, data) {
        super(sidebar, 'terms_conditions', 'Terms & Conditions');

        this.state.accordion = new Accordion.Controller;

        this.state.types = new Map([
            [ContentEntity.Type.CONTRACT, {
                instance: new ContentType(this, {
                    type: ContentEntity.Type.CONTRACT,
                    title: {
                        singular: 'Contract',
                        plural: 'Contracts'
                    }
                })
            }],
            [ContentEntity.Type.DISCLAIMER, {
                instance: new ContentType(this, {
                    type: ContentEntity.Type.DISCLAIMER,
                    title: {
                        singular: 'Disclaimer',
                        plural: 'Disclaimers'
                    }
                })
            }],
            [ContentEntity.Type.WAIVER, {
                instance: new ContentType(this, {
                    type: ContentEntity.Type.WAIVER,
                    title: {
                        singular: 'Waiver',
                        plural: 'Waivers'
                    }
                })
            }],
            [ContentEntity.Type.WARRANTY, {
                instance: new ContentType(this, {
                    type: ContentEntity.Type.WARRANTY,
                    title: {
                        singular: 'Warranty',
                        plural: 'Warranties'
                    }
                })
            }],
            [ContentEntity.Type.ACKNOWLEDGEMENT, {
                instance: new ContentType(this, {
                    type: ContentEntity.Type.ACKNOWLEDGEMENT,
                    title: {
                        singular: 'Acknowledgement',
                        plural: 'Acknowledgements'
                    }
                })
            }]
        ]);

        this.state.line_items = new Map;
        this.state.products = new Map;
        this.state.content = new Map;

        // load existing content
        if (data.content.length > 0) {
            for (let content of data.content) {
                let entity = new ContentEntity(content, true);
                this.state.types.get(entity.type).instance.handleContent(entity);
            }
        }

        // listen for any line item updates so we can load the necessary content
        PubSub.Handler.subscribe(PubSub.Topics.LineItem.HANDLED, (message, data) => {
            this.handleLineItem(data.line_item);
        });
    };

    /**
     * Get nav item for sidebar
     *
     * @returns {ComponentNavItem}
     */
    getNavItem() {
        return new ComponentNavItem(this.name, {
            icon: 'remix-icon--document--file-paper-2-line',
            classes: ['t-terms-conditions']
        });
    };

    /**
     * Get accordion controller
     *
     * @readonly
     *
     * @returns {Accordion.Controller}
     */
    get accordion() {
        return this.state.accordion;
    };

    /**
     * Get content type config
     *
     * @param {number} type
     * @returns {(undefined|ContentTypeConfig)}
     */
    getType(type) {
        return this.state.types.get(type);
    };

    /**
     * Get content by id
     *
     * If content isn't found, it will be fetched and a new entity will be saved
     *
     * @param {string} content_id
     * @returns {Promise<any>}
     */
    getContent(content_id) {
        let promise = this.state.content.get(content_id);
        if (promise === undefined) {
            promise = new Promise((resolve, reject) => {
                ContentEntity.getEntity(content_id).then((entity) => {
                    let content = {
                        type: entity.get('type'),
                        product_ids: {}
                    };
                    this.getType(content.type).instance.addContent(entity).then((content_entity) => {
                        content.item_id = content_entity.id;
                        resolve(content);
                    }, (error) => reject(error));
                }, (error) => {
                    reject(error);
                });
            });
            this.state.content.set(content_id, promise);
        }
        return promise;
    };

    /**
     * Unregister a product with content, if the content no longer has any products tied to it and it wasn't added
     * via the manager modal, then we remove the content
     *
     * @param {string} content_id
     * @param {string} product_id
     */
    removeContentProduct(content_id, product_id) {
        this.getContent(content_id).then((content) => {
            if (content.product_ids[product_id] !== undefined) {
                content.product_ids[product_id]--;
                if (content.product_ids[product_id] === 0) {
                    delete content.product_ids[product_id];
                }
            }
            // if no more products are tied to content, then we remove it
            if (Object.keys(content.product_ids).length === 0) {
                this.getType(content.type).instance.deleteContent(content.item_id);
            }
        });
    };

    /**
     * Unregister line item with a product
     *
     * We also unregister this product with any associated content since each line item registers a product when it's
     * handled.
     *
     * @param {string} product_id
     * @param {string} line_item_id
     * @param {boolean} [content_only=false] - Determines if we only unregister from content and leave line item alone
     */
    removeProductLineItem(product_id, line_item_id, content_only = false) {
        this.getProduct(product_id).then((product) => {
            if (!content_only) {
                let index = product.line_item_ids.indexOf(line_item_id);
                if (index !== -1) {
                    product.line_item_ids.splice(index, 1);
                }
            }
            for (let content_id of product.content_ids) {
                this.removeContentProduct(content_id, product_id);
            }
        });
    };

    /**
     * Remove line item from internal cache, unregister it with product list
     *
     * @param {string} line_item_id
     */
    removeLineItem(line_item_id) {
        let product_id = this.state.line_items.get(line_item_id);
        if (product_id === undefined) {
            return;
        }
        this.removeProductLineItem(product_id, line_item_id);
        this.state.line_items.delete(line_item_id);
    };

    /**
     * Get product by id
     *
     * Caches all content id's associated with product.
     *
     * @param {string} product_id
     * @returns {Promise<object>}
     */
    getProduct(product_id) {
        let promise = this.state.products.get(product_id);
        if (promise === undefined) {
            promise = new Promise((resolve, reject) => {
                LineItem.Product.getEntity(product_id).then((entity) => {
                    let content = entity.get('bid_content', []),
                        content_ids = [];
                    if (content.length > 0) {
                        for (let item of content) {
                            content_ids.push(item.id);
                        }
                    }
                    resolve({
                        line_item_ids: [],
                        content_ids: content_ids
                    });
                }, (error) => {
                    reject(error);
                });
            });
            this.state.products.set(product_id, promise);
        }
        return promise;
    };

    /**
     * Handle line item
     *
     * @param {module:BidCreator/Entities/LineItem/Base} line_item
     */
    handleLineItem(line_item) {
        // if line item isn't a product then we skip
        if (line_item.type !== LineItem.Type.PRODUCT) {
            return;
        }
        let prev_product_id = this.state.line_items.get(line_item.id);
        if (prev_product_id === line_item.product_id) {
            // if the product didn't change from the last time it was loaded, then we do nothing
            return;
        }
        // if we haven't seen this line item before, then we attach an event to handle removing the line item when it's
        // deleted
        if (prev_product_id === undefined) {
            line_item.on('destroyed', () => {
                this.removeLineItem(line_item.id);
            });
        }
        this.state.line_items.set(line_item.id, line_item.product_id);
        this.getProduct(line_item.product_id).then((product) => {
            product.line_item_ids.push(line_item.id);

            let content_promises = [];
            for (let content_id of product.content_ids) {
                content_promises.push(this.getContent(content_id));
            }
            Promise.all(content_promises).then((promises) => {
                for (let content of promises) {
                    content.product_ids[line_item.product_id] = (content.product_ids[line_item.product_id] || 0) + 1;
                }
            }).then(() => {
                // after content is handled, we check to see if the product has changed for the line item. if it has
                // we unlink the product from the content. this is purposefully done after the new content is added so we don't
                // unnecessarily delete and re-add the same content if they happen to share
                if (prev_product_id !== undefined && prev_product_id !== line_item.product_id) {
                    // we reuse this method (even though we aren't technically removing the line item from the product since
                    // it's still needed) to unlink the old product from the content
                    this.removeProductLineItem(prev_product_id, line_item.id, true);
                }
            });
        });
    };

    /**
     * Track content entity
     *
     * @param {ContentEntity} entity
     */
    trackContent(entity) {
        // we only track entities which were created from bid content
        if (entity.bid_content_id === null) {
            return;
        }
        let content = this.state.content.get(entity.bid_content_id);
        if (content === undefined) {
            content = {
                type: entity.type,
                product_ids: {},
                item_id: entity.id
            };
            this.state.content.set(entity.bid_content_id, Promise.resolve(content));
        }
    };

    /**
     * Destroy content using entity
     *
     * Removes tracking of entity from internal cache
     *
     * @param {ContentEntity} entity
     */
    destroyContent(entity) {
        // since we only track entities which were created from bid content, we can ignore custom content
        if (entity.bid_content_id === null) {
            return;
        }
        this.state.content.delete(entity.bid_content_id);
    };

    /**
     * Boot terms conditions component
     *
     * @param {Object} root - jQuery element for panel
     */
    boot(root) {
        super.boot(root);

        for (let content of this.state.types.values()) {
            content.instance.boot();
        }

        this.elem.types = this.elem.content.fxFind('types');

        this.state.accordion.boot(this.elem.types);
    };

    /**
     * Render panel content
     *
     * @returns {string}
     */
    renderPanelContent() {
        return terms_conditions_tpl({
            accordion: this.state.accordion.render()
        });
    };
}

module.exports = TermsConditions;
