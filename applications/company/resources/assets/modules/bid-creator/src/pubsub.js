'use strict';

const PubSub = require('pubsub-js');

const Topics = {
    Save: {
        ENQUEUE_REQUEST: 'bid.save.enqueue_request',
        SEND_REQUEST: 'bid.save.send_request'
    },
    Section: {
        ADD: 'bid.section.add',
        Form: {
            FILE_ADD: 'bid.section.form.file_add'
        }
    },
    Event: {
        LINE_ITEM_PUSH: 'bid.event.line-item-push'
    },
    LineItem: {
        SAVE: 'bid.line-item.save',
        HANDLED: 'bid.line-item.handled'
    }
};

module.exports = {
    Handler: PubSub,
    Topics: Topics
};
