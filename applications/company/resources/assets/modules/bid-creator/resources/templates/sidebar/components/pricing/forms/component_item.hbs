<div class="c-ccsb-item">
    {{#if new_item}}
    <a class="c-ccsbi-remove" data-js="remove-item" data-index="{{index}}" data-type="{{type}}">
        <svg data-icon><use xlink:href="#remix-icon--system--delete-bin-2-line"></use></svg>
    </a>
    <div class="c-ccsbi-option">
        <div class="f-field">
            <select class="f-f-input" required data-parsley-required-message="" data-js="item" data-index="{{index}}"
                    data-fx-form-input="dynamic-dropdown" data-type="{{type}}"></select>
        </div>
    </div>
    <div class="c-ccsbi-quantity">
        <div class="f-field">
            <input class="f-f-input" required data-parsley-required-message=""
                   type="text" data-js="quantity" data-original-value="" data-unit-price="" value=""
                   data-adjusted-amount="" data-fx-form-input="number" data-index="{{index}}" data-type="{{type}}">
        </div>
    </div>
    {{else}}
    <div class="c-ccsbi-title">
        <span class="c-ccsbit-name">{{name}}</span>
        <span data-tooltip data-type="info">{{{tooltip}}}</span>
    </div>
    <div class="c-ccsbi-quantity">
        <div class="f-field">
            <input class="f-f-input" type="text" data-js="quantity" data-original-value="{{original_total_quantity}}" data-unit-price="{{unit_price}}" value="{{total_quantity}}" data-adjusted-amount="" data-fx-form-input="number">
        </div>
    </div>
    {{/if}}
</div>