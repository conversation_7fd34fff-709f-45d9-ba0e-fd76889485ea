/**
 * @module UserProfile
 */

'use strict';

const Router = require('@ca-package/router');

const layout_tpl = require('@cam-user-profile-tpl/layout.hbs');

/**
 * Main controller for user profile module
 *
 * @memberof module:UserProfile
 */
class Controller {
    /**
     * User profile constructor
     *
     * @param {module:Layout.Controller} layout
     */
    constructor(layout) {
        this.state = {
            layout
        };
        this.elem = {};
        this.boot();
    };

    /**
     * Get router instance
     *
     * @readonly
     *
     * @returns {module:Router.Controller}
     */
    get router() {
        return this.state.router;
    };

    /**
     * Boot user profile module
     *
     * @param {jQuery} root
     */
    boot(root) {
        this.state.layout.setModeWindow();
        this.state.layout.setTitle('User Profile');
        this.elem.root = $(this.render());
        this.state.layout.elem.content.append(this.elem.root);

        this.elem.content = this.elem.root.fxFind('content');
        this.state.router = new Router(require('./pages/main'), {
            base_path: '/user/profile',
            main_route_callback: (instance) => {
                instance.layout = this.state.layout;
                return instance;
            }
        });
        this.state.router.boot(this.elem.content);
    };

    /**
     * Render user profile module
     */
    render() {
        return layout_tpl({
            loader: window.fx_url.assets.IMAGE+'ajax-loader.gif'
        });
    };
}

module.exports = Controller;
