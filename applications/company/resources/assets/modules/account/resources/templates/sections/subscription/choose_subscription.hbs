<div class="row" data-new-subscription>
    <div class="small-12 medium-12 columns">
        <h4>Choose Subscription</h4>
        {{#if inactive}}
        <div class="custom-callout t-red">
            <strong>Account Status: <PERSON>rmant</strong><br>
            Your account does not have an active subscription. Please verify that your default payment information is up to date
            <a data-link-section="payment_methods">here</a> and then choose from one of the following subscription options
            to reinstate your company. After your subscription is updated, you will be able to access the full software
            and all of your existing data will be available.
        </div>
        {{/if}}
        {{#if trial}}
            <div class="custom-callout t-blue">
                <strong>Account Status: Trial</strong><br>
                Your company is currently in trial. You will not have access after <strong>{{trial_expires}}</strong>.  Please add your default payment method
                <a data-link-section="payment_methods">here</a> and then choose from one of the following subscription options
                to activate your company.
            </div>
        {{/if}}
        <h5>Subscription Options <small>(Select One)</small></h5>
    </div>
</div>
<div class="row align-center" data-new-subscription>
    {{#each subscription_options}}
    <div class="small-12 medium-{{this.columns}} columns {{#if this.hidden}}hidden{{/if}}">
        <div class="subscription-option" data-id="{{this.id}}" data-price="{{this.price_today}}" {{#if this.disabled_option}}disabled{{/if}}
        {{#if this.current}}disabled{{/if}} data-unit="{{this.interval_unit}}">
            <h4 class="option-name text-center no-margin">{{this.name}}</h4>
            <p class="no-margin text-center">
                {{#if this.single_user}}
                <span class="users-included">Includes 1 User</span>
                {{else}}
                    {{#if this.users}}
                    <span class="users-included">Includes {{this.users}} Users</span>
                    {{else}}
                    <span class="users-included">Includes Unlimited Users</span>
                    {{/if}}
                {{/if}}
            </p>
            {{#if this.single_interval}}
            <h5 class="subscription-price {{#if this.annual}}no-margin-bottom{{else}}monthly{{/if}}">{{this.price}}/{{#if this.annual}}year{{else}}month{{/if}}</h5>
                {{#if this.annual}}
                <p class="text-center {{#if this.disabled_option}}annual-wrapper{{/if}}">
                    <span class="annual-billed">Billed Annually</span>
                </p>
                {{/if}}
            {{else}}
            <h5 class="subscription-price interval-length">{{this.price}}<sup>*</sup></h5>
            <p class="text-center">
                <span class="capitalize">
                    <sup>*</sup>Charged Every {{this.interval_length}} {{this.interval_unit_name}}s
                </span>
                {{#if this.annual}}
                    <span class="annual-billed">Billed Annually</span>
                {{/if}}
            </p>
            {{/if}}
            {{#each adjustments}}
            <div class="subscription-adjustment-wrapper">
                <span class="adjustment-title capitalize">{{type}}</span>
                <ul>
                    {{#each items}}
                    <li>
                        {{this.name}}: {{this.cost}}
                        {{#if occurrence_count}}
                            ({{occurrence_count}} {{../../interval_unit_name}}{{#ifeq occurrence_count 1}}{{else}}s{{/ifeq}})
                        {{/if}}
                        {{#if delay_count}}
                            {{#if show_delay}}
                                (starts after {{delay_count}} {{../../interval_unit_name}}{{#ifeq delay_count 1}}{{else}}s{{/ifeq}})
                            {{/if}}
                        {{/if}}
                    </li>
                    {{/each}}
                </ul>
            </div>
            {{/each}}
            {{#if this.current}}
                <span class="message current">Current Subscription</span>
            {{else}}
                {{#if this.disabled_option}}<span class="message disabled">Your active user count exceeds the total users available for this subscription.</span>{{/if}}
            {{/if}}
        </div>
    </div>
    {{/each}}
</div>
<div class="row align-center">
    <div class="button-group">
        <a class="button bar left" data-button-id="3">Annual</a>
        <a class="button bar right active" data-button-id="2">Monthly</a>
    </div>
    <div class="small-12 medium-12 cost-summary">
        <div class="callout due-callout">
            Total Amount Due Today: <span class="price"></span>*<br/>
            <small>*Total does not include any available credits.</small>
        </div>
    </div>
</div>
<div class="row" data-new-subscription>
    <div class="medium-12 columns">
        <div class="payment-method-message" data-js="no-payment-method">Please add default payment before saving.</div>
        <div class="error"><small>Subscription option is required.</small></div>
        <button class="button save">Save</button>
    </div>
</div>