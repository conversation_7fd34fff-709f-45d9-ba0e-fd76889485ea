.section[data-id="payment-methods"] {
    .add-button-container {
        float: right;
    }
    .callout {
        h4 {
            margin-bottom: 0;
            cursor: pointer;
        }
        p {
            cursor: pointer;
            font-size: .95rem;
        }
        .payment-type {
            color: #000000;
            height: 17px;
            width: 17px;
            position: absolute;
            right: 10px;
            top: 10px;
        }
        .default-payment {
            float: right;
            color: #999999;
            font-style: italic;
            font-size: .9rem;
        }
        .button-loading {
            &.show {
                left: 50%;
                top: 37%;
            }
        }
    }
}

.m-pm-edit-form {
    input {
        &.response-error {
            margin-bottom: 0rem;
        }
        &.parsley-error {
            margin-bottom: 0rem;
        }
    }
    small {
        &.response-error-message {
            margin-bottom: 1rem;
            color: #B94A48;
            font-size: 12.6px;
        }
    }
    .pm-edit-buttons {
        margin-top: 1rem
    }
    .row {
        &.credit_change {
            display: none;
            &.show {
                display: flex;
            }
        }
        &.credit {
            display: none;
            &.show {
                display: flex;
            }
        }
    }
}

.m-pm-add-form {
    input {
        &.response-error {
            margin-bottom: 0rem;
        }
        &.parsley-error {
            margin-bottom: 0rem;
        }
    }
    small {
        &.response-error-message {
            margin-bottom: 1rem;
            color: #B94A48;
            font-size: 12.6px;
        }
    }
    .pm-add-buttons {
        margin-top: 1rem
    }
    .tabs-title {
        margin-bottom: .3rem;
        width: 50%;
    }
    .tabs .tabs-title > a {
        padding: 1rem;
        text-align: center;
        background: #F0F0F0;
        color: #000000;
        font-weight:normal;
        text-transform: none;
        font-size: .9rem;
    }
    #payment-info.tabs .tabs-title.is-active > a {
        background-color: #b0b1b2;
        color: #000000;
    }
    .tabs-content {
        margin-top: 0;
    }
    .tabs-panel {
        padding-top: 0;
    }
    .account-type {
        display: inline-block;
        margin-right: 2rem;
        font-weight: normal;
        margin-bottom: 1rem;
    }
}
