'use strict';

const Page = require('@ca-package/router/src/page');

const Api = require('../../../api');
const LegacySchedule = require('./legacy/schedule');

const schedule_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/schedule.hbs');

class Schedule extends Page {
    /**
     * Fetch data for page
     *
     * @returns {object}
     */
    async fetchData() {
        let customer = {
            customer: {
                fields: ['email', 'is_unsubscribed']
            }
        };
        let {data: entity} = await Api.Resources.Properties().relations(customer).fields(['id', 'address', 'city', 'state', 'zip', 'latitude', 'longitude'])
            .retrieve(this.state.property_id);
        return entity.data;
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.elem.loader.show();

        await super.load(request, next);
        this.state.customer_id = request.data.customer_id;
        this.state.property_id = request.data.property_id;
        let data = await this.fetchData();

        LegacySchedule.projectSchedule({
            id: request.params.project_id,
            customer: data.customer,
            address: data.address,
            city: data.city,
            state: data.state,
            zip: data.zip,
            latitude: data.latitude,
            longitude: data.longitude
        });
        this.elem.loader.hide();
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.loader = this.elem.root.fxFind('loader');
        LegacySchedule.setup({
            loader: this.parent.elem.loader
        });
        e$(this.elem.root[0]).find('[data-reveal]').foundation();
    };

    /**
     * Render page
     */
    render() {
        return schedule_tpl({
            info: window.fx_url.assets.IMAGE+'icons/info.png'
        });
    };
}

module.exports = Schedule;
