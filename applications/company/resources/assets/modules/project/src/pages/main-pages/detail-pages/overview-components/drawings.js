'use strict';

const Api = require('../../../../api');

const Component = require('@ca-package/router/src/component');

const drawings_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/overview-components/drawings.hbs');
const item_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/overview-components/drawings/item.hbs');

class Drawings extends Component {
    /**
     * Populate template with data
     *
     * @param {object} data
     */
    populate(data) {
        if (data.length > 0) {
            this.elem.no_drawings.hide();
        }
        for (let drawing_item of data) {
            this.elem.list.append(item_tpl({
                link: drawing_item.repair_plan_media_urls.original,
                name: drawing_item.name
            }));
        }
    };

    /**
     * Load component
     *
     * @param {object} request
     */
    async load(request) {
        await super.load(request);

        let collection = await Api.Resources.Drawings()
            .filters({
                'project_id': request.params.project_id,
                'status': Api.Constants.Drawings.Status.FINALIZED
            })
            .relations({
                'repair_plan_media_urls': {}
            })
            .sorts({
                'created_at': 'asc'
            })
            .all();

        let drawings = collection.data.entities;
        if (drawings.length === 0) {
            return;
        }
        drawings = drawings.map((entity) => {
            return entity.data;
        });
        this.populate(drawings);
    };

    /**
     * Unload component
     *
     * @param {object} request
     */
    async unload(request) {
        this.elem.list.empty();
        this.elem.no_drawings.show();
        await super.unload(request);
    };

    /**
     * Boot component
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        this.elem.list = this.elem.root.fxFind('list');
        this.elem.no_drawings = this.elem.root.fxFind('no-drawings');

        this.elem.add_drawing = this.elem.root.fxFind('add-drawing');
        this.elem.add_drawing.fxClick(() => {
            this.router.externalRedirect(window.fx_pages.DRAWINGS_CREATE, null, {
                project_id: this.router.current_route.request.params.project_id
            });
        }, true);
    };

    /**
     * Render component
     */
    render() {
        return drawings_tpl();
    };
}

module.exports = Drawings;
