'use strict';

const Api = require('../../../../api');

const Component = require('@ca-package/router/src/component');

const property_image_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/overview-components/property_image.hbs');

class PropertyImage extends Component {
    /**
     * Get or set property image modal
     *
     * @returns {object}
     */
    get image_modal() {
        if (this.state.image_modal === undefined) {
            let modal = require('@cam-project-js/modals/main/project/overview/property_image');
            this.state.image_modal = new modal();

            this.state.image_modal.on('saved', () => {
                this.reloadProperty();
            });
        }
        return this.state.image_modal;
    };

    /**
     * Get directions link for Google
     *
     * @param {object} data
     * @returns {string}
     */
    getDirectionsLink(data) {
        return 'https://www.google.com/maps/dir/?api=1&destination='+encodeURIComponent(data.address+' '+data.city+' '+data.state+' '+data.zip);
    };

    /**
     * Handle delete for property image
     */
    deleteImage() {
        Api.Resources.Properties().partialUpdate(
            this.state.property_id, {
                'image_file_id': null
            }).then((entity, response) => {
                this.reloadProperty();
        }, () => {}); // @todo add logging
    };

    /**
     * Populate template with data
     *
     * @param {object} data
     */
    populate(data) {
        this.elem.directions_link.prop('href', this.getDirectionsLink(data));
        let image = data.image_file_id !== null ? data.image_media_urls.size_medium : data.street_view_image_media_urls.original;
        this.elem.property_image.prop('src', image);

        this.elem.edit_buttons.hide();
        this.elem.add_button.hide();

        if (data.image_file_id !== null) {
            this.elem.edit_buttons.show();
        } else {
            this.elem.add_button.show();
        }
    };

    /**
     * Handle event pushed to component from page
     *
     * @param {string} event
     * @param {object} data
     */
    handleEvent(event, data) {
        if (event === 'update') {
            this.populate(data.property);
        }
    };

    /**
     * Reload property after image is updated
     */
    reloadProperty() {
        let propertyRelations = {
            image_media_urls: {},
            street_view_image_media_urls: {}
        };
        Api.Resources.Properties().relations(propertyRelations)
            .retrieve(this.state.property_id).then((entity, response) => {
                this.populate(entity.data.data);
        }, () => {}); // @todo handle errors
    };

    /**
     * Load component
     *
     * @param {object} request
     */
    async load(request) {
        this.state.property_id = request.data.property_id;
        this.state.customer_id = request.data.customer_id;
        await super.load(request);
    };

    /**
     * Boot component
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.directions_link = this.elem.root.fxFind('directions');
        this.elem.property_image = this.elem.root.fxFind('property-image');
        this.elem.edit_buttons = this.elem.root.fxFind('edit');
        this.elem.add_button = this.elem.root.fxFind('add');

        this.elem.root.fxFind('manage-property-image').on('click.fx', () => {
            this.image_modal.open(this.state.property_id);
        });
        this.elem.root.fxFind('delete-property-image').on('click.fx', () => {
            this.deleteImage();
        });
    };

    /**
     * Render component
     */
    render() {
        return property_image_tpl({
            edit_project: window.project_data.user.can_edit
        });
    };
}

module.exports = PropertyImage;
