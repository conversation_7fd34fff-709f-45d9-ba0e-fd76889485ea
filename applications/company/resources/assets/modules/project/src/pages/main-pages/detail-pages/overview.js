'use strict';

const Api = require('../../../api');

const Page = require('@ca-package/router/src/page');

const overview_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/overview.hbs');

class Overview extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            task_total: null,
            task_open: null,
            task_completed: null,
            no_task_feature_modal: null,
            components: {
                property_image: require('./overview-components/property_image'),
                notes: require('./overview-components/notes'),
                info: require('./overview-components/info'),
                schedule: require('./overview-components/schedule'),
                files: require('./overview-components/files'),
                drawings: require('./overview-components/drawings'),
                costs: require('./overview-components/costs'),
                resulting: require('./overview-components/resulting')
            }
        });
        if (window.project_data.user.billing_allowed) {
            this.state.components['billing'] = require('./overview-components/billing');
            this.state.components['bids'] = require('./overview-components/bids');
            this.state.components['wisetack'] = require('./overview-components/wisetack');
        }
    };

    /**
     * Get or set no task feature modal
     *
     * @returns {object}
     */
    get no_task_feature_modal() {
        if (this.state.no_task_feature_modal === null) {
            let modal = require('@cam-project-js/modals/main/project/overview/task_feature_not_enabled');
            this.state.no_task_feature_modal = new modal();
        }
        return this.state.no_task_feature_modal;
    };

    /**
     * Populate template with data
     *
     * @param {object} data
     */
    populate(data) {
        this.elem.overview_link.prop('href', data.project_overview_media_urls.original);
        this.elem.add_task.prop('href', fx_pages.TASKS_CREATE.replace('{project_id}', this.state.id));
        this.elem.task_link.html('');

        let content = '';
        if (project_data.features.tasks) {
            this.elem.add_task.show();
            if (this.state.task_total > 0) {
                let link_text = `View ${this.state.task_total} Task`;
                if (this.state.task_total > 1) {
                    link_text = `${link_text}s`;
                }
                content = `<a target="_blank" href="${fx_pages.PROJECT_TASKS.replace('{project_id}', this.state.id)}">${link_text}</a><br><span>${this.state.task_open} Open | ${this.state.task_completed} Completed</span>`;
            } else {
                content = '<span class="no-results-display">No Tasks</span>';
            }
        } else {
            content = `<a data-js="no-task-feature-link">View Tasks</a>`
        }
        this.elem.task_link.append(content);
        this.elem.no_task_feature_link = this.elem.task_link.fxFind('no-task-feature-link');

        this.elem.no_task_feature_link.on('click.fx', () => {
            this.no_task_feature_modal.open();
        });
    };

    async fetchTasks() {
        let {data: tasks} = await Api.Resources.Tasks()
            .accept('application/vnd.adg.fx.collection-v1+json')
            .filter('association_type', Api.Constants.Tasks.AssociationType.PROJECT)
            .filter('association_id', this.state.id)
            .all();
        let open = 0,
            completed = 0;
        for (let {data: task} of tasks.entities) {
            switch(task.status) {
                case Api.Constants.Tasks.Status.ACTIVE:
                    open++;
                    break;
                case Api.Constants.Tasks.Status.COMPLETED:
                    completed++;
                    break;
            }
        }
        return {
            total: tasks.entities.length,
            open,
            completed
        };
    };

    /**
     * Fetch data for component
     *
     * @returns {object}
     */
    async fetchData() {
        let {data: entity} = await Api.Resources.Projects().accept('application/vnd.adg.fx.info-v1+json')
            .retrieve(this.state.id);
        this.state.data = entity.data;
        this.emitComponentEvent('update', this.state.data);
        this.populate(this.state.data);
        return this.state.data;
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.elem.loader.show();

        this.state.id = request.params.project_id;

        await super.load(request, next);
        this.state.tasks = await this.fetchTasks();
        this.state.task_total = this.state.tasks.total;
        this.state.task_open = this.state.tasks.open;
        this.state.task_completed = this.state.tasks.completed;
        request.data.project = await this.fetchData();

        this.elem.loader.hide();
    };

    /**
     * Unload modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        request.data.project_name = this.state.data !== undefined ? this.state.data.description : '';
        this.elem.task_link.empty();
        this.elem.add_task.hide();
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.overview_link = this.elem.root.fxFind('overview-link');
        this.elem.task_link = this.elem.root.fxFind('task-link');
        this.elem.add_task = this.elem.root.fxFind('add-task');
        this.elem.loader = this.elem.root.fxFind('loader');
    };

    /**
     * Render page
     */
    render() {
        return overview_tpl({
            // if user is not a primary and project costing for primary only is true
            hide_project_costs: !window.project_data.user.primary && window.project_data.company.project_costing_for_primary_only
        });
    };
}

module.exports = Overview;
