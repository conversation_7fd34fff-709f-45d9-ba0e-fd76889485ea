<form data-js="form">
    <h3 class="text-center">Cancel Project</h3>
    <div class="row">
        <div class="medium-12 columns">
            <p class="text-center content" style="font-size: 16px;">
                Are you sure you want to cancel this project?
            </p>
            <div class="callout small secondary">
                <p>
                    &bull; This will cancel any open appointments and bids<br>
                    &bull; Cancelled projects will <u>NOT</u> be included in metrics
                </p>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="medium-12 columns">
            <div class="f-field">
                <label class="f-f-label">Project Cancelled</label>
                <input name="cancelled_on" type="datetime" data-js="cancelled">
            </div>
        </div>

        <div class="medium-12 columns">
            <div class="f-field">
                <label class="f-f-label">Result
                    <span data-tooltip data-type="info">
                        Result tracks project outcomes for quick visibility and reporting. Edit options in Company Profile > Company Settings > Projects
                    </span>
                    <span class="f-fl-optional" data-js="result_optional">(Optional)</span>
                </label>
                <select data-js="result" id="result" name="result" value=""></select>
            </div>
        </div>
    </div>

    <div class="row" style="margin-top: 1rem;">
        <div class="medium-12 columns">
            <p class="text-center no-margin buttons">
                <button class="button" data-js="save">Yes</button>
                <button class="button secondary" data-js="close">Cancel</button>
            </p>
        </div>
    </div>
</form>