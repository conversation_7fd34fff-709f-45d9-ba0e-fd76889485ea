<div class="c-pcc-component">
    <div class="c-pccc-loading" data-js="loader"></div>
    <div class="medium-12 columns">
        <div class="row expanded">
            <div class="medium-12 columns no-pad">
                <div id="cancelSchedule"></div>
            </div>
        </div>
        <div class="row expanded">
            <div id="scheduleList" class="small-12 medium-12 large-8 columns no-pad-left">
                <div class="medium-12 columns">
                    <div class="row expanded">
                        <div class="medium-6 columns no-pad">
                            <h5 style="margin:0;color: #333333;margin-bottom:.5rem;">Evaluation</h5>
                        </div>
                        <div id="scheduleEvaluationButton" class="medium-6 columns no-pad"></div>
                    </div>
                    <div class="row expanded" id="evaluationSchedule">
                    </div>
                </div>
                <hr>
                <div class="medium-12 columns">
                    <div class="row expanded">
                        <div class="medium-6 columns no-pad">
                            <h5 style="margin:.5rem 0;color: #333333;">Installation</h5>
                        </div>
                        <div id="scheduleInstallationButton" class="medium-6 columns no-pad-right"></div>
                    </div>
                    <div class="row expanded" id="installationSchedule">
                    </div>
                </div>
            </div>
        </div>
        <input id="eventType" type="hidden" name="eventType" value="" />
        <div class="tiny reveal" id="emailWontSendModalSchedule" data-reveal data-close-on-esc="false" data-close-on-click="false">
            <h3 class="text-center">Email Will Not Send</h3>
            <div class="row">
                <div class="medium-12 columns">
                    <p class="text-center no-margin">
                    </p>
                </div>
                <div class="medium-12 columns text-center">
                    <br/>
                    <button class="button" id="continueWithoutEmailSchedule">Yes</button>
                    <button class="button" id="dontContinueWithoutEmail">Cancel</button>
                </div>
            </div>
        </div>
        <div class="row expanded" style="margin-right:-0.9375rem; margin-left:-0.9375rem">
            <div id="scheduleCalendar" class="medium-12 columns no-pad" style="display:none;position:relative;">
                <div class="calendarWrapper">
                    <div id="filterGroup" class="left">
                        <div class="header">
                                    <span class="title">Filter
                                        <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="8" title="Filter: Select the users that you want displayed on the calendar. Order: Drag the users to set their display order.">
                                            <img src="{{info}}" />
                                        </span>
                                    </span>
                            <div class="group">
                                <button id="filter-clear" class="btn icon-cross"></button>
                                <button id="filter-apply" class="btn icon-checkmark"></button>
                            </div>
                        </div>
                        <div class="list"></div>
                    </div>
                    <div id="calendarGroup">
                        <div class="dashboard-filter-bar table">
                            <div>
                                <div class="filter">
                                    <button id="filter" class="btn icon-filter"></button>
                                </div>
                                <div class="todayButton">
                                    <a id="today" class="button today" style="">Today</a>
                                </div>
                                <div class="daySelector">
                                    <div>
                                        <div id="previous" class="arrow-left"></div>
                                    </div>
                                    <div style="width: 60%">
                                        <span id="calendarTitle" class="calendarTitle"></span>
                                    </div>
                                    <div>
                                        <div id="next" class="arrow-right"></div>
                                    </div>
                                </div>
                                <div class="button-group viewSelector">
                                    <a id="dailyView" class="button view view-left">Daily</a>
                                    <a id="resourceWeekView" class="button view view-center">Weekly</a>
                                    <a id="monthlyView" class="button view view-right active">Monthly</a>
                                </div>
                            </div>
                        </div>
                        <div class="row expanded">
                            <div class="medium-12 columns no-pad">
                                <div id="calendar"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="addEvent" class="reveal small" data-reveal data-close-on-esc="false" data-close-on-click="false">
                <h2 id="modalTitle"></h2>
                <p class="lead name"></p>
                <div class="row">
                    <div class="medium-12 columns">
                        <label class="scheduledTitle"></label>
                    </div>
                    <div class="medium-5 columns" id="scheduleModalDD">
                        <select class="evaluationData" name="salesperson">
                            <option value="">--</option>
                        </select>
                    </div>
                    <div class="medium-7 columns"></div>
                </div>
                <div class="row">
                    <div class="medium-12 columns">
                        <label>Start Time</label>
                    </div>
                    <div class="medium-4 columns">
                        <input id="scheduledStartDate" class="evaluationData datepickerFrom date" type="text" name="scheduledStartDate" />
                        <small class="form-error" id="startDateErr">Please select a scheduled start date</small>
                    </div>
                    <div class="medium-4 columns">
                        <input id="scheduledStartTime" class="evaluationData timepicker" type="text" name="scheduledStartTime" />
                        <small class="form-error" id="startTimeErr">Please enter a scheduled start time</small>
                        <div id="scheduledStartTimeHidden" class="is-hidden"></div>
                    </div>
                    <div class="medium-3 columns">
                        <input type="checkbox" name="scheduledStartTimeAllDay" value="1" /> All Day
                    </div>
                </div>
                <div class="row">
                    <div class="medium-12 columns">
                        <label>End Time</label>
                    </div>
                    <div class="medium-4 columns">
                        <input id="scheduledEndDate" class="evaluationData datepickerTo date" type="text" name="scheduledEndDate" />
                        <small class="form-error" id="endDateErr">Please select an end date</small>
                    </div>
                    <div class="medium-4 columns">
                        <input id="scheduledEndTime" class="evaluationData timepicker" type="text" name="scheduledEndTime" />
                        <small id="endTimeErr" class="form-error">Please enter an end time</small>

                        <div id="scheduledEndTimeHidden" class="is-hidden"></div>
                    </div>
                </div>
                <div class="row">
                    <div class="medium-12 columns">
                        <label>Description <small><i>(Internal)</i></small></label>
                    </div>
                    <div class="medium-8 columns">
                        <textarea class="internalNote" rows="3" name="scheduleDescription"></textarea>
                    </div>
                </div>
                <div class="row">
                    <div class="medium-12 columns">
                        <label>
                            <input type="checkbox" name="notifyCustomer" value="1" />
                            Notify Customer of Appointment <span data-tooltip aria-haspopup="true" class="has-tip top" data-disable-hover="false" tabindex="1" title="Customer will receive an appointment email (See Company Profile > Email Section to customize email content)."><img src="{{info}}" /></span>
                        </label>
                    </div>
                </div>
                <div class="row">
                    <div class="medium-12 columns">
                        <input type="hidden" name="tempID" value=""/>
                        <input type="hidden" name="projectScheduleID" value=""/>
                    </div>
                </div>
                <div class="row">
                    <div class="medium-12 columns" id="okButtonDiv">
                        <p><a id="confirmAppointmentYes" class="button top">Ok</a> <a id="confirmAppointmentNoSelect" class="button secondary select top">Cancel</a> <a id="confirmAppointmentNoDrag" class="button secondary drag top">Cancel</a></p>
                    </div>
                </div>
            </div>
            <div id="editEvent" class="reveal xsmall" data-reveal data-close-on-esc="false" data-close-on-click="false">
                <div class="row">
                    <div class="medium-12 columns">
                        <h2 id="modalTitle"></h2>
                        <p class="lead name"></p>
                    </div>
                </div>
                <div class="row">
                    <div class="medium-12 columns">
                        <label>Description <small><i>(Internal)</i></small></label>
                    </div>
                    <div class="medium-12 columns">
                        <textarea class="internalNote" rows="3" name="scheduleDescription"></textarea>
                    </div>
                </div>
                <div class="row">
                    <div class="medium-12 columns">
                        <input type="hidden" name="projectScheduleID" value=""/>
                    </div>
                </div>
                <div class="row">
                    <div class="medium-12 columns" id="okButtonDiv">
                        <p><a id="editAppointment" class="button top">Ok</a> <a id="editAppointmentClose" class="button secondary select top">Cancel</a></p>
                    </div>
                </div>
            </div>
            <div id="viewEvent" class="reveal" data-reveal></div>
            <div id="cancelEvent" class="reveal tiny" data-reveal data-close-on-esc="false" data-close-on-click="false">
                <h2 class="text-center" id="modalTitle">Cancel</h2>
                <p class="text-center">Are you sure you want to cancel this event?</p>
                <input type="hidden" name="tempID" value="" />
                <p class="text-center"><a id="cancelConfirmYes" class="button">Yes</a> <a id="cancelConfirmNo" class="button secondary">No</a></p>
            </div>
        </div>
    </div>
</div>