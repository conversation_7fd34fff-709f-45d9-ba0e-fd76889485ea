@use '~@cac-sass/config/global' with (
    $legacy: false
);
@use '~@cac-sass/base';
@use '~@cac-sass/app/button/text';
@use '~@cas-modal-sass/modal';

@use 'modules/module';
@use 'modules/complete';
@use '~@cas-layout-sass/layout';

body {
    background-color: base.$color-white-default;
}

.m-training {
    .c-t-inner {
        width: base.unit-rem-calc(928px);
        margin: 0 auto;
        padding: base.unit-rem-calc(40px) 0;
        @include base.respond-to('<960px') {
            width: 100%;
            padding: base.unit-rem-calc(48px) base.unit-rem-calc(16px);
        }
    }
        .c-ti-training {
            padding: 0 base.unit-rem-calc(8px);
            border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
        }
            .c-tit-title {
                display: flex;
                gap: base.unit-rem-calc(16px);
                align-items: center;
                margin: 0;
                padding-bottom: base.unit-rem-calc(16px);
            }
                .c-titt-text {
                    flex: 1;
                    margin: 0;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    @include base.respond-to('<small') {
                        font-size: base.unit-rem-calc(20px);
                    }
                }
                .c-titt-button {
                    @include base.button-text-primary;
                }
            .c-tit-video {
                @include base.responsive-embed('widescreen');
                border-radius: base.unit-rem-calc(8px);
                // makes it so that border-radius works on mobile devices
                isolation: isolate;
            }
            .c-tit-sections {
                margin-top: base.unit-rem-calc(24px);
            }
        .c-ti-complete {
            padding: base.unit-rem-calc(24px) base.unit-rem-calc(8px);
            > p {
                margin-top: base.unit-rem-calc(8px);
            }
        }
            .c-tic-wrapper {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                grid-template-rows: 1fr;
                padding: base.unit-rem-calc(40px) 0;
                gap: base.unit-rem-calc(24px);
                @include base.respond-to('<932px') {
                    grid-template-columns: 1fr;
                    grid-template-rows: repeat(3, 1fr);
                }
                &.t-1-column {
                    grid-template-columns: 1fr;
                }
            }
            .c-ticw-button {
                display: flex;
                flex: 1;
                align-items: center;
                padding: base.unit-rem-calc(16px);
                gap: base.unit-rem-calc(16px);
                border: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                border-radius: base.unit-rem-calc(8px);
                background-color: base.$color-white-default;
                box-shadow: base.$elevation-level-1;
                transition: all 300ms ease-out;
                > svg {
                    color: base.$color-primary-default;
                    @include base.svg-icon('default-18');
                }
                &:hover {
                    border-color: base.$color-primary-light-1;
                    color: base.$color-primary-default;
                    background-color: base.$color-primary-light-4;
                    box-shadow: base.$elevation-level-3;
                    transition: all 300ms ease-out;
                }
                &.t-disabled {
                    cursor: not-allowed;
                    border-color: base.$color-grey-light-4;
                    box-shadow: none;
                    .c-ticwb-title {
                        color: base.$color-green-light-1;
                    }
                    .c-ticwb-circle {
                        background-color: base.$color-green-light-1;
                    }
                    > svg {
                        color: base.$color-green-light-1;
                    }
                    &:hover {
                        background-color: transparent;
                    }
                }
            }
                .c-ticwb-circle {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: base.unit-rem-calc(32px);
                    height: base.unit-rem-calc(32px);
                    color: base.$color-white-default;
                    background-color: base.$color-primary-default;
                    border-radius: base.unit-rem-calc(32px);
                    padding-bottom: base.unit-rem-calc(2px);
                    > svg {
                        color: base.$color-white-default;
                        @include base.svg-icon('default-18');
                    }
                }
                .c-ticwb-title {
                    flex: 1;
                }
            .c-tis-section {
                margin-top: base.unit-rem-calc(24px);
                &:first-child {
                    margin-top: 0;
                }
                @include base.respond-to('<small') {
                    margin-top: base.unit-rem-calc(40px);
                }
            }
    .c-t-footer {
        width: 100%;
        border-top: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
    }
        .c-tf-inner {
            display: flex;
            width: base.unit-rem-calc(650px);
            padding: base.unit-rem-calc(64px) 0;
            margin: 0 auto;
            @include base.respond-to('<700px') {
                width: 100%;
                padding: base.unit-rem-calc(40px) base.unit-rem-calc(24px);
            }
            @include base.respond-to('<small') {
                flex-direction: column;
            }
        }
            .c-tfi-info {
                flex: 1;
                margin-right: base.unit-rem-calc(64px);
                &:nth-child(2n+2) {
                    margin-right: 0;
                }
                @include base.respond-to('<700px') {
                    margin-right: base.unit-rem-calc(40px);
                }
                @include base.respond-to('<small') {
                    margin-top: base.unit-rem-calc(40px);
                    &:first-child {
                        margin-top: 0;
                    }
                }
            }
                .c-tfii-title {
                    display: flex;
                    align-items: center;
                    color: base.$color-grey-dark-4;
                }
                    .c-tfiit-icon {
                        display: block;
                        @include base.svg-icon('default-18');
                    }
                    .c-tfiit-text {
                        margin: 0 0 0 base.unit-rem-calc(8px);
                    }
                .c-tfii-content {
                    margin-top: base.unit-rem-calc(12px);
                    color: base.$color-grey-dark-4;
                }
    @import 'modules/section';
}
