@use '~@cac-sass/base';

.m-section {
    border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
    &:last-child {
        border-bottom: none;
    }
    .c-s-title {
        margin: 0;
        @include base.respond-to('<small') {
            font-size: base.unit-rem-calc(20px);
        }
    }
    .c-s-description {
        margin-top: base.unit-rem-calc(8px);
        > p {
            @include base.typo-paragraph;
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
    .c-s-modules {
        display: flex;
        flex-wrap: wrap;
        gap: base.unit-rem-calc(24px);
        margin-top: base.unit-rem-calc(24px);
        margin-bottom: base.unit-rem-calc(48px);
        @include base.respond-to('<small') {
            flex-direction: column;
        }
    }
        .c-sm-module {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex: 1;
            padding: base.unit-rem-calc(40px) base.unit-rem-calc(8px);
            border: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
            border-radius: base.unit-rem-calc(8px);
            box-shadow: base.$elevation-level-1;
            color: base.$color-grey-default;
            transition: all 300ms ease-out;
            @include base.respond-to('hover') {
                &:hover {
                    box-shadow: base.$elevation-level-3;
                    background-color: base.$color-primary-light-4;
                    border-color: base.$color-primary-default;
                    color: base.$color-primary-default;
                    transition: all 300ms ease-out;
                    .c-smm-icon,
                    .c-smm-text {
                        color: base.$color-primary-default;
                        transition: all 300ms ease-out;
                    }
                }
            }
            @include base.respond-to('<small') {
                width: 100%;
                margin-right: 0;
            }
            &:nth-child(3n+3) {
                margin-right: 0;
            }
            &.t-completed {
                box-shadow: none;
                border-color: base.$color-green-light-1;
                .c-smm-checkmark{
                    border-color: base.$color-green-light-1;
                    background-color: base.$color-green-light-4;
                }
                .c-smmc-icon {
                    color: base.$color-green-light-1;
                }
                .c-smm-icon {
                    color: base.$color-green-default;
                }
                .c-smm-text {
                    color: base.$color-green-default;
                }
                &:hover {
                    background-color: base.$color-white-default;
                }
            }
            &.t-hidden {
                display: none;
            }
        }
            .c-smm-checkmark{
                display: flex;
                position: absolute;
                justify-content: center;
                align-items: center;
                top: base.unit-rem-calc(16px);
                right: base.unit-rem-calc(16px);
                width: base.unit-rem-calc(32px);
                height: base.unit-rem-calc(32px);
                background-color: base.$color-white-default;
                border: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
                border-radius: base.unit-rem-calc(64px);
                .c-smmc-icon {
                    border-radius: base.unit-rem-calc(64px);
                    @include base.svg-icon('default-18');
                    color: base.$color-grey-light-4;
                    z-index: 2;
                    &.t-show {
                        display: block;
                    }
                }
            }

            .c-smm-icon {
                @include base.svg-icon('default-64');
                margin-bottom: base.unit-rem-calc(8px);
                color: base.$color-grey-light-1;
                transition: all 300ms ease-out;
            }
            .c-smm-text {
                margin: 0;
                color: base.$color-grey-dark-1;
                text-align: center;
                @include base.respond-to('<medium') {
                    padding: 0 base.unit-rem-calc(8px);
                    max-width: 100%;
                    white-space: nowrap;
                    text-align: center;
                    text-overflow: ellipsis;
                    overflow: hidden;
                }
                @include base.respond-to('>=small', '<medium') {
                    font-size: base.unit-rem-calc(14px);
                }
            }
    .c-s-view-button {
        display: flex;
        justify-content: center;
        padding: base.unit-rem-calc(40px) 0 0;
    }
        .c-svb-button {
            @include base.button-text-secondary($rounded: true);
        }
}