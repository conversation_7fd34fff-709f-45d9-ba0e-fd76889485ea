@use '../color';
@use '../typography';

// font
$font-family: typography.$font-family-text;
$font-weight: normal;
$font-size: typography.$font-size-paragraph;
$line-height: typography.$line-height-paragraph;

// default
$default-color: color.$primary-default !default;
$default-bg-color: transparent !default;
$default-border-color: transparent !default;
$default-box-shadow: none;

// hover
$hover-color: color.$primary-light-1 !default;
$hover-bg-color: $default-bg-color !default;
$hover-border-color: $default-border-color !default;
$hover-box-shadow: none;

// active
$active-color: color.$primary-dark-1 !default;
$active-bg-color: $hover-bg-color !default;
$active-border-color: $hover-border-color !default;
$active-box-shadow: none;

// disabled
$disabled-color: color.$grey-light-4 !default;
$disabled-bg-color: $default-bg-color !default;
$disabled-border-color: $default-border-color !default;
$disabled-box-shadow: none;
