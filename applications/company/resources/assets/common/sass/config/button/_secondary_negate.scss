@use '../color';
@use '../elevation';
@use '../typography';

// font
$font-family: typography.$font-family-header;
$font-weight: 600;

// default
$default-color: color.$grey-dark-1 !default;
$default-bg-color: color.$white-default !default;
$default-border-color: color.$grey-light-1 !default;
$default-box-shadow: elevation.$secondary-base;

// hover
$hover-color: color.$grey-dark-2 !default;
$hover-bg-color: $default-bg-color !default;
$hover-border-color: color.$grey-dark-1 !default;
$hover-box-shadow: elevation.$secondary-hover;

// active
$active-color: color.$grey-dark-3 !default;
$active-bg-color: $hover-bg-color !default;
$active-border-color: color.$grey-dark-3 !default;
$active-box-shadow: none;

// disabled
$disabled-color: color.$grey-light-4 !default;
$disabled-bg-color: $default-bg-color !default;
$disabled-border-color: $disabled-color !default;
$disabled-box-shadow: none;
