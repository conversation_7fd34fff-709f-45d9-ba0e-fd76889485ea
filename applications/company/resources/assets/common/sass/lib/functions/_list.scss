@forward 'sass:list';

@use 'sass:list';

///
/// Slice `$list` between `$start` and `$end` indexes
///
/// @param {List} $list - List to -slice
/// @param {Number} $start [1] - Start index
/// @param {Number} $end [length($list)] - End index
///
/// @return {List} -sliced list
///
@function slice($list, $start: 1, $end: list.length($list)) {
    @if list.length($list) < 1 or $start > $end {
        @return ();
    }

    $result: ();

    @for $i from $start through $end {
        $result: list.append($result, nth($list, $i));
    }

    @return $result;
}
