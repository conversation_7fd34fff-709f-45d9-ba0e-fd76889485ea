@use 'functions/unit';
@use 'functions/list';
@use 'functions/string';
@use 'functions/math';
@use 'sass:map';

// Heavily modified version of https://eduardoboucas.github.io/include-media

///
/// Creates a list of global breakpoints
///
/// @example scss - Creates a single breakpoint with the label `phone`
///  $breakpoints: ('phone': 320px);
///
$breakpoints: () !default;

///
/// Creates a list of static expressions or media types
///
/// @example scss - Creates a single media type (screen)
///  $media-expressions: ('screen': 'screen');
///
/// @example scss - Creates a static expression with logical disjunction (OR operator)
///  $media-expressions: (
///    'retina2x': '(-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)'
///  );
///
$media-expressions: (
    'screen': 'screen',
    'print': 'print',
    'handheld': 'handheld',
    'landscape': '(orientation: landscape)',
    'portrait': '(orientation: portrait)',
    'retina2x': '(-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi), (min-resolution: 2dppx)',
    'retina3x': '(-webkit-min-device-pixel-ratio: 3), (min-resolution: 350dpi), (min-resolution: 3dppx)',
    'no-hover': '(hover:none), (hover:on-demand)'
) !default;

///
/// Default embed ratios
///
$embed-ratios: (
    default: 4 by 3,
    widescreen: 16 by 9,
) !default;

///
/// Get operator of an expression
///
/// @param {String} $expression - Expression to extract operator from
///
/// @return {String} - Any of `>=`, `>`, `<=`, `<`, `≥`, `≤`
///
@function -get-expression-operator($expression) {
    @each $operator in ('>=', '>', '<=', '<', '≥', '≤') {
        @if string.index($expression, $operator) {
            @return $operator;
        }
    }
    @error 'No operator found in `#{$expression}`.';
}


///
/// Get dimension of an expression, based on a found operator
///
/// @param {String} $expression - Expression to extract dimension from
/// @param {String} $operator - Operator from `$expression`
///
/// @return {String} - `width` or `height` (or potentially anything else)
///
@function -get-expression-dimension($expression, $operator) {
    $operator-index: string.index($expression, $operator);
    $parsed-dimension: string.slice($expression, 0, $operator-index - 1);
    $dimension: 'width';

    @if string.length($parsed-dimension) > 0 {
        $dimension: $parsed-dimension;
    }

    @return $dimension;
}


///
/// Get dimension prefix based on an operator
///
/// @param {String} $operator - Operator
///
/// @return {String} - `min` or `max`
///
@function -get-expression-prefix($operator) {
    @return if(list.index(('<', '<=', '≤'), $operator), 'max', 'min');
}


///
/// Get value of an expression, based on a found operator
///
/// @param {String} $expression - Expression to extract value from
/// @param {String} $operator - Operator from `$expression`
///
/// @return {Number} - A numeric value
///
@function -get-expression-value($expression, $operator) {
    $operator-index: string.index($expression, $operator);
    $value: string.slice($expression, $operator-index + string.length($operator));

    @if map.has-key($breakpoints, $value) {
        $value: map.get($breakpoints, $value);
    } @else {
        $value: string.to-number($value);
    }
    @if $operator == '>' {
        $value: $value + 1;
    } @else if $operator == '<' {
        $value: $value - 1;
    }
    @return unit.rem-calc($value);
}


///
/// Parse an expression to return a valid media-query expression
///
/// @param {String} $expression - Expression to parse
///
/// @return {String} - Valid media query
///
@function -expression-to-mq($expression) {
    // If it is part of $media-expressions, it has no operator
    // then there is no need to go any further, just return the value
    @if map.has-key($media-expressions, $expression) {
        @return map.get($media-expressions, $expression);
    }

    $operator: -get-expression-operator($expression);
    $dimension: -get-expression-dimension($expression, $operator);
    $prefix: -get-expression-prefix($operator);
    $value: -get-expression-value($expression, $operator);

    @return '(#{$prefix}-#{$dimension}: #{$value})';
}

@function get-breakpoint($name) {
    @if not map.has-key($breakpoints, $name) {
        @error 'Unable to find breakpoint';
    }
    @return map.get($breakpoints, $name);
}

///
/// This mixin aims at redefining the configuration just for the scope of
/// the call. It is helpful when having a component needing an extended
/// configuration such as custom breakpoints (referred to as tweakpoints)
/// for instance.
///
/// <AUTHOR> Giraudel
///
/// @param {Map} $tweakpoints [()] - Map of tweakpoints to be merged with `$breakpoints`
/// @param {Map} $tweak-media-expressions [()] - Map of tweaked media expressions to be merged with `$media-expression`
///
/// @example scss - Extend the global breakpoints with a tweakpoint
///  @include media-context(('custom': 678px)) {
///    .foo {
///      @include media('>phone', '<=custom') {
///       // ...
///      }
///    }
///  }
///
/// @example scss - Extend the global media expressions with a custom one
///  @include media-context($tweak-media-expressions: ('all': 'all')) {
///    .foo {
///      @include media('all', '>phone') {
///       // ...
///      }
///    }
///  }
///
/// @example scss - Extend both configuration maps
///  @include media-context(('custom': 678px), ('all': 'all')) {
///    .foo {
///      @include media('all', '>phone', '<=custom') {
///       // ...
///      }
///    }
///  }
///
@mixin respond-to-context($tweakpoints: (), $tweak-media-expressions: ()) {
    // Save global configuration
    $global-breakpoints: $breakpoints;
    $global-media-expressions: $media-expressions;

    // Update global configuration
    $breakpoints: map.merge($breakpoints, $tweakpoints) !global;
    $media-expressions: map.merge($media-expressions, $tweak-media-expressions) !global;

    @content;

    // Restore global configuration
    $breakpoints: $global-breakpoints !global;
    $media-expressions: $global-media-expressions !global;
}

///
/// Generates a media query based on a list of conditions
///
/// @param {Arglist}   $conditions  - Media query conditions
///
/// @example scss - With a single set breakpoint
///  @include respond-to('>phone', 'landspace') { }
///
/// @example scss - With two set breakpoints
///  @include respond-to('>phone', '<=tablet') { }
///
/// @example scss - With custom values
///  @include respond-to('>=358px', '<850px') { }
///
/// @example scss - With set breakpoints with custom values
///  @include respond-to('>desktop', '<=1350px') { }
///
/// @example scss - With a static expression
///  @include respond-to('retina2x') { }
///
/// @example scss - Mixing everything
///  @include respond-to('>=350px', '<tablet', 'retina3x') { }
///
@mixin respond-to($conditions...) {
    @if list.length($conditions) > 0 {
        @media #{unquote(-expression-to-mq(list.nth($conditions, 1)))} {
            $conditions: list.slice($conditions, 2);
            @include respond-to($conditions...) {
                @content;
            }
        }
    } @else {
        @content;
    }
}

///
/// Creates a responsive embed container
///
/// @param {String|List} $ratio [default] - Ratio of the container. Can be a key from the `$embed-ratios` map or a list formatted as `x by y`.
///
@mixin responsive-embed($ratio: default) {
    @if type-of($ratio) == 'string' {
        $ratio: map.get($embed-ratios, $ratio);
    }
    position: relative;
    height: 0;
    padding-bottom: math.ratio-to-percentage($ratio);
    overflow: hidden;

    iframe,
    object,
    embed,
    video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
}
