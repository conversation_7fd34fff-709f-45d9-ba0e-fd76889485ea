@use '../config/typography';
@use 'functions/unit';

@mixin nav($size, $line-height: null) {
    font-family: typography.$font-family-nav;
    font-size: unit.rem-calc($size);
    font-weight: 600;
    @if $line-height != null {
        line-height: $line-height;
    }
}

@mixin header($size, $line-height: null) {
    font-family: typography.$font-family-header;
    font-size: unit.rem-calc($size);
    font-weight: 600;
    @if $line-height != null {
        line-height: $line-height;
    }
}

@mixin text($size) {
    font-family: typography.$font-family-text;
    font-size: unit.rem-calc($size);
}
@mixin text-medium($size) {
    @include text($size);
    font-weight: 500;
}

@mixin paragraph {
    font-size: typography.$font-size-paragraph;
    line-height: 1.4;
}
@mixin paragraph-medium {
    font-weight: 500;
    font-size: typography.$font-size-paragraph;
    line-height: 1.4;
}
@mixin paragraph-small {
    font-size: typography.$font-size-paragraph-small;
    line-height: 1.4;
}
@mixin paragraph-small-medium {
    font-weight: 500;
    font-size: typography.$font-size-paragraph-small;
    line-height: 1.4;
}