'use strict';

import $ from 'jquery';

import {on<PERSON><PERSON><PERSON><PERSON><PERSON>, jsSelector, findParents} from '@ca-package/dom';

/**
 * @memberof module:Notification
 */
export class Controller {
    /**
     * Constructor
     */
    constructor() {
        this.elem = {};
        /**
         * @protected
         */
        this.state = {
            rendered: false,
            booted: false,
            messages: new Map(),
            pending: []
        };
    };

    /**
     * Get message by id
     *
     * @param {number} idx
     * @returns {module:Notification.Message} - Message instance
     */
    getMessage(idx) {
        return this.state.messages.get(idx);
    };

    /**
     * Render message
     *
     * @param {module:Notification.Message} message - Message instance
     * @param {boolean} [content_only=false]
     * @returns {(string|void)}
     */
    renderMessage(message, content_only = false) {
        let content = message.render();
        if (content_only) {
            return content;
        }
        this.elem.root.append(content);
    };

    /**
     * Add message
     *
     * @param {module:Notification.Message} message - Message instance
     * @param {boolean} [immediate=true] - Determines if message is displayed now
     */
    addMessage(message, immediate = true) {
        if (!immediate) {
            this.state.pending.push(message);
            return;
        }
        let count = this.state.messages.size;
        this.state.messages.set(message.id, message);
        if (this.isBooted()) {
            this.renderMessage(message);
            message.boot(this);
            if (count === 0) {
                this.elem.root.addClass('t-has-message');
            }
        }
    };

    /**
     * Handle message action
     *
     * @param {number} id - Message id
     * @param {number} action - Action id
     */
    handleMessageAction(id, action) {
        let message = this.getMessage(id);
        if (message === undefined) {
            return;
        }
        message.handleAction(action);
    };

    /**
     * Delete message by id
     *
     * @param {number} id
     */
    deleteMessage(id) {
        let message = this.getMessage(id);
        message.delete();
    };

    /**
     * Clear message from internal cache
     *
     * @param {number} id
     */
    clearMessage(id) {
        this.state.messages.delete(id);
        if (this.isBooted() && this.state.messages.size === 0) {
            this.elem.root.removeClass('t-has-message');
        }
    };

    /**
     * Delete all messages
     */
    deleteAllMessages() {
        if (this.state.messages.size === 0) {
            return;
        }
        for (let message of this.state.messages.values()) {
            message.delete();
        }
    };

    /**
     * Render any pending messages
     */
    renderPendingMessages() {
        if (this.state.pending.length === 0) {
            return;
        }
        for (let message of this.state.pending) {
            this.addMessage(message);
        }
        this.state.pending = [];
    };

    /**
     * Determines if controller is booted
     *
     * @returns {boolean}
     */
    isBooted() {
        return this.state.booted;
    };

    /**
     * Bind events to root element
     *
     * Watch for message actions and pass them to the message to handling.
     */
    bindEvents() {
        const that = this;
        onClickWatcher(this.elem.root, jsSelector('action'), function () {
            let $this = $(this);
            that.handleMessageAction(parseInt(findParents($this, jsSelector('message')).data('id')), parseInt($this.data('id')));
        }, true);
    };

    /**
     * Boot any defined messages
     */
    bootMessages() {
        if (this.state.messages.size === 0) {
            return;
        }
        for (let message of this.state.messages.values()) {
            message.boot(this);
        }
    };

    /**
     * Boot controller
     */
    boot() {
        if (!this.isRendered()) {
            throw new Error('Cannot boot that which has not been rendered');
        }
    };

    /**
     * Determines if controller is rendered
     *
     * @returns {boolean}
     */
    isRendered() {
        return this.state.rendered;
    };

    /**
     * Render all stored messages
     *
     * @returns {string}
     */
    renderMessages() {
        let messages = '';
        for (let message of this.state.messages.values()) {
            messages += this.renderMessage(message, true);
        }
        return messages;
    };

    /**
     * Render controller
     *
     * @returns {string}
     */
    render() {
        return '';
    };
}
