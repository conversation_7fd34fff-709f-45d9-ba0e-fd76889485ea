'use strict';

import $ from 'jquery';

import {selector as domSelector} from './selector';

/**
 * Find select in parent
 *
 * If no parent is defined, document is used
 *
 * @param {module:DOM/Selector.Selector|module:DOM/Selector.JsSelector|string} selector
 * @param {jQuery|HTMLElement|string} parent_elem
 * @returns {jQuery}
 */
export function find(selector, parent_elem = null) {
    return $(parent_elem ?? window.document).find(domSelector(selector).toString());
}

/**
 * Find child in elem by selector
 *
 * @param {jQuery|HTMLElement|string} elem
 * @param {module:DOM/Selector.Selector|module:DOM/Selector.JsSelector|string} selector
 * @returns {jQuery}
 */
export function findChild(elem, selector) {
    return $(elem).find(domSelector(selector).toString());
}

/**
 * Find first child in elem by selector
 *
 * @param {jQuery|HTMLElement|string} elem
 * @param {module:DOM/Selector.Selector|module:DOM/Selector.JsSelector|string} selector
 * @returns {jQuery}
 */
export function findFirstChild(elem, selector) {
    return $(elem).find(`${domSelector(selector)}:first`);
}

/**
 * Find children in elem by selector
 *
 * @param {jQuery|HTMLElement|string} elem
 * @param {module:DOM/Selector.Selector|module:DOM/Selector.JsSelector|string} selector
 * @returns {jQuery}
 */
export function findChildren(elem, selector) {
    return $(elem).children(domSelector(selector).toString());
}

/**
 * Find parents of elem by selector
 *
 * @param {jQuery|HTMLElement|string} elem
 * @param {module:DOM/Selector.Selector|module:DOM/Selector.JsSelector|string} selector
 * @returns {jQuery}
 */
export function findParents(elem, selector) {
    return $(elem).parents(domSelector(selector).toString());
}

/**
 * Find closest element by selector
 *
 * @param {jQuery|HTMLElement|string} elem
 * @param {module:DOM/Selector.Selector|module:DOM/Selector.JsSelector|string} selector
 * @returns {jQuery}
 */
export function findClosest(elem, selector) {
    return $(elem).closest(domSelector(selector).toString());
}
