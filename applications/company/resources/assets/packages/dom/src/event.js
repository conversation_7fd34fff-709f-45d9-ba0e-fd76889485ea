'use strict';

import $ from 'jquery';

import {selector as domSelector} from './selector';

/**
 * Create namespaced event string for use with jQuery
 *
 * @param {string|array<string>} events
 * @param {object} config
 * @param {string|undefined} config.namespace
 * @returns {string}
 */
function namespaceEvents(events, config) {
    if (typeof events === 'string') {
        events = [events];
    }
    let namespace = '.ca';
    if (typeof config.namespace !== 'undefined') {
        namespace += '.' + config.namespace.trim('.');
    }
    for (let key in events) {
        events[key] += namespace;
    }
    return events.join(' ');
}

/**
 * Prevent default closure wrapper
 *
 * If enabled is true, passed closure is wrapped with function which calls preventDefault on event.
 *
 * @param {function} closure
 * @param {boolean} enabled
 * @returns {function}
 */
function preventDefault(closure, enabled) {
    if (!enabled) {
        return closure;
    }
    return function (e) {
        e.preventDefault();
        closure.call(this, e);
        return false;
    };
}

/**
 * Assign closure to events on element
 *
 * @param {jQuery|HTMLElement|string} elem
 * @param {string|array} events
 * @param {function} closure
 * @param {object} config
 * @returns {jQuery}
 */
export function onEvent(elem, events, closure, config = {}) {
    return $(elem).on(namespaceEvents(events, config), closure);
}

/**
 * Watch for bubbled events on element for specified selector
 *
 * @param {jQuery|HTMLElement|string} elem
 * @param {string|array} events
 * @param {module:DOM/Selector.Selector|module:DOM/Selector.JsSelector|string} selector
 * @param {function} closure
 * @param {object} config
 * @returns {jQuery}
 */
export function onEventWatcher(elem, events, selector, closure, config = {}) {
    return $(elem).on(namespaceEvents(events, config), domSelector(selector).toString(), closure);
}

/**
 * Destroy events bound to elem
 *
 * @param {jQuery|HTMLElement|string|array<jQuery|HTMLElement|string>} elem
 * @param {string|array} events
 * @param {object} config
 */
export function onEventDestroy(elem, events, config = {}) {
    if (!Array.isArray(elem)) {
        elem = [elem];
    }
    elem.forEach(elem => $(elem).off(namespaceEvents(events, config)));
}

/**
 * Bind closure to click event of elem
 *
 * @param {jQuery|HTMLElement|string} elem
 * @param {function} closure
 * @param {boolean} [prevent_default=false]
 * @returns {jQuery}
 */
export function onClick(elem, closure, prevent_default = false) {
    return onEvent(elem, ['click'], preventDefault(closure, prevent_default));
}

/**
 * Watch for bubbled click events on element for specified selector
 *
 * @param {jQuery|HTMLElement|string} elem
 * @param {module:DOM/Selector.Selector|module:DOM/Selector.JsSelector|string} selector
 * @param {function} closure
 * @param {boolean} [prevent_default=false]
 * @returns {jQuery}
 */
export function onClickWatcher(elem, selector, closure, prevent_default = false) {
    return onEventWatcher(elem, ['click'], selector, preventDefault(closure, prevent_default));
}

/**
 * Destroy click events bound to elem
 *
 * @param {jQuery|HTMLElement|string|array<jQuery|HTMLElement|string>} elem
 */
export function onClickDestroy(elem) {
    return onEventDestroy(elem, ['click']);
}
