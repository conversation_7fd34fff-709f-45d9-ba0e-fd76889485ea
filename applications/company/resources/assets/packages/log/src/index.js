/**
 * @module Log
 */

'use strict';

const Levels = require('./levels');

const LevelNames = new Map([
    [Levels.DEBUG, 'DEBUG'],
    [Levels.INFO, 'INFO'],
    [Levels.NOTICE, 'NOTICE'],
    [Levels.WARNING, 'WARNING'],
    [Levels.ERROR, 'ERROR']
]);

/**
 * @memberof module:Log
 */
class Logger {
    /**
     * Constructor
     *
     * @param {string} name
     * @param {(module:Log.Logger|null)} parent
     */
    constructor(name, parent = null) {
        /**
         * @protected
         */
        this.state = {
            name,
            parent,
            enabled: true,
            loggers: new Map,
            handlers: []
        };
    };

    /**
     * Get all levels
     *
     * @readonly
     *
     * @returns {object}
     */
    static get Level() {
        return Levels;
    };

    /**
     * Get parent logger instance
     *
     * @readonly
     *
     * @returns {module:Log.Logger}
     */
    get parent() {
        return this.state.parent;
    };

    /**
     * Get channel name
     *
     * @returns {string}
     */
    get name() {
        let name = this.state.name;
        if (this.state.parent !== null) {
            name = `${this.state.parent.name}.${name}`;
        }
        return name;
    };

    /**
     * Get logger instance for channel by name
     *
     * @param {string} name
     * @returns {module:Log.Logger}
     */
    channel(name) {
        let logger = this.state.loggers.get(name);
        if (logger === undefined) {
            logger = new Logger(name, this);
            this.state.loggers.set(name, logger);
        }
        return logger;
    };

    /**
     * Push handler onto logger
     *
     * @param {module:Log/Handlers.Base} handler
     */
    pushHandler(handler) {
        if (this.state.parent !== null) {
            throw new Error('Cannot add handler to child logger');
        }
        handler.logger = this;
        this.state.handlers.push(handler);
    };

    /**
     * Add record
     *
     * @param {object} record
     */
    addRecord(record) {
        if (!this.state.enabled) {
            return;
        }
        for (let handler of this.state.handlers) {
            handler.handle(record);
        }
    };

    /**
     * Log message with context for certain level
     *
     * @param {number} level
     * @param {string} message
     * @param {object} context
     */
    log(level, message, context = {}) {
        if (this.parent !== null) {
            this.parent.log(level, message, context);
            return;
        }
        this.addRecord({
            message,
            context,
            level,
            level_name: LevelNames.get(level),
            channel: this.name,
            datetime: new Date().toISOString(),
            extra: {}
        });
    };

    /**
     * Log debug message
     *
     * @param {string} message
     * @param {object} [context={}]
     */
    debug(message, context = {}) {
        this.log(Levels.DEBUG, message, context);
    };

    /**
     * Log info message
     *
     * @param {string} message
     * @param {object} [context={}]
     */
    info(message, context = {}) {
        this.log(Levels.INFO, message, context);
    };

    /**
     * Log notice message
     *
     * @param {string} message
     * @param {object} [context={}]
     */
    notice(message, context = {}) {
        this.log(Levels.NOTICE, message, context);
    };

    /**
     * Log warning message
     *
     * @param {string} message
     * @param {object} [context={}]
     */
    warning(message, context = {}) {
        this.log(Levels.WARNING, message, context);
    };

    /**
     * Log warning message (alias for warning)
     *
     * @param {string} message
     * @param {object} [context={}]
     */
    warn(message, context = {}) {
        this.warning(message, context);
    };

    /**
     * Log error message
     *
     * @param {string} message
     * @param {object} [context={}]
     */
    error(message, context = {}) {
        this.log(Levels.ERROR, message, context);
    };
}

module.exports = Logger;
