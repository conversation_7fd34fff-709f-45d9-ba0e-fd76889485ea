'use strict';

const Base = require('./base');
const Levels = require('../levels');

const ConsoleMethodMap = new Map([
    [Levels.DEBUG, 'debug'],
    [Levels.INFO, 'info'],
    [Levels.NOTICE, 'warn'],
    [Levels.WARNING, 'warn'],
    [Levels.ERROR, 'error']
]);

/**
 * @memberof module:Log/Handlers
 */
class Console extends Base {
    /**
     * Log all found error instances in context to console
     *
     * @param {object} context
     */
    logErrors(context) {
        for (let key of Object.getOwnPropertyNames(context)) {
            let value = context[key];
            if (value instanceof Error) {
                console.error(value);
            } else if (value !== null && typeof value === 'object') {
                this.logErrors(value);
            }
        }
    };

    /**
     * Handle record
     *
     * @param {object} record
     */
    onHandle(record) {
        let method = ConsoleMethodMap.get(record.level);
        console[method](record.message, record.context);
        this.logErrors(record.context);
    };
}

module.exports = Console;
