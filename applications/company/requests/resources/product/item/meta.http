### Get all product meta
GET {{host}}/api/v1/product/item/meta

### Get individual product item meta
GET {{host}}/api/v1/product/item/meta/{{meta_id}}

### Store new product item meta
POST {{host}}/api/v1/product/item/meta
Content-Type: application/json

{
  "item_id": "",
  "name": "",
  "value_type": 1,
  "value": ""
}

### Update product item meta
PUT {{host}}/api/v1/product/item/meta/{{meta_id}}
Content-Type: application/json

{
  "item_id": "",
  "name": "",
  "value_type": 1,
  "value": ""
}

### Partially update product item meta
PATCH {{host}}/api/v1/product/item/meta/{{meta_id}}
Content-Type: application/json

{
  "name": ""
}

### Delete product item meta
DELETE {{host}}/api/v1/product/item/meta/{{meta_id}}
