<?php

use function Core\Functions\env;

return [
    'email' => [
        'enabled' => env('LOG_EMAIL_ENABLED', true),
        'to' => env('ERROR_EMAIL'),
        'from' => '<EMAIL>',
        'subject' => 'System Error'
    ],
    'slack' => [
        'enabled' => env('SLACK_ENABLED', false),
        'webhook_url' => env('SLACK_GENERAL_WEBHOOK_URL'),
        'channel' => env('SLACK_GENERAL_CHANNEL'),
        'username' => env('SLACK_USERNAME', null),
        'use_attachment' => env('SLACK_USE_ATTACHMENT', true),
        'icon_emoji' => env('SLACK_ICON_EMOJI', null),
        'short_attachment' => env('SLACK_SHORT_ATTACHMENT', false),
        'include_context' => env('SLACK_INCLUDE_CONTEXT', true),
        'configs' => [
            'subscription' => [
                'webhook_url' => env('SLACK_SUBSCRIPTION_WEBHOOK_URL'),
                'channel' => env('SLACK_SUBSCRIPTION_CHANNEL')
            ],
            'company' => [
                'username' => 'company',
                'webhook_url' => env('SLACK_COMPANY_WEBHOOK_URL'),
                'channel' => env('SLACK_COMPANY_CHANNEL')
            ],
            'mail' => [
                'username' => 'mail',
                'webhook_url' => env('SLACK_MAIL_WEBHOOK_URL'),
                'channel' => env('SLACK_MAIL_CHANNEL')
            ],
            'text' => [
                'username' => 'text',
                'webhook_url' => env('SLACK_TEXT_WEBHOOK_URL'),
                'channel' => env('SLACK_TEXT_CHANNEL')
            ],
            'support' => [
                'username' => 'support',
                'webhook_url' => env('SLACK_SUPPORT_WEBHOOK_URL'),
                'channel' => env('SLACK_SUPPORT_CHANNEL')
            ]
        ]
    ],
    'file' => 'main.log',
    'sentry' =>  [
        'enabled' => env('SENTRY_ENABLED', false),
        'dsn' => env('SENTRY_DSN', ''),
    ]
];
